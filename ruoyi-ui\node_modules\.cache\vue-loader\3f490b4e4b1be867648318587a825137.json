{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetDialog.vue?vue&type=style&index=0&id=3e7a394a&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetDialog.vue", "mtime": 1756432531741}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750942927475}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750942929511}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovKiDkvb/nlKjlhajlsYDmoLflvI8gKi8NCi5iZXQtZGlhbG9nLWxvYWRpbmcgew0KICB6LWluZGV4OiA5OTk5ICFpbXBvcnRhbnQ7DQp9DQoNCi5iZXQtZGlhbG9nLWxvYWRpbmcgLmVsLWxvYWRpbmctbWFzayB7DQogIHotaW5kZXg6IDk5OTkgIWltcG9ydGFudDsNCn0NCg=="}, {"version": 3, "sources": ["BetDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiwEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "BetDialog.vue", "sourceRoot": "src/views/game/record/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\" width=\"600px\" append-to-body\r\n      :close-on-click-modal=\"false\" class=\"modern-bet-dialog\" style=\"height: 108%;  top: -55px;\">\r\n\r\n      <el-form ref=\"form\" :model=\"formData\" :rules=\"rules\" label-width=\"80px\" class=\"modern-bet-form\">\r\n        <div class=\"info-card\">\r\n          <!-- 福体开关行 -->\r\n          <div class=\"info-row\">\r\n            <div class=\"info-group\">\r\n              <div class=\"info-label\">\r\n                <i class=\"el-icon-s-operation\"></i>\r\n                <span>福体开关</span>\r\n              </div>\r\n              <el-switch v-model=\"futiSwitch\" active-value=\"all\" inactive-value=\"tc\" active-text=\"全部彩种\" inactive-text=\"仅体彩\"\r\n                @change=\"onFutiSwitchChange\"\r\n                size=\"small\"\r\n                class=\"lottery-switch\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 用户信息行 -->\r\n          <div class=\"info-row\">\r\n            <div class=\"info-group\">\r\n              <div class=\"info-label\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>当前用户</span>\r\n              </div>\r\n              <el-input v-model=\"currentUser.name\" disabled placeholder=\"未获取\" class=\"compact-input\" style=\"width: 100px;\" />\r\n            </div>\r\n            <div class=\"info-group\">\r\n              <div class=\"info-label\">\r\n                <i class=\"el-icon-switch-button\"></i>\r\n                <span>切换用户</span>\r\n              </div>\r\n              <el-select v-model=\"selectedUserId\" placeholder=\"请选择用户\" class=\"compact-select\" style=\"width: 120px;\">\r\n                <el-option v-for=\"user in userList\" :key=\"user.userId\" :label=\"user.name\" :value=\"user.userId\" />\r\n              </el-select>\r\n              <el-button\r\n                type=\"primary\"\r\n                :disabled=\"!selectedUserId\"\r\n                @click=\"switchUser\"\r\n                v-if=\"!row\"\r\n                class=\"compact-btn\"\r\n              >切换</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 流水号信息行 -->\r\n          <div class=\"info-row\">\r\n            <div class=\"info-group\">\r\n              <div class=\"info-label\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>流水号</span>\r\n              </div>\r\n              <el-input v-model.number=\"serialNumber\" placeholder=\"流水号\" class=\"compact-input\" style=\"width: 120px;\" />\r\n              <el-button type=\"success\" @click=\"generateNewSerialNumber\" :loading=\"generateLoading\" class=\"compact-btn\">\r\n                <i class=\"el-icon-refresh\"></i>\r\n                生成新流水号\r\n              </el-button>\r\n              <el-button type=\"primary\" @click=\"showFormatDialog\" class=\"compact-btn\">\r\n                <i class=\"el-icon-s-operation\"></i>\r\n                格式转换工具\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"showStatisticsButton\"\r\n                type=\"success\"\r\n                @click=\"showStatistics\"\r\n                class=\"compact-btn\">\r\n                <i class=\"el-icon-data-analysis\"></i>\r\n                下注统计\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 隐藏的期号字段，用于后端传输 -->\r\n          <div style=\"display: none;\">\r\n            <el-input v-model=\"formData.fc3dIssueNumber\" />\r\n            <el-input v-model=\"formData.tcIssueNumber\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"recognition-section\">\r\n          <el-form-item label=\"号码识别\" prop=\"shibie\">\r\n            <div class=\"recognition-wrapper\">\r\n              <div class=\"recognition-header\">\r\n                <i class=\"el-icon-view\"></i>\r\n                <span v-if=\"!isNumberCountExceeded\">智能识别</span>\r\n                <span v-else-if=\"isFirstNumberThreeDigits(formData.shibie)\" style=\"color: #67c23a;\">首组为三位数，不限制组数 ({{ numberCount }}组)</span>\r\n                <span v-else style=\"color: #f56c6c;\">单次识别不可超过30组，你现在是{{ numberCount }}组</span>\r\n                <el-button size=\"mini\" type=\"primary\" @click=\"showBetFormatDialog\">玩法格式</el-button>\r\n              </div>\r\n              <el-input v-model=\"formData.shibie\" type=\"textarea\" :rows=\"4\"\r\n                placeholder=\"请将号码输入此处，识别后自动生成下注号码\"\r\n                @input=\"handleNumberRecognition\" size=\"small\" :resize=\"'vertical'\"\r\n                :class=\"['recognition-textarea', { 'number-count-exceeded': isNumberCountExceeded }]\" />\r\n            </div>\r\n          </el-form-item>\r\n        </div>\r\n\r\n        <div class=\"bet-groups-list-wrapper\">\r\n          <div class=\"bet-groups-list\">\r\n            <div v-for=\"(group, idx) in formData.betGroups\" :key=\"idx\"\r\n              :class=\"['bet-group-card', { 'bet-group-even': idx % 2 === 1 }]\">\r\n              <div style=\"display:flex;align-items:center;gap:8px;margin-bottom:8px;\">\r\n                <el-form-item :label=\"'玩法名称'\" :prop=\"'betGroups.' + idx + '.methodId'\" style=\"flex:1;margin-bottom:0;\">\r\n                  <el-select v-model=\"group.methodId\"\r\n                             filterable\r\n                             remote\r\n                             :remote-method=\"filterMethod\"\r\n                             :loading=\"loadingGameMethods\"\r\n                             clearable\r\n                             placeholder=\"请选择玩法名称\"\r\n                             size=\"small\"\r\n                             style=\"width:100%\"\r\n                             @change=\"onMethodChange(group)\">\r\n                    <el-option v-for=\"item in filteredGameMethods\"\r\n                               :key=\"item.methodId\"\r\n                               :label=\"item.methodName\"\r\n                               :value=\"item.methodId\">\r\n                      {{ item.methodName }}\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item :label=\"'金额'\" :prop=\"'betGroups.' + idx + '.money'\" style=\"width:200px;margin-bottom:0;\">\r\n                  <el-input v-model=\"group.money\" placeholder=\"请输入金额\" size=\"small\" @input=\"updateTotals\" />\r\n                </el-form-item>\r\n                <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"removeBetGroup(idx)\" circle size=\"mini\" />\r\n              </div>\r\n\r\n              <div\r\n                v-if=\"group.methodId === 29 || group.methodId === 30 || group.methodId === 2 || [21, 22, 23, 24, 25, 26, 27, 37, 38, 39, 40, 41, 42, 43].includes(group.methodId)\"\r\n                style=\"margin-left: 80px; margin-bottom: 8px;\">\r\n                <template v-if=\"group.methodId === 29\">\r\n                  <div class=\"option-group\">\r\n                    <span class=\"option-label\" style=\"font-size: 16px;font-weight: 600;\">单双：</span>\r\n                    <el-radio-group v-model=\"group.danshuang\" size=\"medium\">\r\n                      <el-radio-button :label=\"200\">单</el-radio-button>\r\n                      <el-radio-button :label=\"201\">双</el-radio-button>\r\n                    </el-radio-group>\r\n                  </div>\r\n                </template>\r\n                <template v-else-if=\"group.methodId === 30\">\r\n                  <div class=\"option-group\">\r\n                    <span class=\"option-label\" style=\"font-size: 16px;font-weight: 600;\">大小：</span>\r\n                    <el-radio-group v-model=\"group.daxiao\" size=\"medium\">\r\n                      <el-radio-button :label=\"300\">大</el-radio-button>\r\n                      <el-radio-button :label=\"301\">小</el-radio-button>\r\n                    </el-radio-group>\r\n                  </div>\r\n                </template>\r\n                <template v-else-if=\"group.methodId === 2\">\r\n                  <div class=\"option-group\">\r\n                    <span class=\"option-label\" style=\"font-size: 16px;font-weight: 600;\">定位：</span>\r\n                    <el-radio-group v-model=\"group.dingwei\" size=\"medium\">\r\n                      <el-radio-button :label=\"100\">百位</el-radio-button>\r\n                      <el-radio-button :label=\"101\">十位</el-radio-button>\r\n                      <el-radio-button :label=\"102\">个位</el-radio-button>\r\n                    </el-radio-group>\r\n                  </div>\r\n                </template>\r\n                <template v-else-if=\"[21, 22, 23, 24, 25, 26, 27, 37, 38, 39, 40, 41, 42, 43].includes(group.methodId)\">\r\n                  <div class=\"option-group\">\r\n                    <span class=\"option-label\" style=\"font-size: 16px;font-weight: 600;\">和值：</span>\r\n                    <el-radio-group v-model=\"group.hezhi\" size=\"medium\">\r\n                      <el-radio-button v-for=\"hz in getHezhiOptions(group.methodId)\" :key=\"hz\"\r\n                        :label=\"hz\">和值{{ hz }}</el-radio-button>\r\n                    </el-radio-group>\r\n                  </div>\r\n                </template>\r\n              </div>\r\n\r\n              <el-form-item :label=\"'下注号码'\" :prop=\"'betGroups.' + idx + '.betNumbers'\" v-if=\"!isSpecialMethod(group.methodId)\"\r\n                style=\"margin-bottom:8px;\">\r\n                <el-input :value=\"getDisplayBetNumbers(group)\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入下注号码\"\r\n                  @input=\"onBetNumbersInput($event, idx)\" />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"彩种类型\" style=\"margin-bottom:8px;\">\r\n                <div style=\"display:flex;align-items:center;\">\r\n                  <el-radio-group v-model=\"group.lotteryId\" size=\"small\" style=\"margin-right:16px;flex-shrink:0;\"\r\n                    :fill=\"group.lotteryId === 2 ? '#1890ff' : '#ff4949'\">\r\n                    <el-radio-button :label=\"1\" data-lottery=\"fc3d\"\r\n                      style=\"margin-right:6px;\">福彩3D</el-radio-button>\r\n                    <el-radio-button :label=\"2\" data-lottery=\"tc\"\r\n                      style=\"margin-right:0;\">体彩排三</el-radio-button>\r\n                  </el-radio-group>\r\n                  <div class=\"group-summary\">\r\n                    <div class=\"summary-item amount\">\r\n                      <div class=\"summary-icon\">\r\n                        <i class=\"el-icon-wallet\"></i>\r\n                      </div>\r\n                      <div class=\"summary-content\">\r\n                        <div class=\"summary-label\">总额</div>\r\n                        <div class=\"summary-value\">￥{{ getGroupAmount(group) }}</div>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"summary-item bets\">\r\n                      <div class=\"summary-icon\">\r\n                        <i class=\"el-icon-tickets\"></i>\r\n                      </div>\r\n                      <div class=\"summary-content\">\r\n                        <div class=\"summary-label\">投注数</div>\r\n                        <div class=\"summary-value\">{{ getGroupBets(group) }}注</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </el-form-item>\r\n            </div>\r\n          </div>\r\n          <button class=\"bet-group-add-btn\" @click=\"addBetGroup\" title=\"添加投注组合\">\r\n            <!-- <i class=\"el-icon-plus\"></i> -->\r\n          </button>\r\n        </div>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-statistic title=\"下注总额\" :value=\"totalAmount\" size=\"small\">\r\n              <template slot=\"prefix\">￥</template>\r\n            </el-statistic>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-statistic title=\"总投注数\" :value=\"totalBets\" size=\"small\">\r\n              <template slot=\"suffix\">注</template>\r\n            </el-statistic>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-form-item label=\"是否结算\" prop=\"jiesuan\" v-if=\"dialogTitle === '修改下注管理'\">\r\n          <el-switch v-model=\"formData.jiesuan\" :active-value=\"1\" :inactive-value=\"0\" active-text=\"已结算\"\r\n            inactive-text=\"未结算\" size=\"small\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <div style=\"display: flex; align-items: center; justify-content: center; width: 100%;\">\r\n          <el-button type=\"primary\" @click=\"submitForm\" size=\"small\">确 定</el-button>\r\n          <el-button @click=\"close\" size=\"small\">取 消</el-button>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog\r\n      title=\"格式转换工具\"\r\n      :visible.sync=\"formatDialogVisible\"\r\n      width=\"650px\"\r\n      append-to-body\r\n      class=\"format-dialog\"\r\n      :close-on-click-modal=\"false\">\r\n\r\n      <div class=\"format-dialog-content\">\r\n        <el-tabs v-model=\"formatTab\" type=\"card\" class=\"format-tabs\">\r\n          <el-tab-pane name=\"unified\">\r\n            <span slot=\"label\">\r\n              <i class=\"el-icon-edit\"></i>\r\n              字符转换\r\n            </span>\r\n            <div class=\"tab-content\">\r\n              <div class=\"input-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-upload2\"></i>\r\n                  <span>输入内容</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"unifiedFormatInput\"\r\n                  placeholder=\"智能识别并转换：汉字金额(如二十)、逗号、句号、冒号、下划线、加号等转为空格&#10;示例：123,456。。789：：：012___345➕678二十元\"\r\n                  @input=\"handleUnifiedFormatInput\"\r\n                  class=\"format-input\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"arrow-section\">\r\n                <i class=\"el-icon-bottom\"></i>\r\n              </div>\r\n\r\n              <div class=\"output-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-download\"></i>\r\n                  <span>转换结果</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  :value=\"unifiedFormatOutput\"\r\n                  readonly\r\n                  placeholder=\"转换结果将在这里显示...\"\r\n                  class=\"format-output\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"button-section\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-check\"\r\n                  @click=\"insertUnifiedFormatResult\"\r\n                  :disabled=\"!unifiedFormatOutput\"\r\n                  class=\"action-button\">\r\n                  插入到号码识别\r\n                </el-button>\r\n                <el-button\r\n                  icon=\"el-icon-close\"\r\n                  @click=\"formatDialogVisible = false\"\r\n                  class=\"cancel-button\">\r\n                  关闭\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane name=\"chain\">\r\n            <span slot=\"label\">\r\n              <i class=\"el-icon-s-grid\"></i>\r\n              链子分类\r\n            </span>\r\n            <div class=\"tab-content\">\r\n              <div class=\"input-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-upload2\"></i>\r\n                  <span>输入内容</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"chainFormatInput\"\r\n                  placeholder=\"按号码长度自动分类到不同行\r\n示例：123-1234-568-56789-503-9012-12345678\"\r\n                  @input=\"handleChainFormatInput\"\r\n                  class=\"format-input\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"arrow-section\">\r\n                <i class=\"el-icon-bottom\"></i>\r\n              </div>\r\n\r\n              <div class=\"output-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-download\"></i>\r\n                  <span>分类结果</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"6\"\r\n                  :value=\"chainFormatOutput\"\r\n                  readonly\r\n                  placeholder=\"分类结果将在这里显示...\"\r\n                  class=\"format-output\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"button-section\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-check\"\r\n                  @click=\"insertChainFormatResult\"\r\n                  :disabled=\"!chainFormatOutput\"\r\n                  class=\"action-button\">\r\n                  插入到号码识别\r\n                </el-button>\r\n                <el-button\r\n                  icon=\"el-icon-close\"\r\n                  @click=\"formatDialogVisible = false\"\r\n                  class=\"cancel-button\">\r\n                  关闭\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane name=\"amount\">\r\n            <span slot=\"label\">\r\n              <i class=\"el-icon-money\"></i>\r\n              金额整合\r\n            </span>\r\n            <div class=\"tab-content\">\r\n              <div class=\"input-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-upload2\"></i>\r\n                  <span>输入内容</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"6\"\r\n                  v-model=\"amountFormatInput\"\r\n                  placeholder=\"将相同金额且相同位数的号码合并到一行\r\n  支持每行一个或一行内空格分隔多个\r\n  示例：57-50  59-50  79-50  88-100\r\n  结果： 57-59-79-50\r\n        88-100\"\r\n                  @input=\"handleAmountFormatInput\"\r\n                  class=\"format-input\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"arrow-section\">\r\n                <i class=\"el-icon-bottom\"></i>\r\n              </div>\r\n\r\n              <div class=\"output-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-download\"></i>\r\n                  <span>整合结果</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"6\"\r\n                  :value=\"amountFormatOutput\"\r\n                  readonly\r\n                  placeholder=\"整合结果将在这里显示...\"\r\n                  class=\"format-output\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"button-section\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-check\"\r\n                  @click=\"insertAmountFormatResult\"\r\n                  :disabled=\"!amountFormatOutput\"\r\n                  class=\"action-button\">\r\n                  插入到号码识别\r\n                </el-button>\r\n                <el-button\r\n                  icon=\"el-icon-close\"\r\n                  @click=\"formatDialogVisible = false\"\r\n                  class=\"cancel-button\">\r\n                  关闭\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane name=\"remove\">\r\n            <span slot=\"label\">\r\n              <i class=\"el-icon-delete\"></i>\r\n              去除分隔符\r\n            </span>\r\n            <div class=\"tab-content\">\r\n              <div class=\"input-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-upload2\"></i>\r\n                  <span>输入内容</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"6\"\r\n                  v-model=\"removeFormatInput\"\r\n                  placeholder=\"去除所有分隔符和英文字母，只保留汉字、数字和换行\r\n示例：123,456。。789：：：abc___def➕ghi二十元ABC\r\n结果：123456789二十元\"\r\n                  @input=\"handleRemoveFormatInput\"\r\n                  class=\"format-input\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"arrow-section\">\r\n                <i class=\"el-icon-bottom\"></i>\r\n              </div>\r\n\r\n              <div class=\"output-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-download\"></i>\r\n                  <span>清理结果</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"6\"\r\n                  :value=\"removeFormatOutput\"\r\n                  readonly\r\n                  placeholder=\"清理结果将在这里显示...\"\r\n                  class=\"format-output\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"button-section\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-check\"\r\n                  @click=\"insertRemoveFormatResult\"\r\n                  :disabled=\"!removeFormatOutput\"\r\n                  class=\"action-button\">\r\n                  插入到号码识别\r\n                </el-button>\r\n                <el-button\r\n                  icon=\"el-icon-close\"\r\n                  @click=\"formatDialogVisible = false\"\r\n                  class=\"cancel-button\">\r\n                  关闭\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 下注统计组件 -->\r\n    <bet-statistics :visible.sync=\"statisticsVisible\" />\r\n\r\n    <!-- 玩法格式弹窗组件 -->\r\n    <bet-format-dialog ref=\"betFormatDialog\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { handleNumberRecognition, handleSmartRecognition } from './numberRecognition.js';\r\nimport request from '@/utils/request';\r\nimport { getMaxSerialNumber } from '@/api/game/serial'\r\nimport { generateSerialNumber } from '@/api/game/record'\r\nimport { getCurrentQihao } from '@/api/game/qihao'\r\nimport { checkAndSetDefaultPlayer } from '@/api/game/customer'\r\nimport { getStatisticsPermission } from '@/api/system/user'\r\nimport BetStatistics from './BetStatistics.vue'\r\nimport BetFormatDialog from './BetFormatDialog.vue'\r\n\r\nexport default {\r\n  name: 'BetDialog',\r\n  components: {\r\n    BetStatistics,\r\n    BetFormatDialog\r\n  },\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: '新增下注'\r\n    },\r\n    row: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      totalAmount: 0,\r\n      totalBets: 0,\r\n      pendingRequests: [],\r\n      gameMethodsData: [], // 玩法数据\r\n      filteredGameMethods: [], // 用于 remote 搜索的玩法数据\r\n      loadingGameMethods: false, // 玩法加载状态\r\n      formData: {\r\n        fc3dIssueNumber: '',  // 福彩3D期号\r\n        tcIssueNumber: '',    // 体彩排三期号\r\n        shibie: '',\r\n        betGroups: [{\r\n          methodId: undefined,\r\n          money: '',\r\n          betNumbers: '',\r\n          danshuang: null,\r\n          daxiao: null,\r\n          dingwei: null,\r\n          hezhi: null,\r\n          lotteryId: 1\r\n        }]\r\n      },\r\n      // 新增：号码数量限制相关\r\n      numberCount: 0,\r\n      isNumberCountExceeded: false,\r\n      rules: {\r\n        betGroups: {\r\n          methodId: [{ required: true, message: '请选择玩法', trigger: 'change' }],\r\n          money: [{ required: true, message: '请输入金额', trigger: 'blur' }]\r\n        }\r\n      },\r\n      localForm: {\r\n        fc3dIssueNumber: '',  // 福彩3D期号\r\n        tcIssueNumber: '',    // 体彩排三期号\r\n        shibie: '',\r\n        betGroups: []\r\n      },\r\n      currentUser: { name: '' },\r\n      userList: [],\r\n      selectedUserId: null,\r\n      futiSwitch: sessionStorage.getItem('futiSwitch') || 'all',\r\n      formatDialogVisible: false,\r\n      formatInput: '',\r\n      formatOutput: '',\r\n      formatTab: 'unified',\r\n      colonFormatInput: '',\r\n      colonFormatOutput: '',\r\n      moneyFormatInput: '',\r\n      moneyFormatOutput: '',\r\n\r\n      underscoreFormatInput: '',\r\n      underscoreFormatOutput: '',\r\n      unifiedFormatInput: '',\r\n      unifiedFormatOutput: '',\r\n      chainFormatInput: '',\r\n      chainFormatOutput: '',\r\n      amountFormatInput: '',\r\n      amountFormatOutput: '',\r\n      removeFormatInput: '',\r\n      removeFormatOutput: '',\r\n      serialNumber: 1, // 新增本组序号\r\n      maxSerialNumber: 1, // 新增最大流水号\r\n      generateLoading: false, // 生成流水号加载状态\r\n      originalUserId: null, // 原始用户ID\r\n      originalSerialNumber: null, // 原始流水号\r\n      statisticsVisible: false, // 统计对话框显示状态\r\n      hasStatisticsPermission: false // 是否有统计权限\r\n    }\r\n  },\r\n  computed: {\r\n    /** 是否显示统计按钮 */\r\n    showStatisticsButton() {\r\n      return this.hasStatisticsPermission;\r\n    }\r\n  },\r\n  watch: {\r\n    visible(val) {\r\n      this.dialogVisible = val;\r\n      if (val) {\r\n        // 显示对话框时先检查玩家状态\r\n        this.checkPlayerStatus();\r\n        this.getAllUsers();\r\n        // 检查统计权限\r\n        this.checkStatisticsPermission();\r\n        // 新增：区分新增和修改，设置 selectedUserId\r\n        if (this.row && this.row.userId) {\r\n          // 修改时\r\n          this.selectedUserId = this.row.userId;\r\n        } else {\r\n          // 新增时，先检查session中是否有User_Id\r\n          let userId = sessionStorage.getItem('User_Id');\r\n          if (userId) {\r\n            this.selectedUserId = Number(userId);\r\n          } else {\r\n            // 如果没有，等待initializeFirstUser()设置\r\n            this.selectedUserId = null;\r\n          }\r\n        }\r\n        // 先初始化流水号，再设置表单数据\r\n      \r\n        this.initSerialNumber(); // 弹窗打开时初始化本组序号\r\n        // 如果是修改操作，初始化表单数据\r\n        if (this.row) {\r\n       \r\n          this.initFormData();\r\n        }\r\n        // 打开弹窗时根据开关状态处理\r\n        this.onFutiSwitchChange(this.futiSwitch);\r\n      }\r\n    },\r\n    dialogVisible(val) {\r\n      this.$emit('update:visible', val);\r\n      if (!val) {\r\n        this.resetForm();\r\n      }\r\n    },\r\n    // 监听用户ID变化，自动处理流水号\r\n    selectedUserId: {\r\n      handler(newUserId, oldUserId) {\r\n        // 只在修改模式下处理，且确保有原始数据\r\n        if (this.row && this.row.betId && this.originalUserId !== null && oldUserId !== undefined) {\r\n          this.handleUserIdChange(newUserId, oldUserId);\r\n        }\r\n      },\r\n      immediate: false\r\n    },\r\n    formatDialogVisible(val) {\r\n      if (!val) {\r\n        // 弹窗关闭时清空所有格式内容\r\n        this.formatInput = '';\r\n        this.formatOutput = '';\r\n        this.colonFormatInput = '';\r\n        this.colonFormatOutput = '';\r\n        this.moneyFormatInput = '';\r\n        this.moneyFormatOutput = '';\r\n\r\n        this.underscoreFormatInput = '';\r\n        this.underscoreFormatOutput = '';\r\n        this.unifiedFormatInput = '';\r\n        this.unifiedFormatOutput = '';\r\n        this.chainFormatInput = '';\r\n        this.chainFormatOutput = '';\r\n        this.amountFormatInput = '';\r\n        this.amountFormatOutput = '';\r\n        this.removeFormatInput = '';\r\n        this.removeFormatOutput = '';\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化时获取玩法列表\r\n    this.getGameMethods();\r\n  },\r\n  methods: {\r\n    // 显示玩法格式弹窗\r\n    showBetFormatDialog() {\r\n      this.$refs.betFormatDialog.show();\r\n    },\r\n    // 检查玩家状态\r\n    async checkPlayerStatus() {\r\n      try {\r\n        const response = await checkAndSetDefaultPlayer();\r\n\r\n        if (response.code === 500 && response.needCreatePlayer) {\r\n          // 没有玩家，显示提示并跳转\r\n          this.$confirm(response.msg + ' 是否立即前往创建？', '提示', {\r\n            confirmButtonText: '前往创建',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            // 关闭当前对话框\r\n            this.dialogVisible = false;\r\n            this.$emit('update:visible', false);\r\n            // 跳转到玩家页面\r\n            this.$router.push('/game/customer');\r\n          }).catch(() => {\r\n            // 用户取消，关闭对话框\r\n            this.dialogVisible = false;\r\n            this.$emit('update:visible', false);\r\n          });\r\n          return;\r\n        }\r\n\r\n        if (response.code === 200 && response.hasPlayers) {\r\n          // 有玩家，继续初始化\r\n          \r\n          if (response.defaultUserId) {\r\n            \r\n          }\r\n\r\n          // 继续初始化对话框\r\n          this.initializeDialog();\r\n        }\r\n      } catch (error) {\r\n        console.error('检查玩家状态失败:', error);\r\n        this.$message.error('检查玩家状态失败，请重试');\r\n        this.dialogVisible = false;\r\n        this.$emit('update:visible', false);\r\n      }\r\n    },\r\n\r\n    // 初始化对话框\r\n    initializeDialog() {\r\n      // 显示对话框时获取期号和玩法列表\r\n      this.getCurrentIssueNumber();\r\n      this.getGameMethods();\r\n      this.getCurrentUser();\r\n    },\r\n\r\n    // 获取玩法列表\r\n    getGameMethods() {\r\n      this.loadingGameMethods = true;\r\n      return request({\r\n        url: '/game/method/list',\r\n        method: 'get',\r\n        params: {\r\n          pageNum: 1,\r\n          pageSize: 1000  // 设置足够大的页面大小以获取所有数据\r\n        }\r\n      }).then(response => {\r\n        let data = [];\r\n        if (response.code === 200) {\r\n          if (Array.isArray(response.data)) {\r\n            data = response.data;\r\n          } else if (response.rows && Array.isArray(response.rows)) {\r\n            data = response.rows;\r\n          } else if (typeof response.data === 'string') {\r\n            try {\r\n              const parsedData = JSON.parse(response.data);\r\n              data = Array.isArray(parsedData) ? parsedData : [];\r\n            } catch (e) {\r\n              data = [];\r\n            }\r\n          }\r\n        }\r\n        this.gameMethodsData = data;\r\n        this.filteredGameMethods = data;\r\n        this.loadingGameMethods = false;\r\n      }).catch(() => {\r\n        this.gameMethodsData = [];\r\n        this.filteredGameMethods = [];\r\n        this.loadingGameMethods = false;\r\n      });\r\n    },\r\n    // 打开对话框\r\n    show(title = '新增下注') {\r\n      this.dialogTitle = title;\r\n      this.dialogVisible = true;\r\n    },\r\n    // 关闭对话框\r\n    close() {\r\n      this.dialogVisible = false;\r\n      this.$emit('update:visible', false);\r\n      this.$emit('cancel');  // 保持向后兼容\r\n    },\r\n    // 重置表单\r\n    resetForm() {\r\n      this.formData = {\r\n        fc3dIssueNumber: '',\r\n        tcIssueNumber: '',\r\n        shibie: '',\r\n        betGroups: [{\r\n          methodId: undefined,\r\n          money: '',\r\n          betNumbers: '',\r\n          danshuang: null,\r\n          daxiao: null,\r\n          dingwei: null,\r\n          hezhi: null,\r\n          lotteryId: 1\r\n        }]\r\n      };\r\n      this.totalAmount = 0;\r\n      this.totalBets = 0;\r\n      // 重置号码数量状态\r\n      this.numberCount = 0;\r\n      this.isNumberCountExceeded = false;\r\n    },\r\n    addBetGroup() {\r\n      const newGroup = {\r\n        methodId: undefined,\r\n        money: '',\r\n        betNumbers: '',\r\n        danshuang: null,\r\n        daxiao: null,\r\n        dingwei: null,\r\n        hezhi: null,\r\n        lotteryId: this.futiSwitch === 'tc' ? 2 : 1\r\n      };\r\n\r\n      // 直接操作formData\r\n      if (!this.formData.betGroups) {\r\n        this.formData.betGroups = [];\r\n      }\r\n      this.formData.betGroups.push({ ...newGroup });\r\n\r\n      this.$nextTick(() => {\r\n        this.updateTotals();\r\n      });\r\n    },\r\n    removeBetGroup(idx) {\r\n      if (this.formData.betGroups.length <= 1) {\r\n        // 如果只剩一个投注组，则清空它而不是删除\r\n        this.formData.betGroups = [{\r\n          methodId: undefined,\r\n          money: '',\r\n          betNumbers: '',\r\n          danshuang: null,\r\n          daxiao: null,\r\n          dingwei: null,\r\n          hezhi: null,\r\n          lotteryId: 1\r\n        }];\r\n      } else {\r\n        // 如果有多个投注组，则删除指定的组\r\n        this.formData.betGroups.splice(idx, 1);\r\n      }\r\n\r\n      this.$nextTick(() => {\r\n        this.updateTotals();\r\n        this.$forceUpdate(); // 强制更新视图\r\n      });\r\n    },\r\n    updateTotals() {\r\n      this.totalAmount = this.formData.betGroups.reduce((sum, g) => sum + Number(this.getGroupAmount(g)), 0);\r\n      this.totalBets = this.formData.betGroups.reduce((sum, g) => sum + Number(this.getGroupBets(g)), 0);\r\n    },\r\n    getHezhiOptions(methodId) {\r\n      const map = {\r\n        21: [7, 20],\r\n        22: [8, 19],\r\n        23: [9, 18],\r\n        24: [10, 17],\r\n        25: [11, 16],\r\n        26: [12, 15],\r\n        27: [13, 14],\r\n        37: [0, 27],\r\n        38: [1, 26],\r\n        39: [2, 25],\r\n        40: [3, 24],\r\n        41: [4, 23],\r\n        42: [5, 22],\r\n        43: [6, 21]\r\n      };\r\n      return map[methodId] || [];\r\n    },\r\n    onMethodChange(group) {\r\n      \r\n      const index = this.formData.betGroups.findIndex(g => g === group);\r\n      if (index > -1) {\r\n        // 创建一个新的组对象，确保包含所有必要的字段\r\n        const updatedGroup = {\r\n          ...group,\r\n          danshuang: null,\r\n          daxiao: null,\r\n          dingwei: null,\r\n          hezhi: null\r\n          // 不重置 betNumbers 和 money\r\n        };\r\n\r\n        // 根据玩法类型设置默认值\r\n        if (updatedGroup.methodId === 29) {\r\n          updatedGroup.danshuang = 200;\r\n        } else if (updatedGroup.methodId === 30) {\r\n          updatedGroup.daxiao = 300;\r\n        } else if (updatedGroup.methodId === 2) {\r\n          updatedGroup.dingwei = 100;\r\n        } else if ([21, 22, 23, 24, 25, 26, 27, 37, 38, 39, 40, 41, 42, 43].includes(updatedGroup.methodId)) {\r\n          const options = this.getHezhiOptions(updatedGroup.methodId);\r\n          if (options && options.length > 0) {\r\n            updatedGroup.hezhi = options[0];\r\n          }\r\n        }\r\n\r\n        // 更新组数据\r\n        this.$set(this.formData.betGroups, index, updatedGroup);\r\n        this.$forceUpdate();\r\n      }\r\n    },\r\n    // 获取当前期号\r\n    getCurrentIssueNumber() {\r\n      const loadingInstance = this.$loading({\r\n        lock: true,\r\n        text: '正在获取期号...',\r\n        spinner: 'el-icon-loading',\r\n        background: 'rgba(0, 0, 0, 0.7)',\r\n        customClass: 'bet-dialog-loading'\r\n      });\r\n\r\n      // 使用Promise.all同时获取两个期号\r\n      Promise.all([\r\n        // 获取福彩3D期号\r\n        getCurrentQihao(1).catch(error => {\r\n          console.error('获取福彩3D期号失败:', error);\r\n          return { code: 500, msg: '获取失败' };\r\n        }),\r\n        // 获取体彩排三期号\r\n        getCurrentQihao(2).catch(error => {\r\n          console.error('获取体彩排三期号失败:', error);\r\n          return { code: 500, msg: '获取失败' };\r\n        })\r\n      ]).then(([fc3dResponse, tcResponse]) => {\r\n        // 处理福彩3D期号\r\n        if (fc3dResponse.code === 200) {\r\n          this.formData.fc3dIssueNumber = fc3dResponse.msg;\r\n          \r\n        } else {\r\n          console.error('获取福彩3D期号失败:', fc3dResponse);\r\n          this.$message.error('获取福彩3D期号失败');\r\n        }\r\n\r\n        // 处理体彩排三期号\r\n        if (tcResponse.code === 200) {\r\n          this.formData.tcIssueNumber = tcResponse.msg;\r\n         \r\n        } else {\r\n          console.error('获取体彩排三期号失败:', tcResponse);\r\n          this.$message.error('获取体彩排三期号失败');\r\n        }\r\n      }).finally(() => {\r\n        loadingInstance.close();\r\n      });\r\n    },\r\n    // 新增：动态计算号码数量的方法\r\n    calculateNumberCount(input) {\r\n      if (!input || !input.trim()) return 0;\r\n\r\n      // 清理输入，去除首尾空白\r\n      const cleanInput = input.trim();\r\n\r\n      // 使用更精确的正则匹配号码组\r\n      // 匹配2-9位连续数字，考虑各种分隔符\r\n      const numberPattern = /\\d{2,9}/g;\r\n      const allMatches = cleanInput.match(numberPattern);\r\n\r\n      if (!allMatches) return 0;\r\n\r\n      // 按行分析，更准确地识别号码和金额\r\n      const lines = cleanInput.split(/[\\r\\n]+/).filter(line => line.trim());\r\n      let totalCount = 0;\r\n\r\n      for (const line of lines) {\r\n        const trimmedLine = line.trim();\r\n        if (!trimmedLine) continue;\r\n\r\n        const lineNumbers = trimmedLine.match(numberPattern);\r\n        if (!lineNumbers) continue;\r\n\r\n        // 检查金额相关关键字\r\n        const moneyKeywords = /[元块米钱赶组直吊放防各]/;\r\n        const hasMoneyKeyword = moneyKeywords.test(trimmedLine);\r\n\r\n        // 检查是否有明显的金额分隔符（如+、/等）\r\n        const hasMoneyDelimiter = /[+＋\\/]/.test(trimmedLine);\r\n\r\n        if (hasMoneyKeyword || hasMoneyDelimiter) {\r\n          // 如果包含金额关键字或分隔符，最后1-2个数字可能是金额\r\n          // 更保守的估计：减去1个可能的金额\r\n          totalCount += Math.max(0, lineNumbers.length - 1);\r\n        } else {\r\n          // 纯号码行，所有数字都算作号码\r\n          totalCount += lineNumbers.length;\r\n        }\r\n      }\r\n\r\n      return totalCount;\r\n    },\r\n    // 智能预处理批量号码+金额输入，避免金额被误识别为号码\r\n    smartPreprocessInput(input) {\r\n      if (!input) return input;\r\n      // 支持多行或\"—\"等超级分隔符\r\n      let parts = input.split(/[—\\-\\n\\r]+/).map(s => s.trim()).filter(Boolean);\r\n      if (parts.length > 1) {\r\n        // 金额表达关键字更宽松，兼容赶/组/直/吊/放/防/元/各等\r\n        const moneyExpr = /(赶|组|直|吊|放|防|元|各)[^\\d]*\\d+.*$/;\r\n        const last = parts[parts.length - 1];\r\n        if (moneyExpr.test(last)) {\r\n          // 合并为\"号码—号码—...—金额表达\"\r\n          return parts.slice(0, -1).join('—') + '—' + last;\r\n        }\r\n      }\r\n      return input;\r\n    },\r\n    // 新增：检查首组号码是否为三位数的方法\r\n    isFirstNumberThreeDigits(input) {\r\n      if (!input || !input.trim()) return false;\r\n\r\n      // 清理输入，去除首尾空白\r\n      const cleanInput = input.trim();\r\n\r\n      // 匹配第一个2-9位连续数字\r\n      const firstNumberMatch = cleanInput.match(/\\d{2,9}/);\r\n\r\n      if (!firstNumberMatch) return false;\r\n\r\n      // 检查第一个号码是否为三位数\r\n      return firstNumberMatch[0].length === 3;\r\n    },\r\n    handleNumberRecognition(value) {\r\n      // 实时计算并更新号码数量\r\n      this.numberCount = this.calculateNumberCount(value);\r\n\r\n      // 判断如果首组号码为三位数则不限制号码组数为30\r\n      const isFirstThreeDigits = this.isFirstNumberThreeDigits(value);\r\n      this.isNumberCountExceeded = isFirstThreeDigits ? false : this.numberCount > 30;\r\n\r\n      if (!value) {\r\n        this.formData.betGroups = [{\r\n          methodId: undefined,\r\n          money: '',\r\n          betNumbers: '',\r\n          danshuang: null,\r\n          daxiao: null,\r\n          dingwei: null,\r\n          hezhi: null,\r\n          lotteryId: this.futiSwitch === 'tc' ? 2 : 1\r\n        }];\r\n        return;\r\n      }\r\n\r\n      // 如果超过30个号码，不进行识别处理，但仍然显示计数\r\n      if (this.isNumberCountExceeded) {\r\n        \r\n        return;\r\n      }\r\n      // 1. 先整体识别（必须用原始 value，不能预处理！）\r\n      let result = handleNumberRecognition(value);\r\n      // 2. 如果整体没命中，再按行分割，每行单独识别，不做任何合并\r\n      if (!result || !result.groups || !result.groups.some(g => g.methodId)) {\r\n        const lines = value.split(/[\\r\\n]+/).map(line => line.trim()).filter(Boolean);\r\n        let allGroups = [];\r\n        for (const line of lines) {\r\n          const lineResult = handleNumberRecognition(line);\r\n          if (lineResult && lineResult.groups && lineResult.groups.length > 0) {\r\n            allGroups = allGroups.concat(lineResult.groups);\r\n          }\r\n        }\r\n        result = { groups: allGroups };\r\n      }\r\n      \r\n      \r\n\r\n      if (result) {\r\n        // 如果存在groups，说明有多个和值组\r\n        if (result.groups && result.groups.length > 0) {\r\n     \r\n          // 清空现有的投注组\r\n          this.$set(this.formData, 'betGroups', []);\r\n          // 添加所有和值组\r\n          result.groups.forEach(group => {\r\n            const newGroup = {\r\n              methodId: group.methodId,\r\n              danshuang: group.danshuang,\r\n              daxiao: group.daxiao,\r\n              dingwei: group.dingwei,\r\n              hezhi: group.hezhi,\r\n              betNumbers: group.betNumbers,\r\n              money: group.money != null ? String(group.money) : '', // 确保money为字符串类型\r\n              lotteryId: this.futiSwitch === 'tc' ? 2 : (group.lotteryId || 1)\r\n            };\r\n\r\n            this.formData.betGroups.push(newGroup);\r\n          });\r\n        } else {\r\n\r\n          // 单个投注组的情况\r\n          const newGroup = {\r\n            methodId: result.methodId,\r\n            danshuang: result.danshuang,\r\n            daxiao: result.daxiao,\r\n            dingwei: result.dingwei,\r\n            hezhi: result.hezhi,\r\n            betNumbers: result.betNumbers,\r\n            money: result.money != null ? String(result.money) : '', // 确保money为字符串类型\r\n            lotteryId: this.futiSwitch === 'tc' ? 2 : (result.lotteryId || 1)\r\n          };\r\n\r\n          this.$set(this.formData, 'betGroups', [newGroup]);\r\n        }\r\n\r\n        // 同步到localForm\r\n        const localGroups = this.formData.betGroups.map(group => ({\r\n          ...group,\r\n          danshuang: group.danshuang || null,\r\n          daxiao: group.daxiao || null,\r\n          dingwei: group.dingwei || null,\r\n          hezhi: group.hezhi || null,\r\n          betNumbers: group.betNumbers || '',\r\n          money: group.money || null,\r\n          lotteryId: group.lotteryId || 1\r\n        }));\r\n   \r\n        this.$set(this.localForm, 'betGroups', localGroups);\r\n\r\n        // 强制更新视图\r\n        this.$nextTick(() => {\r\n          \r\n          this.updateTotals();\r\n          // 确保每个投注组都正确更新\r\n          this.formData.betGroups.forEach((group, index) => {\r\n            const updatedGroup = { ...group };\r\n           \r\n            this.$set(this.formData.betGroups, index, updatedGroup);\r\n          });\r\n    \r\n          this.$forceUpdate();\r\n        });\r\n      }\r\n    },\r\n    formatBetNumbersToDisplay(jsonStr) {\r\n      try {\r\n        if (!jsonStr) return '';\r\n\r\n        // 如果不是JSON格式，直接返回\r\n        if (!jsonStr.startsWith('{')) {\r\n          return jsonStr;\r\n        }\r\n\r\n        const data = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr;\r\n        if (!data.numbers || !Array.isArray(data.numbers)) return jsonStr;\r\n\r\n        return data.numbers.map(num => {\r\n          // 跨度玩法特殊处理\r\n          if (num.kuadu !== undefined) {\r\n            return `跨度${num.kuadu}`;\r\n          }\r\n          // 胆拖玩法特殊处理\r\n          if (num.danma !== undefined && num.tuoma !== undefined) {\r\n            return `胆${num.danma}拖${num.tuoma}`;\r\n          }\r\n          // 其他玩法\r\n          const values = Object.values(num);\r\n          return values.join('');\r\n        }).join(',');\r\n      } catch (error) {\r\n        console.error('格式化下注号码失败:', error, 'jsonStr:', jsonStr);\r\n        return jsonStr;\r\n      }\r\n    },\r\n    // 获取用于显示的下注号码\r\n    getDisplayBetNumbers(group) {\r\n      if (!group.betNumbers) return '';\r\n\r\n      // 跨度玩法 (60-69) 和胆拖玩法 (44-59) 需要特殊格式化\r\n      if ((group.methodId >= 44 && group.methodId <= 59) || (group.methodId >= 60 && group.methodId <= 69)) {\r\n        try {\r\n          // 检查是否是JSON格式\r\n          if (group.betNumbers.startsWith('{') && group.betNumbers.includes('numbers')) {\r\n            return this.formatBetNumbersToDisplay(group.betNumbers);\r\n          }\r\n          // 如果不是JSON格式，直接返回（用户正在编辑）\r\n          return group.betNumbers;\r\n        } catch (error) {\r\n          console.error('格式化显示号码失败:', error);\r\n          return group.betNumbers;\r\n        }\r\n      }\r\n\r\n      // 其他玩法直接显示\r\n      return group.betNumbers;\r\n    },\r\n    // 处理下注号码输入\r\n    onBetNumbersInput(value, index) {\r\n      const group = this.formData.betGroups[index];\r\n\r\n      // 跨度玩法和胆拖玩法需要保持JSON格式存储，但显示为用户友好格式\r\n      if ((group.methodId >= 44 && group.methodId <= 59) || (group.methodId >= 60 && group.methodId <= 69)) {\r\n        // 对于这些特殊玩法，用户输入的友好格式需要转换为JSON格式存储\r\n        // 但这里我们暂时直接存储用户输入，让识别逻辑处理\r\n        this.$set(this.formData.betGroups, index, { ...group, betNumbers: value });\r\n      } else {\r\n        // 其他玩法直接存储\r\n        this.$set(this.formData.betGroups, index, { ...group, betNumbers: value });\r\n      }\r\n\r\n      this.updateTotals();\r\n    },\r\n    initFormData() {\r\n      try {\r\n        // 保存原始数据\r\n        this.originalUserId = this.row.userId;\r\n        this.originalSerialNumber = this.row.serialNumber;\r\n\r\n        // 设置期号\r\n        this.formData.fc3dIssueNumber = this.row.issueNumber;\r\n        this.formData.tcIssueNumber = this.row.issueNumber;\r\n\r\n        // 格式化下注号码\r\n        const formattedBetNumbers = this.formatBetNumbersToDisplay(this.row.betNumbers);\r\n\r\n        // 创建投注组\r\n        const group = {\r\n          methodId: this.row.methodId,\r\n          money: this.row.money != null ? String(this.row.money) : '', // 确保money为字符串类型\r\n          betNumbers: formattedBetNumbers, // 使用格式化后的号码\r\n          danshuang: this.row.danshuang,\r\n          daxiao: this.row.daxiao,\r\n          dingwei: this.row.dingwei,\r\n          hezhi: this.row.hezhi,\r\n          lotteryId: this.row.lotteryId\r\n        };\r\n\r\n        // 设置投注组\r\n        this.formData.betGroups = [group];\r\n\r\n        // 如果有识别文本，也设置\r\n        if (this.row.shibie) {\r\n          this.formData.shibie = this.row.shibie;\r\n        }\r\n\r\n        // 如果有流水号，也设置\r\n        if (this.row.serialNumber) {\r\n       \r\n          this.serialNumber = this.row.serialNumber;\r\n        }\r\n\r\n        // 更新总计\r\n        this.$nextTick(() => {\r\n          this.updateTotals();\r\n          // 强制更新视图以确保特殊玩法正确显示\r\n          this.$forceUpdate();\r\n        });\r\n      } catch (error) {\r\n        console.error('初始化表单数据失败:', error);\r\n      }\r\n    },\r\n    formatNumbersForRequest(betNumbers, methodId) {\r\n      if (!betNumbers) return { numbers: [] };\r\n\r\n      // 胆拖玩法 (44-59) - 只存储danma和tuoma\r\n      if (methodId >= 44 && methodId <= 59) {\r\n        return this.formatDantuoNumbers(betNumbers, methodId);\r\n      }\r\n\r\n      // 跨度玩法 (60-69) - 只存储kuadu\r\n      if (methodId >= 60 && methodId <= 69) {\r\n        return this.formatKuaduNumbers(betNumbers, methodId);\r\n      }\r\n\r\n      // 分割号码组\r\n      const numberGroups = betNumbers.split(',').filter(n => n.trim());\r\n\r\n      // 根据玩法格式化\r\n      const formatMap = {\r\n        // 一位数玩法（独胆、一码定位、杀码等）\r\n        1: (num) => ({ a: parseInt(num) }),\r\n        2: (num) => ({ a: parseInt(num) }),\r\n        33: (num) => ({ a: parseInt(num) }),\r\n        34: (num) => ({ a: num }), // 两码定位，保持原始模式如\"9*6\"\r\n        35: (num) => ({ a: num }), // 一码单双\r\n        36: (num) => ({ a: num }), // 一码大小\r\n        // 两位数玩法（两码组合、对子等）\r\n        3: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1] };\r\n        },\r\n        4: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1] };\r\n        },\r\n        // 两码组三\r\n        70: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1] };\r\n        },\r\n\r\n        // 三位数玩法（三码直选、组选、防对等）\r\n        5: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2] };\r\n        },\r\n        6: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2] };\r\n        },\r\n        7: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2] };\r\n        },\r\n        100: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2] };\r\n        },\r\n\r\n        // 四码\r\n        8: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3] };\r\n        },\r\n        9: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3] };\r\n        },\r\n\r\n        // 五码\r\n        10: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4] };\r\n        },\r\n        11: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4] };\r\n        },\r\n\r\n        // 六码\r\n        12: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5] };\r\n        },\r\n        13: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5] };\r\n        },\r\n\r\n        // 七码\r\n        14: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6] };\r\n        },\r\n        15: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6] };\r\n        },\r\n\r\n        // 八码\r\n        16: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6], h: digits[7] };\r\n        },\r\n        17: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6], h: digits[7] };\r\n        },\r\n\r\n        // 九码\r\n        18: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6], h: digits[7], i: digits[8] };\r\n        },\r\n        19: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6], h: digits[7], i: digits[8] };\r\n        },\r\n\r\n        // 包打组六、组三（不需要号码，返回空对象）\r\n        20: () => ({}),\r\n        31: () => ({}),\r\n\r\n        // 和值玩法（和值直接传递，不需要号码）\r\n        21: () => ({}),\r\n        22: () => ({}),\r\n        23: () => ({}),\r\n        24: () => ({}),\r\n        25: () => ({}),\r\n        26: () => ({}),\r\n        27: () => ({}),\r\n\r\n        // 新增和值玩法（和值直接传递，不需要号码）\r\n        37: () => ({}),\r\n        38: () => ({}),\r\n        39: () => ({}),\r\n        40: () => ({}),\r\n        41: () => ({}),\r\n        42: () => ({}),\r\n        43: () => ({}),\r\n\r\n        // 直选复式（暂时按空对象处理）\r\n        28: () => ({}),\r\n\r\n        // 和值单双\r\n        29: () => ({}),\r\n        // 和值大小\r\n        30: () => ({}),\r\n\r\n      };\r\n\r\n      const formatter = formatMap[methodId];\r\n      if (!formatter) return { numbers: [] };\r\n\r\n      return {\r\n        numbers: numberGroups.map(num => formatter(num.trim()))\r\n      };\r\n    },\r\n    submitForm() {\r\n      // 先验证金额\r\n      const moneyValidation = this.validateMoney();\r\n      if (!moneyValidation.valid) {\r\n        this.$alert(moneyValidation.message, '错误', {\r\n          confirmButtonText: '确定',\r\n          type: 'error',\r\n          dangerouslyUseHTMLString: true\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          const loadingInstance = this.$loading({\r\n            lock: true,\r\n            text: '正在提交下注...',\r\n            spinner: 'el-icon-loading',\r\n            background: 'rgba(0, 0, 0, 0.7)'\r\n          });\r\n\r\n          // 修正 userId 赋值逻辑\r\n          const betRecords = this.formData.betGroups.map((group, index) => {\r\n            // 格式化下注号码为JSON格式\r\n            const numbers = this.formatNumbersForRequest(group.betNumbers, group.methodId);\r\n            // 从 sessionStorage 获取 sysUserId\r\n            const sysUserId = sessionStorage.getItem('sysUserId');\r\n\r\n            const record = {\r\n              lotteryId: group.lotteryId,\r\n              issueNumber: group.lotteryId === 1 ? this.formData.fc3dIssueNumber : this.formData.tcIssueNumber,\r\n              methodId: group.methodId,\r\n              // 修改：userId 始终用当前选择的 selectedUserId\r\n              userId: Number(this.selectedUserId),\r\n              // 新增：从 sessionStorage 获取并传递 sysUserId\r\n              sysUserId: sysUserId ? Number(sysUserId) : null,\r\n              betNumbers: JSON.stringify(numbers),\r\n              money: group.money,\r\n              danshuang: group.danshuang,\r\n              daxiao: group.daxiao,\r\n              dingwei: group.dingwei,\r\n              hezhi: group.hezhi,\r\n              totalAmount: this.getGroupAmount(group),\r\n              totalBets: this.getGroupBets(group),\r\n              shibie: this.formData.shibie,\r\n              betTime: this.formatDate(new Date()),\r\n              serialNumber: this.serialNumber // 新增：传递流水号\r\n            };\r\n            // 如果是修改操作，添加betId\r\n            if (this.row && index === 0) {\r\n              record.betId = this.row.betId;\r\n            }\r\n            // 逐行打印每个 record\r\n          \r\n            return record;\r\n          });\r\n\r\n          // 提交前打印所有 betRecords\r\n       \r\n\r\n          // 根据是否是修改操作选择不同的提交方式\r\n          const url = this.row ? '/game/record' : '/game/record/batch';\r\n          const method = this.row ? 'put' : 'post';\r\n          const data = this.row ? betRecords[0] : betRecords;\r\n\r\n          request({\r\n            url,\r\n            method,\r\n            data\r\n          }).then(response => {\r\n            if (response.code === 200) {\r\n              this.$modal.msgSuccess('操作成功');\r\n              this.$emit('success');\r\n              // 清空号码识别框和投注组\r\n              this.formData.shibie = '';\r\n              this.formData.betGroups = [{\r\n                methodId: undefined,\r\n                money: '',\r\n                betNumbers: '',\r\n                danshuang: null,\r\n                daxiao: null,\r\n                dingwei: null,\r\n                hezhi: null,\r\n                lotteryId: 1\r\n              }];\r\n              this.updateTotals();\r\n              // 新增：仅在修改时关闭弹窗，新增时不关闭\r\n              if (this.row) {\r\n                this.close();\r\n              }\r\n            } else {\r\n              // 如果后端返回了具体哪些投注失败\r\n              if (response.data && Array.isArray(response.data.failedIndices)) {\r\n                // 保留失败的投注组\r\n                const failedGroups = this.formData.betGroups.filter((_, index) =>\r\n                  response.data.failedIndices.includes(index)\r\n                );\r\n                this.$set(this.formData, 'betGroups', failedGroups);\r\n                this.$modal.msgError(`${response.data.failedIndices.length}个下注失败，请重试`);\r\n              } else {\r\n                // 检查错误是否已经被响应拦截器处理\r\n                if (response._errorHandled) {\r\n                  // 错误已经被响应拦截器处理并显示，这里不再重复处理\r\n          \r\n                } else {\r\n                  // 未被处理的错误，显示通用错误信息\r\n                  this.$modal.msgError(response.msg || '操作失败，请重试');\r\n                }\r\n              }\r\n            }\r\n          }).catch(error => {\r\n            console.error('网络请求失败:', error);\r\n\r\n            // 现在只有真正的网络错误才会进入 catch 块\r\n            // 业务错误已经在响应拦截器中处理并返回到 then 块\r\n            this.$modal.msgError('网络连接失败，请检查网络后重试');\r\n          }).finally(() => {\r\n            loadingInstance.close();\r\n            this.updateTotals();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    getGroupBets(group) {\r\n      // 胆拖玩法 (44-59) - 始终显示为1注\r\n      if (group.methodId >= 44 && group.methodId <= 59) {\r\n        return 1;\r\n      }\r\n\r\n      // 跨度玩法 (60-69) - 始终显示为1注\r\n      if (group.methodId >= 60 && group.methodId <= 69) {\r\n        return 1;\r\n      }\r\n\r\n      if (this.isSpecialMethod(group.methodId)) {\r\n        if ([21,22,23,24,25,26,27,37,38,39,40,41,42,43].includes(group.methodId)) {\r\n          return group.hezhi ? 1 : 0;\r\n        }\r\n        if ([29,30].includes(group.methodId)) {\r\n          if (group.danshuang || group.daxiao) {\r\n            return 1;\r\n          }\r\n          return 0;\r\n        }\r\n        if ([20,31].includes(group.methodId)) {  // 包打组六和包打组三\r\n          return 1;\r\n        }\r\n        return 0;\r\n      }\r\n      if (group.betNumbers) {\r\n        return group.betNumbers.split(',').filter(s => s.trim()).length;\r\n      }\r\n      return 0;\r\n    },\r\n    getGroupAmount(group) {\r\n      const bets = this.getGroupBets(group);\r\n      const money = Number(group.money) || 0;\r\n      return bets * money;\r\n    },\r\n    isSpecialMethod(methodId) {\r\n      return [20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 37, 38, 39, 40, 41, 42, 43].includes(methodId);\r\n    },\r\n    // 胆拖格式化方法\r\n    formatDantuoNumbers(betNumbers, methodId) {\r\n      // 解析胆码和拖码\r\n      const parts = betNumbers.split('拖');\r\n      if (parts.length !== 2) return { numbers: [] };\r\n\r\n      const danma = parts[0].replace(/[^0-9]/g, '');\r\n      const tuoma = parts[1].replace(/[^0-9,，\\s]/g, '').split(/[,，\\s]+/).filter(n => n && n !== danma);\r\n\r\n      return {\r\n        numbers: [{\r\n          danma: danma,\r\n          tuoma: tuoma.join(',')\r\n        }]\r\n      };\r\n    },\r\n    // 跨度格式化方法\r\n    formatKuaduNumbers(betNumbers, methodId) {\r\n      console.log('formatKuaduNumbers 输入:', betNumbers, 'methodId:', methodId);\r\n\r\n      // 如果已经是JSON格式，直接返回\r\n      if (betNumbers.startsWith('{')) {\r\n        try {\r\n          const parsed = JSON.parse(betNumbers);\r\n          console.log('已是JSON格式:', parsed);\r\n          return parsed;\r\n        } catch (error) {\r\n          console.error('JSON解析失败:', error);\r\n        }\r\n      }\r\n\r\n      // 解析跨度值 - 支持多种格式\r\n      let kuaduValue = null;\r\n\r\n      // 格式1: \"跨度5\"\r\n      let match = betNumbers.match(/跨度(\\d)/);\r\n      if (match) {\r\n        kuaduValue = match[1];\r\n      } else {\r\n        // 格式2: \"跨5-50\" 或 \"跨5 50\"\r\n        match = betNumbers.match(/跨(\\d)[\\s\\-]*\\d+/);\r\n        if (match) {\r\n          kuaduValue = match[1];\r\n        } else {\r\n          // 格式3: 从methodId推导跨度值\r\n          if (methodId >= 60 && methodId <= 69) {\r\n            kuaduValue = (methodId - 60).toString();\r\n          }\r\n        }\r\n      }\r\n\r\n      console.log('解析出的跨度值:', kuaduValue);\r\n\r\n      if (kuaduValue === null) {\r\n        console.warn('无法解析跨度值，返回空数组');\r\n        return { numbers: [] };\r\n      }\r\n\r\n      const result = {\r\n        numbers: [{\r\n          kuadu: kuaduValue\r\n        }]\r\n      };\r\n\r\n      console.log('formatKuaduNumbers 输出:', result);\r\n      return result;\r\n    },\r\n    updateGroupValue(group, field, value) {\r\n\r\n      const index = this.formData.betGroups.findIndex(g => g === group);\r\n      if (index > -1) {\r\n        const updatedGroup = {\r\n          ...this.formData.betGroups[index],\r\n          [field]: value\r\n        };\r\n        this.$set(this.formData.betGroups, index, updatedGroup);\r\n        this.$forceUpdate();\r\n      }\r\n    },\r\n\r\n\r\n    // 格式化日期为 yyyy-MM-dd HH:mm:ss\r\n    formatDate(date) {\r\n      const pad = (num) => (num < 10 ? `0${num}` : num);\r\n      const year = date.getFullYear();\r\n      const month = pad(date.getMonth() + 1);\r\n      const day = pad(date.getDate());\r\n      const hours = pad(date.getHours());\r\n      const minutes = pad(date.getMinutes());\r\n      const seconds = pad(date.getSeconds());\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n    getCurrentUser() {\r\n      let userId = sessionStorage.getItem('User_Id');\r\n\r\n      if (userId) {\r\n        // 如果session中有User_Id，直接使用\r\n        this.selectedUserId = Number(userId);\r\n        this.loadUserById(userId);\r\n      } else {\r\n        // 如果session中没有User_Id，先获取用户列表，然后使用第一个用户\r\n        this.initializeFirstUser();\r\n      }\r\n    },\r\n\r\n    loadUserById(userId) {\r\n      request({\r\n        url: `/game/customer/${userId}`,\r\n        method: 'get'\r\n      }).then(res => {\r\n        if (res.code === 200 && res.data) {\r\n          this.currentUser = res.data;\r\n        } else {\r\n          this.currentUser = { name: '未知用户' };\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取用户信息失败:', error);\r\n        this.currentUser = { name: '未知用户' };\r\n      });\r\n    },\r\n\r\n    initializeFirstUser() {\r\n      // 获取当前用户的玩家列表\r\n      request({\r\n        url: '/game/customer/list',\r\n        method: 'get',\r\n        params: { pageNum: 1, pageSize: 1000 }\r\n      }).then(res => {\r\n        if (res.code === 200 && res.rows && res.rows.length > 0) {\r\n          // 使用第一个玩家作为默认用户\r\n          const firstUser = res.rows[0];\r\n          this.selectedUserId = firstUser.userId;\r\n          this.currentUser = firstUser;\r\n          // 保存到session中\r\n          sessionStorage.setItem('User_Id', firstUser.userId);\r\n        \r\n        } else {\r\n          console.warn('当前用户没有玩家数据');\r\n          this.currentUser = { name: '无玩家数据' };\r\n          this.selectedUserId = null;\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取玩家列表失败:', error);\r\n        this.currentUser = { name: '获取失败' };\r\n        this.selectedUserId = null;\r\n      });\r\n    },\r\n    getAllUsers() {\r\n      request({\r\n        url: '/game/customer/list',\r\n        method: 'get',\r\n        params: { pageNum: 1, pageSize: 1000 }\r\n      }).then(res => {\r\n        if (res.code === 200 && res.rows) {\r\n          this.userList = res.rows;\r\n        } else {\r\n          this.userList = [];\r\n        }\r\n      });\r\n    },\r\n    switchUser() {\r\n      if (!this.selectedUserId) return;\r\n      sessionStorage.setItem('User_Id', this.selectedUserId);\r\n      this.getCurrentUser();\r\n      this.$message.success('切换用户成功');\r\n    },\r\n    onFutiSwitchChange(val) {\r\n      sessionStorage.setItem('futiSwitch', val);\r\n      if (val === 'tc') {\r\n        // 仅体彩，所有下注单都设为2\r\n        if (this.formData && this.formData.betGroups) {\r\n          this.formData.betGroups.forEach(group => {\r\n            group.lotteryId = 2;\r\n            // 确保money字段始终为字符串类型\r\n            if (group.money != null && typeof group.money !== 'string') {\r\n              group.money = String(group.money);\r\n            }\r\n          });\r\n        }\r\n      } else {\r\n        // 切换为全部彩种时，确保数据类型正确\r\n        if (this.formData && this.formData.betGroups) {\r\n          this.formData.betGroups.forEach(group => {\r\n            // 确保money字段始终为字符串类型\r\n            if (group.money != null && typeof group.money !== 'string') {\r\n              group.money = String(group.money);\r\n            }\r\n          });\r\n        }\r\n      }\r\n      // 切换为全部彩种时不做强制限制\r\n      this.$forceUpdate();\r\n    },\r\n    // remote 搜索用的 filterMethod\r\n    filterMethod(query) {\r\n      if (!query) {\r\n        this.filteredGameMethods = this.gameMethodsData;\r\n      } else {\r\n        this.filteredGameMethods = this.gameMethodsData.filter(item => {\r\n          return (item.methodName && item.methodName.indexOf(query) !== -1) ||\r\n                 (query === '3' && item.methodName && item.methodName.indexOf('三') !== -1);\r\n        });\r\n      }\r\n    },\r\n    showFormatDialog() {\r\n      this.formatDialogVisible = true;\r\n      this.formatInput = '';\r\n      this.formatOutput = '';\r\n      this.formatTab = 'unified'; // 每次打开都默认显示字符转换\r\n    },\r\n    /** 显示统计对话框 */\r\n    showStatistics() {\r\n      this.statisticsVisible = true;\r\n    },\r\n    /** 验证金额 */\r\n    validateMoney() {\r\n      for (let i = 0; i < this.formData.betGroups.length; i++) {\r\n        const group = this.formData.betGroups[i];\r\n        const money = group.money;\r\n        const groupNumber = i + 1; // 下注单号从1开始\r\n\r\n        // 确保money是字符串类型，防止trim()方法报错\r\n        const moneyStr = money != null ? String(money) : '';\r\n\r\n        // 检查是否为空\r\n        if (!moneyStr || moneyStr.trim() === '') {\r\n          return {\r\n            valid: false,\r\n            message: `第<span style=\"color: #ff4757; font-weight: bold; font-size: 16px;\">${groupNumber}</span>组下注金额不能为<span style=\"color: #ff4757; font-weight: bold; font-size: 16px;\">空</span>`\r\n          };\r\n        }\r\n\r\n        // 检查是否为数字\r\n        const numMoney = Number(moneyStr.trim());\r\n        if (isNaN(numMoney) || numMoney <= 0) {\r\n          return {\r\n            valid: false,\r\n            message: `第<span style=\"color: #ff4757; font-weight: bold; font-size: 16px;\">${groupNumber}</span>组下注金额不能为<span style=\"color: #ff4757; font-weight: bold; font-size: 16px;\">\"${moneyStr}\"</span>，请修改`\r\n          };\r\n        }\r\n      }\r\n\r\n      return {\r\n        valid: true,\r\n        message: ''\r\n      };\r\n    },\r\n    /** 检查统计权限 */\r\n    async checkStatisticsPermission() {\r\n      try {\r\n        // 调用专门的统计权限API\r\n        const response = await getStatisticsPermission();\r\n\r\n\r\n        if (response && response.code === 200 && response.data !== undefined) {\r\n\r\n          this.hasStatisticsPermission = response.data === '1';\r\n        } else {\r\n \r\n          this.hasStatisticsPermission = false;\r\n        }\r\n\r\n  \r\n      } catch (error) {\r\n        console.error('获取用户权限失败:', error);\r\n        this.hasStatisticsPermission = false;\r\n      }\r\n    },\r\n    handleFormatInput() {\r\n      // 句号tab：所有句号类字符转为-\r\n      let out = (this.formatInput || '').replace(/[。．.]/g, '-');\r\n      // 连续多个短横合并为一个\r\n      out = out.replace(/-+/g, '-');\r\n      // 先替换所有汉字数字为阿拉伯数字\r\n      let lines = out.split('\\n').map(line => this.replaceChineseNumber(line));\r\n      lines = this.autoConvertChineseMoney(lines);\r\n      this.formatOutput = lines.filter(line => line).join('\\n');\r\n    },\r\n    insertFormatResult() {\r\n      this.formData.shibie = this.formatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.formatOutput);\r\n    },\r\n    handleColonFormatInput() {\r\n      // 冒号tab：所有冒号类字符转为-\r\n      let out = (this.colonFormatInput || '').replace(/[：:]/g, '-');\r\n      // 连续多个短横合并为一个\r\n      out = out.replace(/-+/g, '-');\r\n      // 先替换所有汉字数字为阿拉伯数字\r\n      let lines = out.split('\\n').map(line => this.replaceChineseNumber(line));\r\n      lines = this.autoConvertChineseMoney(lines);\r\n      this.colonFormatOutput = lines.filter(line => line).join('\\n');\r\n    },\r\n    insertColonFormatResult() {\r\n      this.formData.shibie = this.colonFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.colonFormatOutput);\r\n    },\r\n\r\n    handleMoneyFormatInput() {\r\n      // 只做汉字金额转数字，且只替换最后一个汉字金额为/数字\r\n      let lines = (this.moneyFormatInput || '').split('\\n').map(line => this.replaceChineseNumber(line));\r\n      const chineseMoneyReg = /([一二三四五六七八九十百千万两零]+)(元|块|米)?/g;\r\n      lines = lines.map(line => {\r\n        let lastMatch;\r\n        let match;\r\n        while ((match = chineseMoneyReg.exec(line)) !== null) {\r\n          lastMatch = match;\r\n        }\r\n        if (lastMatch) {\r\n          const num = this.chineseToNumber(lastMatch[1]);\r\n          const unit = lastMatch[2] || '';\r\n          const start = lastMatch.index;\r\n          const end = start + lastMatch[0].length;\r\n          let prefix = line.slice(0, start);\r\n          if (!/[\\/\\s,，.。-]+$/.test(prefix)) {\r\n            prefix += '/';\r\n          }\r\n          return prefix + num + unit + line.slice(end);\r\n        }\r\n        return line;\r\n      });\r\n      this.moneyFormatOutput = lines.filter(line => line).join('\\n');\r\n    },\r\n    insertMoneyFormatResult() {\r\n      this.formData.shibie = this.moneyFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.moneyFormatOutput);\r\n    },\r\n    handleUnifiedFormatInput() {\r\n      // 统一字符转换：智能识别并转换各种分隔符和汉字金额\r\n      let out = this.unifiedFormatInput || '';\r\n\r\n      // 1. 先处理汉字金额转换（在其他转换之前）\r\n      let lines = out.split('\\n').map(line => line.trim()).filter(Boolean);\r\n      lines = this.autoConvertChineseMoney(lines);\r\n      out = lines.join('\\n');\r\n\r\n      // 2. 替换剩余的汉字数字为阿拉伯数字（不包括已转换的金额）\r\n      out = this.replaceChineseNumber(out);\r\n\r\n      // 3. 转换各种分隔符为空格（支持一个或多个连续字符）\r\n      // 逗号、句号、冒号、下划线都转为空格\r\n      out = out.replace(/[,，]+/g, ' ');        // 逗号（中英文）\r\n      out = out.replace(/[。．.]+/g, ' ');       // 句号（中英文）\r\n      out = out.replace(/[：:]+/g, ' ');        // 冒号（中英文）\r\n      out = out.replace(/_+/g, ' ');            // 下划线\r\n\r\n      // 4. 处理加号类字符（转换为空格）\r\n      out = out.replace(/[➕＋﹢✚✛✜✙❇️❌❎]+/g, ' ');\r\n\r\n      // 5. 合并多个连续空格为一个\r\n      out = out.replace(/\\s+/g, ' ');\r\n\r\n      this.unifiedFormatOutput = out.split('\\n').map(line => line.trim()).filter(Boolean).join('\\n');\r\n    },\r\n    insertUnifiedFormatResult() {\r\n      this.formData.shibie = this.unifiedFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.unifiedFormatOutput);\r\n    },\r\n    handleChainFormatInput() {\r\n      // 链子分类：按号码长度分组到不同行\r\n      let input = this.chainFormatInput || '';\r\n\r\n      // 1. 先进行基础的字符转换\r\n      input = this.replaceChineseNumber(input);\r\n\r\n      // 2. 提取所有数字（支持各种分隔符）\r\n      // 使用正则匹配所有连续的数字\r\n      const numberPattern = /\\d+/g;\r\n      const numbers = input.match(numberPattern) || [];\r\n\r\n      // 3. 按长度分组，同时记录每个长度首次出现的位置\r\n      const lengthGroups = {};\r\n      const lengthFirstIndex = {};\r\n\r\n      numbers.forEach((num, index) => {\r\n        const length = num.length;\r\n        if (!lengthGroups[length]) {\r\n          lengthGroups[length] = [];\r\n          lengthFirstIndex[length] = index; // 记录该长度首次出现的位置\r\n        }\r\n        lengthGroups[length].push(num);\r\n      });\r\n\r\n      // 4. 按首次出现的顺序排序并生成输出\r\n      const sortedLengths = Object.keys(lengthGroups).sort((a, b) => lengthFirstIndex[a] - lengthFirstIndex[b]);\r\n      const resultLines = [];\r\n\r\n      sortedLengths.forEach(length => {\r\n        const nums = lengthGroups[length];\r\n        if (nums.length > 0) {\r\n          // 每行格式：直接显示号码列表，不加长度标识\r\n          const line = nums.join(' ');\r\n          resultLines.push(line);\r\n        }\r\n      });\r\n\r\n      this.chainFormatOutput = resultLines.join('\\n');\r\n    },\r\n    insertChainFormatResult() {\r\n      this.formData.shibie = this.chainFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.chainFormatOutput);\r\n    },\r\n    handleAmountFormatInput() {\r\n      // 金额整合：将相同金额且相同位数的号码合并到一行\r\n      // 支持每行一个号码，也支持一行内用空格分隔的多个号码\r\n      const input = this.amountFormatInput || '';\r\n      const lines = input.split('\\n').filter(line => line.trim());\r\n\r\n      // 解析每行的号码和金额\r\n      const groups = {};\r\n      const groupOrder = []; // 记录分组首次出现的顺序\r\n\r\n      lines.forEach(line => {\r\n        const trimmedLine = line.trim();\r\n        if (!trimmedLine) return;\r\n\r\n        // 先尝试按空格分割，看是否有多个号码-金额对\r\n        const parts = trimmedLine.split(/\\s+/).filter(part => part.trim());\r\n\r\n        // 如果只有一个部分，按原来的逻辑处理\r\n        if (parts.length === 1) {\r\n          this.processAmountFormatItem(parts[0], groups, groupOrder);\r\n        } else {\r\n          // 如果有多个部分，逐个处理每个号码-金额对\r\n          parts.forEach(part => {\r\n            this.processAmountFormatItem(part, groups, groupOrder);\r\n          });\r\n        }\r\n      });\r\n\r\n      // 按首次出现的顺序生成输出\r\n      const resultLines = [];\r\n      groupOrder.forEach(groupKey => {\r\n        const group = groups[groupKey];\r\n        if (group && group.numbers.length > 0) {\r\n          // 合并相同金额和位数的号码到一行\r\n          const line = group.numbers.join('-') + '-' + group.amount;\r\n          resultLines.push(line);\r\n        }\r\n      });\r\n\r\n      this.amountFormatOutput = resultLines.join('\\n');\r\n    },\r\n\r\n    // 新增辅助函数：处理单个号码-金额项\r\n    processAmountFormatItem(item, groups, groupOrder) {\r\n      const trimmedItem = item.trim();\r\n      if (!trimmedItem) return;\r\n\r\n      // 查找最后一个短横线，分离号码和金额\r\n      const lastDashIndex = trimmedItem.lastIndexOf('-');\r\n      if (lastDashIndex === -1) return; // 没有找到分隔符，跳过\r\n\r\n      const number = trimmedItem.substring(0, lastDashIndex).trim();\r\n      const amount = trimmedItem.substring(lastDashIndex + 1).trim();\r\n\r\n      if (!number || !amount) return; // 号码或金额为空，跳过\r\n\r\n      // 按金额和号码位数分组\r\n      const numberLength = number.length;\r\n      const groupKey = `${amount}_${numberLength}`; // 金额_位数作为分组键\r\n\r\n      if (!groups[groupKey]) {\r\n        groups[groupKey] = {\r\n          amount: amount,\r\n          length: numberLength,\r\n          numbers: []\r\n        };\r\n        groupOrder.push(groupKey); // 记录分组首次出现的顺序\r\n      }\r\n      groups[groupKey].numbers.push(number);\r\n    },\r\n    insertAmountFormatResult() {\r\n      this.formData.shibie = this.amountFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.amountFormatOutput);\r\n    },\r\n    handleRemoveFormatInput() {\r\n      // 去除分隔符和英文字母：只保留汉字、数字和换行\r\n      const input = this.removeFormatInput || '';\r\n\r\n      // 使用正则表达式只保留汉字、数字和换行符\r\n      // \\u4e00-\\u9fff 匹配汉字\r\n      // \\d 匹配数字\r\n      // \\n\\r 匹配换行符\r\n      // 去除所有英文字母（大小写）和其他字符\r\n      const output = input.replace(/[^\\u4e00-\\u9fff\\d\\n\\r]/g, '');\r\n\r\n      // 清理多余的空行，但保留必要的换行\r\n      this.removeFormatOutput = output.split('\\n')\r\n        .map(line => line.trim())\r\n        .filter((line, index, arr) => {\r\n          // 保留非空行，以及前一行非空的空行（作为分隔）\r\n          return line || (index > 0 && arr[index - 1]);\r\n        })\r\n        .join('\\n')\r\n        .replace(/\\n{3,}/g, '\\n\\n'); // 最多保留两个连续换行\r\n    },\r\n    insertRemoveFormatResult() {\r\n      this.formData.shibie = this.removeFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.removeFormatOutput);\r\n    },\r\n    handleUnderscoreFormatInput() {\r\n      // 下划线tab：将一个或多个连续的下划线转换为空格\r\n      let out = (this.underscoreFormatInput || '').replace(/_+/g, ' ');\r\n      // 先替换所有汉字数字为阿拉伯数字\r\n      let lines = out.split('\\n').map(line => this.replaceChineseNumber(line));\r\n      lines = this.autoConvertChineseMoney(lines);\r\n      this.underscoreFormatOutput = lines.filter(line => line).join('\\n');\r\n    },\r\n    insertUnderscoreFormatResult() {\r\n      this.formData.shibie = this.underscoreFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.underscoreFormatOutput);\r\n    },\r\n    // 工具函数：替换所有汉字数字为阿拉伯数字\r\n    replaceChineseNumber(str) {\r\n      // 先处理完整的中文数字（包括单位），然后再处理单个汉字数字\r\n      // 匹配完整的中文数字表达式，如\"二十五\"、\"一百二十三\"等\r\n      const chineseNumberPattern = /[零一二两三四五六七八九十百千万]+/g;\r\n\r\n      return str.replace(chineseNumberPattern, (match) => {\r\n        // 如果匹配的是单个字符且不包含单位，使用简单映射\r\n        if (match.length === 1 && !/[十百千万]/.test(match)) {\r\n          const map = { '零':0,'一':1,'二':2,'两':2,'三':3,'四':4,'五':5,'六':6,'七':7,'八':8,'九':9 };\r\n          return map[match] || match;\r\n        }\r\n        // 否则使用完整的中文数字转换\r\n        return this.chineseToNumber(match);\r\n      });\r\n    },\r\n    chineseToNumber(chinese) {\r\n      if (!chinese) return 0;\r\n\r\n      // 先判断是否为纯数字\r\n      if (/^\\d+$/.test(chinese)) {\r\n        return parseInt(chinese, 10);\r\n      }\r\n\r\n      // 移除单位\r\n      const str = chinese.replace(/元|块|米/g, '');\r\n\r\n      // 数字映射\r\n      const numMap = {\r\n        '零': 0, '一': 1, '二': 2, '两': 2, '三': 3, '四': 4,\r\n        '五': 5, '六': 6, '七': 7, '八': 8, '九': 9\r\n      };\r\n\r\n      // 单位映射\r\n      const unitMap = { '十': 10, '百': 100, '千': 1000, '万': 10000 };\r\n\r\n      let result = 0;\r\n      let temp = 0;\r\n      let hasNum = false;\r\n\r\n      for (let i = 0; i < str.length; i++) {\r\n        const char = str[i];\r\n\r\n        if (numMap.hasOwnProperty(char)) {\r\n          // 是数字\r\n          temp = numMap[char];\r\n          hasNum = true;\r\n        } else if (unitMap.hasOwnProperty(char)) {\r\n          // 是单位\r\n          const unit = unitMap[char];\r\n\r\n          if (char === '十' && !hasNum) {\r\n            // \"十\"前面没有数字，如\"十五\"，十前面默认为1\r\n            temp = 1;\r\n          }\r\n\r\n          if (unit === 10000) {\r\n            // 万\r\n            result = (result + temp) * unit;\r\n            temp = 0;\r\n          } else if (unit >= 100) {\r\n            // 百、千\r\n            result += temp * unit;\r\n            temp = 0;\r\n          } else {\r\n            // 十\r\n            result += temp * unit;\r\n            temp = 0;\r\n          }\r\n          hasNum = false;\r\n        }\r\n      }\r\n\r\n      // 处理最后剩余的数字\r\n      result += temp;\r\n\r\n      return result;\r\n    },\r\n    autoConvertChineseMoney(lines) {\r\n      if (typeof lines === 'string') lines = lines.split(/\\n/);\r\n      // 更精确的中文金额正则，支持复杂的中文数字\r\n      const chineseMoneyReg = /([零一二三四五六七八九十百千万两]+)(元|块|米)?/g;\r\n      return lines.map(line => {\r\n        // 查找所有汉字金额\r\n        let lastMatch;\r\n        let match;\r\n        // 重置正则的lastIndex\r\n        chineseMoneyReg.lastIndex = 0;\r\n        while ((match = chineseMoneyReg.exec(line)) !== null) {\r\n          // 验证匹配的是否真的是数字（包含数字字符或单位字符）\r\n          if (/[一二三四五六七八九十百千万两零]/.test(match[1])) {\r\n            lastMatch = match;\r\n          }\r\n        }\r\n        if (lastMatch) {\r\n          const num = this.chineseToNumber(lastMatch[1]);\r\n          const unit = lastMatch[2] || '';\r\n          // 替换最后一个汉字金额为数字\r\n          const start = lastMatch.index;\r\n          const end = start + lastMatch[0].length;\r\n          let prefix = line.slice(0, start);\r\n\r\n          // 确保汉字金额前面有空格，避免和号码混淆\r\n          if (prefix && !/[\\s\\/,，.。-]$/.test(prefix)) {\r\n            prefix += ' ';\r\n          }\r\n\r\n          return prefix + num + unit + line.slice(end);\r\n        }\r\n        return line;\r\n      });\r\n    },\r\n    // 初始化本组序号\r\n    initSerialNumber() {\r\n      // 如果是修改模式（有row数据）且已有流水号，不要重新初始化\r\n      if (this.row && this.row.serialNumber) {\r\n\r\n        this.serialNumber = this.row.serialNumber;\r\n        this.originalSerialNumber = this.row.serialNumber;\r\n        // 仍然获取最大流水号用于生成新流水号时参考\r\n        getMaxSerialNumber().then(max => {\r\n          this.maxSerialNumber = max ? Number(max) : 1;\r\n        }).catch(() => {\r\n          this.maxSerialNumber = 1;\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 新增模式或没有流水号时，获取最大流水号\r\n      \r\n      getMaxSerialNumber().then(max => {\r\n        if (max == null) {\r\n          this.maxSerialNumber = 1;\r\n          this.serialNumber = 1;\r\n        } else {\r\n          this.maxSerialNumber = Number(max);\r\n          this.serialNumber = Number(max);\r\n        }\r\n        \r\n      }).catch(() => {\r\n        this.maxSerialNumber = 1;\r\n        this.serialNumber = 1;\r\n      });\r\n    },\r\n    // 增加本组序号，只能加到最大+1\r\n    increaseSerialNumber() {\r\n      const next = Number(this.maxSerialNumber) + 1;\r\n      if (this.serialNumber < next) {\r\n        this.serialNumber = next;\r\n      }\r\n      // 如果已经是最大+1，则不再增加\r\n    },\r\n    // 生成新的流水号\r\n    generateNewSerialNumber() {\r\n      this.generateLoading = true;\r\n      generateSerialNumber().then(response => {\r\n        if (response.code === 200) {\r\n          this.serialNumber = response.serialNumber;\r\n          this.maxSerialNumber = response.serialNumber;\r\n          this.$message.success(`生成新流水号: ${response.serialNumber}`);\r\n        } else {\r\n          this.$message.error('生成流水号失败: ' + response.msg);\r\n        }\r\n      }).catch(error => {\r\n        console.error('生成流水号失败:', error);\r\n        this.$message.error('生成流水号失败');\r\n      }).finally(() => {\r\n        this.generateLoading = false;\r\n      });\r\n    },\r\n    // 处理用户ID变化\r\n    handleUserIdChange(newUserId, oldUserId) {\r\n    \r\n      if (newUserId === this.originalUserId) {\r\n        // 改回原用户，恢复原流水号\r\n        this.serialNumber = this.originalSerialNumber;\r\n        this.$message.info(`已恢复原流水号: ${this.originalSerialNumber}`);\r\n      } else {\r\n        // 改为其他用户，自动生成新流水号\r\n        this.autoGenerateSerialNumber();\r\n      }\r\n    },\r\n    // 自动生成流水号（不显示成功消息）\r\n    autoGenerateSerialNumber() {\r\n      generateSerialNumber().then(response => {\r\n        if (response.code === 200) {\r\n          this.serialNumber = response.serialNumber;\r\n          this.maxSerialNumber = response.serialNumber;\r\n          this.$message.info(`用户变更，自动生成新流水号: ${response.serialNumber}`);\r\n        } else {\r\n          this.$message.error('自动生成流水号失败: ' + response.msg);\r\n        }\r\n      }).catch(error => {\r\n        console.error('自动生成流水号失败:', error);\r\n        this.$message.error('自动生成流水号失败');\r\n      });\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 使用全局样式 */\r\n.bet-dialog-loading {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n.bet-dialog-loading .el-loading-mask {\r\n  z-index: 9999 !important;\r\n}\r\n</style>\r\n\r\n<style scoped>\r\n/* 现代化弹窗样式 */\r\n.modern-bet-dialog {\r\n  .el-dialog {\r\n    border-radius: 12px;\r\n    overflow: hidden;\r\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\r\n  }\r\n\r\n  .el-dialog__header {\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    padding: 20px 24px;\r\n  }\r\n\r\n  .el-dialog__title {\r\n    color: white;\r\n    font-weight: 600;\r\n    font-size: 18px;\r\n  }\r\n\r\n  .el-dialog__headerbtn .el-dialog__close {\r\n    color: white;\r\n    font-size: 20px;\r\n\r\n    &:hover {\r\n      color: rgba(255, 255, 255, 0.8);\r\n    }\r\n  }\r\n}\r\n\r\n/* 福体开关样式 */\r\n.lottery-switch {\r\n  font-weight: 500;\r\n  margin-left: 8px;\r\n}\r\n\r\n/* 表单样式 */\r\n.modern-bet-form {\r\n  .el-form-item__label {\r\n    font-weight: 500;\r\n    color: #495057;\r\n  }\r\n}\r\n\r\n/* 合并的信息卡片样式 */\r\n.info-card {\r\n  background: white;\r\n  padding: 8px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e9ecef;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-bottom: 6px;\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.info-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.info-label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  font-weight: 500;\r\n  color: #495057;\r\n  font-size: 13px;\r\n  min-width: 60px;\r\n\r\n  i {\r\n    color: #667eea;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n.compact-input {\r\n  .el-input__inner {\r\n    height: 28px;\r\n    font-size: 12px;\r\n    border-radius: 4px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:focus {\r\n      border-color: #667eea;\r\n      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);\r\n    }\r\n  }\r\n}\r\n\r\n.compact-select {\r\n  .el-input__inner {\r\n    height: 28px;\r\n    font-size: 12px;\r\n    border-radius: 4px;\r\n  }\r\n}\r\n\r\n.compact-btn {\r\n  height: 28px;\r\n  padding: 0 8px;\r\n  font-size: 12px;\r\n  border-radius: 4px;\r\n\r\n  i {\r\n    margin-right: 2px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n/* 识别区域样式 */\r\n.recognition-section {\r\n  background: white;\r\n  padding: 6px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e9ecef;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.recognition-wrapper {\r\n  .el-form-item__content {\r\n    position: relative;\r\n  }\r\n}\r\n\r\n.recognition-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  margin-bottom: 6px;\r\n  font-size: 12px;\r\n  color: #495057;\r\n\r\n  i {\r\n    color: #667eea;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .el-tag {\r\n    margin-left: auto;\r\n  }\r\n\r\n  .el-button {\r\n    margin-left: auto;\r\n  }\r\n}\r\n\r\n.recognition-textarea {\r\n  .el-textarea__inner {\r\n    border-radius: 6px;\r\n    border-color: #e9ecef;\r\n    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\r\n    font-size: 12px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:focus {\r\n      border-color: #667eea;\r\n      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);\r\n    }\r\n  }\r\n}\r\n\r\n.option-group {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  padding: 6px 10px;\r\n  background: rgba(248, 249, 250, 0.8);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(233, 236, 239, 0.8);\r\n  backdrop-filter: blur(5px);\r\n  transition: all 0.3s ease;\r\n  min-height: 36px;\r\n\r\n  &:hover {\r\n    background: rgba(248, 249, 250, 1);\r\n    border-color: rgba(102, 126, 234, 0.3);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  }\r\n}\r\n\r\n.option-label {\r\n  min-width: 55px;\r\n  margin-right: 8px;\r\n  font-weight: 600;\r\n  color: #495057;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  flex-shrink: 0;\r\n\r\n  &::before {\r\n    content: '●';\r\n    color: #667eea;\r\n    font-size: 10px;\r\n    animation: pulse 2s infinite;\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    opacity: 0.6;\r\n  }\r\n}\r\n\r\n/* 投注组总额和投注数美化 */\r\n.group-summary {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-left: auto;\r\n}\r\n\r\n.summary-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  padding: 4px 8px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n\r\n  &.amount {\r\n    background: rgba(102, 126, 234, 0.1);\r\n    border: 1px solid rgba(102, 126, 234, 0.2);\r\n\r\n    .summary-icon {\r\n      color: #667eea;\r\n    }\r\n\r\n    .summary-value {\r\n      color: #667eea;\r\n      font-size: 15px;\r\n    }\r\n  }\r\n\r\n  &.bets {\r\n    background: rgba(255, 107, 53, 0.1);\r\n    border: 1px solid rgba(255, 107, 53, 0.2);\r\n\r\n    .summary-icon {\r\n      color: #ff6b35;\r\n    }\r\n\r\n    .summary-value {\r\n      color: #ff6b35;\r\n    }\r\n  }\r\n\r\n  &:hover {\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  }\r\n}\r\n\r\n.summary-icon {\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.summary-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n}\r\n\r\n.summary-label {\r\n  font-size: 10px;\r\n  color: #666;\r\n  line-height: 1;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.summary-value {\r\n  font-size: 12px;\r\n  font-weight: 700;\r\n  line-height: 1;\r\n}\r\n\r\n.el-radio-group {\r\n  display: inline-flex;\r\n  gap: 6px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.el-radio-button {\r\n  margin-right: 0;\r\n\r\n  .el-radio-button__inner {\r\n    border-radius: 6px;\r\n    font-weight: 600;\r\n    transition: all 0.3s ease;\r\n    border-color: #e9ecef;\r\n    padding: 6px 8px;\r\n    font-size: 11px;\r\n    min-width: 65px;\r\n    text-align: center;\r\n\r\n    &:hover {\r\n      border-color: #667eea;\r\n      color: #667eea;\r\n    }\r\n  }\r\n\r\n  /* 福彩3D按钮样式 */\r\n  &[data-lottery=\"fc3d\"] {\r\n    .el-radio-button__inner {\r\n      border-color: #ff6b35;\r\n      color: #ff6b35;\r\n\r\n      &:hover {\r\n        border-color: #ff6b35;\r\n        background: rgba(255, 107, 53, 0.1);\r\n      }\r\n    }\r\n\r\n    &.is-active {\r\n      .el-radio-button__inner {\r\n        background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);\r\n        border-color: #ff6b35;\r\n        color: white;\r\n        box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);\r\n      }\r\n    }\r\n  }\r\n\r\n  /* 体彩排三按钮样式 */\r\n  &[data-lottery=\"tc\"] {\r\n    .el-radio-button__inner {\r\n      border-color: #409eff;\r\n      color: #409eff;\r\n      min-width: 70px;\r\n\r\n      &:hover {\r\n        border-color: #409eff;\r\n        background: rgba(64, 158, 255, 0.1);\r\n      }\r\n    }\r\n\r\n    &.is-active {\r\n      .el-radio-button__inner {\r\n        background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);\r\n        border-color: #409eff;\r\n        color: white;\r\n        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.bet-groups-list-wrapper {\r\n  position: relative;\r\n  margin-bottom: 8px;\r\n  margin-top: 0;\r\n}\r\n\r\n.bet-groups-list {\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  padding: 0 4px;\r\n}\r\n\r\n.bet-group-card {\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  box-shadow: 0 3px 12px 0 rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 8px;\r\n  padding: 10px 14px;\r\n  border: 2px solid #e9ecef;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 6px;\r\n    height: 100%;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  }\r\n\r\n  &::after {\r\n   \r\n    position: absolute;\r\n    top: 8px;\r\n    right: 12px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    padding: 4px 10px;\r\n    border-radius: 15px;\r\n    font-size: 11px;\r\n    font-weight: 700;\r\n    z-index: 10;\r\n    box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);\r\n  }\r\n}\r\n\r\n.bet-group-card:hover {\r\n  box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.15);\r\n  transform: translateY(-2px);\r\n  border-color: #667eea;\r\n}\r\n\r\n.bet-group-even {\r\n  background: linear-gradient(135deg, #fff8f5 0%, #fef5f0 100%) !important;\r\n  border-color: #ffb8a1 !important;\r\n\r\n  &::before {\r\n    background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);\r\n  }\r\n\r\n  &::after {\r\n    \r\n    background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);\r\n    box-shadow: 0 2px 6px rgba(255, 107, 53, 0.3);\r\n  }\r\n\r\n  &:hover {\r\n    box-shadow: 0 6px 20px 0 rgba(255, 107, 53, 0.2);\r\n    border-color: #ff6b35;\r\n  }\r\n}\r\n\r\n.bet-group {\r\n  background: #f5f7fa;\r\n  border-radius: 4px;\r\n  padding: 15px;\r\n  margin-bottom: 15px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.bet-group:hover {\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);\r\n}\r\n\r\n.bet-group-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #dcdfe6;\r\n}\r\n\r\n.bet-group-content {\r\n  margin-left: 10px;\r\n}\r\n\r\n.bet-group-footer {\r\n  margin-top: 10px;\r\n  padding-top: 10px;\r\n  border-top: 1px solid #dcdfe6;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.el-form-item--small.el-form-item {\r\n  margin-bottom: 3px;\r\n}\r\n\r\n.modern-bet-form .el-form-item {\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.el-textarea__inner {\r\n  font-family: monospace;\r\n}\r\n\r\n.el-input-number--small {\r\n  width: 130px;\r\n}\r\n\r\n.total-info {\r\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);\r\n  padding: 6px 12px;\r\n  border-radius: 6px;\r\n  box-shadow: 0 1px 6px 0 rgba(102, 126, 234, 0.1);\r\n  margin-top: 4px;\r\n  border: 1px solid rgba(102, 126, 234, 0.2);\r\n  position: relative;\r\n  font-size: 13px;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 2px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    border-radius: 6px 6px 0 0;\r\n  }\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center;\r\n  \r\n  /* background: #f8f9fa; */\r\n  border-radius: 0 0 6px 6px;\r\n  margin: 4px -6px -6px -6px;\r\n}\r\n\r\n.dialog-footer .el-button {\r\n  min-width: 80px;\r\n  margin: 0 6px;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n  font-size: 13px;\r\n  height: 32px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-1px);\r\n  }\r\n\r\n  &.el-button--primary {\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    border: none;\r\n\r\n    &:hover {\r\n      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\r\n    }\r\n  }\r\n}\r\n\r\n:deep(.bet-dialog-loading) {\r\n  z-index: 3000 !important;\r\n}\r\n\r\n.bet-group-add-btn {\r\n  position: absolute;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  bottom: -12px;\r\n  width: 28px;\r\n  height: 28px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  border: 2px solid white;\r\n  box-shadow: 0 2px 8px 0 rgba(102, 126, 234, 0.3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  z-index: 20;\r\n  transition: all 0.3s ease;\r\n\r\n  &::before {\r\n    content: '+';\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.bet-group-add-btn:hover {\r\n  background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);\r\n  transform: translateX(-50%) translateY(-1px) scale(1.05);\r\n  box-shadow: 0 4px 12px 0 rgba(102, 126, 234, 0.4);\r\n}\r\n.el-dialog__body{\r\n  margin-top: -30px;\r\n}\r\n\r\n/* 简洁格式转换弹窗样式 */\r\n.simple-format-dialog {\r\n  .el-textarea__inner {\r\n    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\r\n    font-size: 13px;\r\n  }\r\n\r\n  .el-form-item {\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  .el-button {\r\n    margin-right: 8px;\r\n  }\r\n}\r\n\r\n/* 号码数量超限样式 */\r\n.number-count-exceeded .el-textarea__inner {\r\n  border-color: #f56c6c !important;\r\n  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;\r\n}\r\n\r\n.number-count-exceeded .el-textarea__inner:focus {\r\n  border-color: #f56c6c !important;\r\n  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.3) !important;\r\n}\r\n\r\n/* 格式转换弹窗美化样式 */\r\n.format-dialog {\r\n  .el-dialog__header {\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    padding: 20px 24px;\r\n    border-radius: 8px 8px 0 0;\r\n\r\n    .el-dialog__title {\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      color: white;\r\n    }\r\n\r\n    .el-dialog__close {\r\n      color: white;\r\n      font-size: 20px;\r\n\r\n      &:hover {\r\n        color: #f0f0f0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.format-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.format-tabs {\r\n  .el-tabs__header {\r\n    margin: 0 0 20px 0;\r\n    background: white;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    padding: 12px;\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n\r\n  .el-tabs__nav {\r\n    border: none;\r\n    display: flex;\r\n    justify-content: center;\r\n    width: 100%;\r\n  }\r\n\r\n  .el-tabs__item {\r\n    border: none;\r\n    border-radius: 6px;\r\n    margin-right: 8px;\r\n    padding: 12px 24px;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n    min-width: 120px;\r\n    text-align: center;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 44px;\r\n\r\n    &:hover {\r\n      background: #e3f2fd;\r\n      color: #1976d2;\r\n    }\r\n\r\n    &.is-active {\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      color: white;\r\n      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n    }\r\n\r\n    i {\r\n      margin-right: 6px;\r\n      font-size: 16px;\r\n      display: inline-block;\r\n      vertical-align: middle;\r\n    }\r\n\r\n    span {\r\n      display: inline-block;\r\n      vertical-align: middle;\r\n    }\r\n  }\r\n\r\n  .el-tabs__content {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.tab-content {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e8eaed;\r\n}\r\n\r\n.input-section, .output-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  font-size: 14px;\r\n\r\n  i {\r\n    margin-right: 8px;\r\n    font-size: 16px;\r\n    color: #667eea;\r\n  }\r\n}\r\n\r\n.arrow-section {\r\n  text-align: center;\r\n  margin: 16px 0;\r\n\r\n  i {\r\n    font-size: 24px;\r\n    color: #667eea;\r\n    animation: bounce 2s infinite;\r\n  }\r\n}\r\n\r\n@keyframes bounce {\r\n  0%, 20%, 50%, 80%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  40% {\r\n    transform: translateY(-8px);\r\n  }\r\n  60% {\r\n    transform: translateY(-4px);\r\n  }\r\n}\r\n\r\n.format-input, .format-output {\r\n  .el-textarea__inner {\r\n    border-radius: 8px;\r\n    border: 2px solid #e8eaed;\r\n    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\r\n    font-size: 13px;\r\n    line-height: 1.6;\r\n    transition: all 0.3s ease;\r\n\r\n    &:focus {\r\n      border-color: #667eea;\r\n      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n    }\r\n\r\n    &::placeholder {\r\n      color: #999;\r\n      font-style: italic;\r\n    }\r\n  }\r\n}\r\n\r\n.format-output {\r\n  .el-textarea__inner {\r\n    background: #f8f9fa;\r\n    color: #2d3748;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.button-section {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 12px;\r\n  margin-top: 24px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #e8eaed;\r\n}\r\n\r\n.action-button {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 8px;\r\n  padding: 12px 24px;\r\n  font-weight: 600;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);\r\n  }\r\n\r\n  &:disabled {\r\n    background: #d1d5db;\r\n    box-shadow: none;\r\n    transform: none;\r\n  }\r\n}\r\n\r\n.cancel-button {\r\n  border: 2px solid #e8eaed;\r\n  border-radius: 8px;\r\n  padding: 12px 24px;\r\n  font-weight: 500;\r\n  background: white;\r\n  color: #6b7280;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    border-color: #667eea;\r\n    color: #667eea;\r\n    background: #f8f9ff;\r\n  }\r\n}\r\n</style>"]}]}