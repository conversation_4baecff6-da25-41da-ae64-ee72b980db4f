<template>
  <div>
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" append-to-body
      :close-on-click-modal="false" class="modern-bet-dialog" style="height: 108%;  top: -55px;">

      <el-form ref="form" :model="formData" :rules="rules" label-width="80px" class="modern-bet-form">
        <div class="info-card">
          <!-- 福体开关行 -->
          <div class="info-row">
            <div class="info-group">
              <div class="info-label">
                <i class="el-icon-s-operation"></i>
                <span>福体开关</span>
              </div>
              <el-switch v-model="futiSwitch" active-value="all" inactive-value="tc" active-text="全部彩种" inactive-text="仅体彩"
                @change="onFutiSwitchChange"
                size="small"
                class="lottery-switch" />
            </div>
          </div>

          <!-- 用户信息行 -->
          <div class="info-row">
            <div class="info-group">
              <div class="info-label">
                <i class="el-icon-user"></i>
                <span>当前用户</span>
              </div>
              <el-input v-model="currentUser.name" disabled placeholder="未获取" class="compact-input" style="width: 100px;" />
            </div>
            <div class="info-group">
              <div class="info-label">
                <i class="el-icon-switch-button"></i>
                <span>切换用户</span>
              </div>
              <el-select v-model="selectedUserId" placeholder="请选择用户" class="compact-select" style="width: 120px;">
                <el-option v-for="user in userList" :key="user.userId" :label="user.name" :value="user.userId" />
              </el-select>
              <el-button
                type="primary"
                :disabled="!selectedUserId"
                @click="switchUser"
                v-if="!row"
                class="compact-btn"
              >切换</el-button>
            </div>
          </div>

          <!-- 流水号信息行 -->
          <div class="info-row">
            <div class="info-group">
              <div class="info-label">
                <i class="el-icon-document"></i>
                <span>流水号</span>
              </div>
              <el-input v-model.number="serialNumber" placeholder="流水号" class="compact-input" style="width: 120px;" />
              <el-button type="success" @click="generateNewSerialNumber" :loading="generateLoading" class="compact-btn">
                <i class="el-icon-refresh"></i>
                生成新流水号
              </el-button>
              <el-button type="primary" @click="showFormatDialog" class="compact-btn">
                <i class="el-icon-s-operation"></i>
                格式转换工具
              </el-button>
              <el-button
                v-if="showStatisticsButton"
                type="success"
                @click="showStatistics"
                class="compact-btn">
                <i class="el-icon-data-analysis"></i>
                下注统计
              </el-button>
            </div>
          </div>

          <!-- 隐藏的期号字段，用于后端传输 -->
          <div style="display: none;">
            <el-input v-model="formData.fc3dIssueNumber" />
            <el-input v-model="formData.tcIssueNumber" />
          </div>
        </div>
        <div class="recognition-section">
          <el-form-item label="号码识别" prop="shibie">
            <div class="recognition-wrapper">
              <div class="recognition-header">
                <i class="el-icon-view"></i>
                <span v-if="!isNumberCountExceeded">智能识别</span>
                <span v-else-if="isFirstNumberThreeDigits(formData.shibie)" style="color: #67c23a;">首组为三位数，不限制组数 ({{ numberCount }}组)</span>
                <span v-else style="color: #f56c6c;">单次识别不可超过30组，你现在是{{ numberCount }}组</span>
                <el-button size="mini" type="primary" @click="showBetFormatDialog">玩法格式</el-button>
              </div>
              <el-input v-model="formData.shibie" type="textarea" :rows="4"
                placeholder="请将号码输入此处，识别后自动生成下注号码"
                @input="handleNumberRecognition" size="small" :resize="'vertical'"
                :class="['recognition-textarea', { 'number-count-exceeded': isNumberCountExceeded }]" />
            </div>
          </el-form-item>
        </div>

        <div class="bet-groups-list-wrapper">
          <div class="bet-groups-list">
            <div v-for="(group, idx) in formData.betGroups" :key="idx"
              :class="['bet-group-card', { 'bet-group-even': idx % 2 === 1 }]">
              <div style="display:flex;align-items:center;gap:8px;margin-bottom:8px;">
                <el-form-item :label="'玩法名称'" :prop="'betGroups.' + idx + '.methodId'" style="flex:1;margin-bottom:0;">
                  <el-select v-model="group.methodId"
                             filterable
                             remote
                             :remote-method="filterMethod"
                             :loading="loadingGameMethods"
                             clearable
                             placeholder="请选择玩法名称"
                             size="small"
                             style="width:100%"
                             @change="onMethodChange(group)">
                    <el-option v-for="item in filteredGameMethods"
                               :key="item.methodId"
                               :label="item.methodName"
                               :value="item.methodId">
                      {{ item.methodName }}
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item :label="'金额'" :prop="'betGroups.' + idx + '.money'" style="width:200px;margin-bottom:0;">
                  <el-input v-model="group.money" placeholder="请输入金额" size="small" @input="updateTotals" />
                </el-form-item>
                <el-button type="danger" icon="el-icon-delete" @click="removeBetGroup(idx)" circle size="mini" />
              </div>

              <div
                v-if="group.methodId === 29 || group.methodId === 30 || group.methodId === 2 || [21, 22, 23, 24, 25, 26, 27, 37, 38, 39, 40, 41, 42, 43].includes(group.methodId)"
                style="margin-left: 80px; margin-bottom: 8px;">
                <template v-if="group.methodId === 29">
                  <div class="option-group">
                    <span class="option-label" style="font-size: 16px;font-weight: 600;">单双：</span>
                    <el-radio-group v-model="group.danshuang" size="medium">
                      <el-radio-button :label="200">单</el-radio-button>
                      <el-radio-button :label="201">双</el-radio-button>
                    </el-radio-group>
                  </div>
                </template>
                <template v-else-if="group.methodId === 30">
                  <div class="option-group">
                    <span class="option-label" style="font-size: 16px;font-weight: 600;">大小：</span>
                    <el-radio-group v-model="group.daxiao" size="medium">
                      <el-radio-button :label="300">大</el-radio-button>
                      <el-radio-button :label="301">小</el-radio-button>
                    </el-radio-group>
                  </div>
                </template>
                <template v-else-if="group.methodId === 2">
                  <div class="option-group">
                    <span class="option-label" style="font-size: 16px;font-weight: 600;">定位：</span>
                    <el-radio-group v-model="group.dingwei" size="medium">
                      <el-radio-button :label="100">百位</el-radio-button>
                      <el-radio-button :label="101">十位</el-radio-button>
                      <el-radio-button :label="102">个位</el-radio-button>
                    </el-radio-group>
                  </div>
                </template>
                <template v-else-if="[21, 22, 23, 24, 25, 26, 27, 37, 38, 39, 40, 41, 42, 43].includes(group.methodId)">
                  <div class="option-group">
                    <span class="option-label" style="font-size: 16px;font-weight: 600;">和值：</span>
                    <el-radio-group v-model="group.hezhi" size="medium">
                      <el-radio-button v-for="hz in getHezhiOptions(group.methodId)" :key="hz"
                        :label="hz">和值{{ hz }}</el-radio-button>
                    </el-radio-group>
                  </div>
                </template>
              </div>

              <el-form-item :label="'下注号码'" :prop="'betGroups.' + idx + '.betNumbers'" v-if="!isSpecialMethod(group.methodId)"
                style="margin-bottom:8px;">
                <el-input :value="getDisplayBetNumbers(group)" type="textarea" :rows="3" placeholder="请输入下注号码"
                  @input="onBetNumbersInput($event, idx)" />
              </el-form-item>

              <el-form-item label="彩种类型" style="margin-bottom:8px;">
                <div style="display:flex;align-items:center;">
                  <el-radio-group v-model="group.lotteryId" size="small" style="margin-right:16px;flex-shrink:0;"
                    :fill="group.lotteryId === 2 ? '#1890ff' : '#ff4949'">
                    <el-radio-button :label="1" data-lottery="fc3d"
                      style="margin-right:6px;">福彩3D</el-radio-button>
                    <el-radio-button :label="2" data-lottery="tc"
                      style="margin-right:0;">体彩排三</el-radio-button>
                  </el-radio-group>
                  <div class="group-summary">
                    <div class="summary-item amount">
                      <div class="summary-icon">
                        <i class="el-icon-wallet"></i>
                      </div>
                      <div class="summary-content">
                        <div class="summary-label">总额</div>
                        <div class="summary-value">￥{{ getGroupAmount(group) }}</div>
                      </div>
                    </div>
                    <div class="summary-item bets">
                      <div class="summary-icon">
                        <i class="el-icon-tickets"></i>
                      </div>
                      <div class="summary-content">
                        <div class="summary-label">投注数</div>
                        <div class="summary-value">{{ getGroupBets(group) }}注</div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <button class="bet-group-add-btn" @click="addBetGroup" title="添加投注组合">
            <!-- <i class="el-icon-plus"></i> -->
          </button>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-statistic title="下注总额" :value="totalAmount" size="small">
              <template slot="prefix">￥</template>
            </el-statistic>
          </el-col>
          <el-col :span="12">
            <el-statistic title="总投注数" :value="totalBets" size="small">
              <template slot="suffix">注</template>
            </el-statistic>
          </el-col>
        </el-row>

        <el-form-item label="是否结算" prop="jiesuan" v-if="dialogTitle === '修改下注管理'">
          <el-switch v-model="formData.jiesuan" :active-value="1" :inactive-value="0" active-text="已结算"
            inactive-text="未结算" size="small" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <div style="display: flex; align-items: center; justify-content: center; width: 100%;">
          <el-button type="primary" @click="submitForm" size="small">确 定</el-button>
          <el-button @click="close" size="small">取 消</el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      title="格式转换工具"
      :visible.sync="formatDialogVisible"
      width="650px"
      append-to-body
      class="format-dialog"
      :close-on-click-modal="false">

      <div class="format-dialog-content">
        <el-tabs v-model="formatTab" type="card" class="format-tabs">
          <el-tab-pane name="unified">
            <span slot="label">
              <i class="el-icon-edit"></i>
              字符转换
            </span>
            <div class="tab-content">
              <div class="input-section">
                <div class="section-header">
                  <i class="el-icon-upload2"></i>
                  <span>输入内容</span>
                </div>
                <el-input
                  type="textarea"
                  :rows="5"
                  v-model="unifiedFormatInput"
                  placeholder="智能识别并转换：汉字金额(如二十)、逗号、句号、冒号、下划线、加号等转为空格&#10;示例：123,456。。789：：：012___345➕678二十元"
                  @input="handleUnifiedFormatInput"
                  class="format-input"
                />
              </div>

              <div class="arrow-section">
                <i class="el-icon-bottom"></i>
              </div>

              <div class="output-section">
                <div class="section-header">
                  <i class="el-icon-download"></i>
                  <span>转换结果</span>
                </div>
                <el-input
                  type="textarea"
                  :rows="5"
                  :value="unifiedFormatOutput"
                  readonly
                  placeholder="转换结果将在这里显示..."
                  class="format-output"
                />
              </div>

              <div class="button-section">
                <el-button
                  type="primary"
                  icon="el-icon-check"
                  @click="insertUnifiedFormatResult"
                  :disabled="!unifiedFormatOutput"
                  class="action-button">
                  插入到号码识别
                </el-button>
                <el-button
                  icon="el-icon-close"
                  @click="formatDialogVisible = false"
                  class="cancel-button">
                  关闭
                </el-button>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane name="chain">
            <span slot="label">
              <i class="el-icon-s-grid"></i>
              链子分类
            </span>
            <div class="tab-content">
              <div class="input-section">
                <div class="section-header">
                  <i class="el-icon-upload2"></i>
                  <span>输入内容</span>
                </div>
                <el-input
                  type="textarea"
                  :rows="5"
                  v-model="chainFormatInput"
                  placeholder="按号码长度自动分类到不同行
示例：123-1234-568-56789-503-9012-12345678"
                  @input="handleChainFormatInput"
                  class="format-input"
                />
              </div>

              <div class="arrow-section">
                <i class="el-icon-bottom"></i>
              </div>

              <div class="output-section">
                <div class="section-header">
                  <i class="el-icon-download"></i>
                  <span>分类结果</span>
                </div>
                <el-input
                  type="textarea"
                  :rows="6"
                  :value="chainFormatOutput"
                  readonly
                  placeholder="分类结果将在这里显示..."
                  class="format-output"
                />
              </div>

              <div class="button-section">
                <el-button
                  type="primary"
                  icon="el-icon-check"
                  @click="insertChainFormatResult"
                  :disabled="!chainFormatOutput"
                  class="action-button">
                  插入到号码识别
                </el-button>
                <el-button
                  icon="el-icon-close"
                  @click="formatDialogVisible = false"
                  class="cancel-button">
                  关闭
                </el-button>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane name="amount">
            <span slot="label">
              <i class="el-icon-money"></i>
              金额整合
            </span>
            <div class="tab-content">
              <div class="input-section">
                <div class="section-header">
                  <i class="el-icon-upload2"></i>
                  <span>输入内容</span>
                </div>
                <el-input
                  type="textarea"
                  :rows="6"
                  v-model="amountFormatInput"
                  placeholder="将相同金额且相同位数的号码合并到一行
  支持每行一个或一行内空格分隔多个
  示例：57-50  59-50  79-50  88-100
  结果： 57-59-79-50
        88-100"
                  @input="handleAmountFormatInput"
                  class="format-input"
                />
              </div>

              <div class="arrow-section">
                <i class="el-icon-bottom"></i>
              </div>

              <div class="output-section">
                <div class="section-header">
                  <i class="el-icon-download"></i>
                  <span>整合结果</span>
                </div>
                <el-input
                  type="textarea"
                  :rows="6"
                  :value="amountFormatOutput"
                  readonly
                  placeholder="整合结果将在这里显示..."
                  class="format-output"
                />
              </div>

              <div class="button-section">
                <el-button
                  type="primary"
                  icon="el-icon-check"
                  @click="insertAmountFormatResult"
                  :disabled="!amountFormatOutput"
                  class="action-button">
                  插入到号码识别
                </el-button>
                <el-button
                  icon="el-icon-close"
                  @click="formatDialogVisible = false"
                  class="cancel-button">
                  关闭
                </el-button>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane name="remove">
            <span slot="label">
              <i class="el-icon-delete"></i>
              去除分隔符
            </span>
            <div class="tab-content">
              <div class="input-section">
                <div class="section-header">
                  <i class="el-icon-upload2"></i>
                  <span>输入内容</span>
                </div>
                <el-input
                  type="textarea"
                  :rows="6"
                  v-model="removeFormatInput"
                  placeholder="去除所有分隔符和英文字母，只保留汉字、数字和换行
示例：123,456。。789：：：abc___def➕ghi二十元ABC
结果：123456789二十元"
                  @input="handleRemoveFormatInput"
                  class="format-input"
                />
              </div>

              <div class="arrow-section">
                <i class="el-icon-bottom"></i>
              </div>

              <div class="output-section">
                <div class="section-header">
                  <i class="el-icon-download"></i>
                  <span>清理结果</span>
                </div>
                <el-input
                  type="textarea"
                  :rows="6"
                  :value="removeFormatOutput"
                  readonly
                  placeholder="清理结果将在这里显示..."
                  class="format-output"
                />
              </div>

              <div class="button-section">
                <el-button
                  type="primary"
                  icon="el-icon-check"
                  @click="insertRemoveFormatResult"
                  :disabled="!removeFormatOutput"
                  class="action-button">
                  插入到号码识别
                </el-button>
                <el-button
                  icon="el-icon-close"
                  @click="formatDialogVisible = false"
                  class="cancel-button">
                  关闭
                </el-button>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 下注统计组件 -->
    <bet-statistics :visible.sync="statisticsVisible" />

    <!-- 玩法格式弹窗组件 -->
    <bet-format-dialog ref="betFormatDialog" />
  </div>
</template>

<script>
import { handleNumberRecognition, handleSmartRecognition } from './numberRecognition.js';
import request from '@/utils/request';
import { getMaxSerialNumber } from '@/api/game/serial'
import { generateSerialNumber } from '@/api/game/record'
import { getCurrentQihao } from '@/api/game/qihao'
import { checkAndSetDefaultPlayer } from '@/api/game/customer'
import { getStatisticsPermission } from '@/api/system/user'
import BetStatistics from './BetStatistics.vue'
import BetFormatDialog from './BetFormatDialog.vue'

export default {
  name: 'BetDialog',
  components: {
    BetStatistics,
    BetFormatDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '新增下注'
    },
    row: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '',
      totalAmount: 0,
      totalBets: 0,
      pendingRequests: [],
      gameMethodsData: [], // 玩法数据
      filteredGameMethods: [], // 用于 remote 搜索的玩法数据
      loadingGameMethods: false, // 玩法加载状态
      formData: {
        fc3dIssueNumber: '',  // 福彩3D期号
        tcIssueNumber: '',    // 体彩排三期号
        shibie: '',
        betGroups: [{
          methodId: undefined,
          money: '',
          betNumbers: '',
          danshuang: null,
          daxiao: null,
          dingwei: null,
          hezhi: null,
          lotteryId: 1
        }]
      },
      // 新增：号码数量限制相关
      numberCount: 0,
      isNumberCountExceeded: false,
      rules: {
        betGroups: {
          methodId: [{ required: true, message: '请选择玩法', trigger: 'change' }],
          money: [{ required: true, message: '请输入金额', trigger: 'blur' }]
        }
      },
      localForm: {
        fc3dIssueNumber: '',  // 福彩3D期号
        tcIssueNumber: '',    // 体彩排三期号
        shibie: '',
        betGroups: []
      },
      currentUser: { name: '' },
      userList: [],
      selectedUserId: null,
      futiSwitch: sessionStorage.getItem('futiSwitch') || 'all',
      formatDialogVisible: false,
      formatInput: '',
      formatOutput: '',
      formatTab: 'unified',
      colonFormatInput: '',
      colonFormatOutput: '',
      moneyFormatInput: '',
      moneyFormatOutput: '',

      underscoreFormatInput: '',
      underscoreFormatOutput: '',
      unifiedFormatInput: '',
      unifiedFormatOutput: '',
      chainFormatInput: '',
      chainFormatOutput: '',
      amountFormatInput: '',
      amountFormatOutput: '',
      removeFormatInput: '',
      removeFormatOutput: '',
      serialNumber: 1, // 新增本组序号
      maxSerialNumber: 1, // 新增最大流水号
      generateLoading: false, // 生成流水号加载状态
      originalUserId: null, // 原始用户ID
      originalSerialNumber: null, // 原始流水号
      statisticsVisible: false, // 统计对话框显示状态
      hasStatisticsPermission: false // 是否有统计权限
    }
  },
  computed: {
    /** 是否显示统计按钮 */
    showStatisticsButton() {
      return this.hasStatisticsPermission;
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        // 显示对话框时先检查玩家状态
        this.checkPlayerStatus();
        this.getAllUsers();
        // 检查统计权限
        this.checkStatisticsPermission();
        // 新增：区分新增和修改，设置 selectedUserId
        if (this.row && this.row.userId) {
          // 修改时
          this.selectedUserId = this.row.userId;
        } else {
          // 新增时，先检查session中是否有User_Id
          let userId = sessionStorage.getItem('User_Id');
          if (userId) {
            this.selectedUserId = Number(userId);
          } else {
            // 如果没有，等待initializeFirstUser()设置
            this.selectedUserId = null;
          }
        }
        // 先初始化流水号，再设置表单数据
      
        this.initSerialNumber(); // 弹窗打开时初始化本组序号
        // 如果是修改操作，初始化表单数据
        if (this.row) {
       
          this.initFormData();
        }
        // 打开弹窗时根据开关状态处理
        this.onFutiSwitchChange(this.futiSwitch);
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
      if (!val) {
        this.resetForm();
      }
    },
    // 监听用户ID变化，自动处理流水号
    selectedUserId: {
      handler(newUserId, oldUserId) {
        // 只在修改模式下处理，且确保有原始数据
        if (this.row && this.row.betId && this.originalUserId !== null && oldUserId !== undefined) {
          this.handleUserIdChange(newUserId, oldUserId);
        }
      },
      immediate: false
    },
    formatDialogVisible(val) {
      if (!val) {
        // 弹窗关闭时清空所有格式内容
        this.formatInput = '';
        this.formatOutput = '';
        this.colonFormatInput = '';
        this.colonFormatOutput = '';
        this.moneyFormatInput = '';
        this.moneyFormatOutput = '';

        this.underscoreFormatInput = '';
        this.underscoreFormatOutput = '';
        this.unifiedFormatInput = '';
        this.unifiedFormatOutput = '';
        this.chainFormatInput = '';
        this.chainFormatOutput = '';
        this.amountFormatInput = '';
        this.amountFormatOutput = '';
        this.removeFormatInput = '';
        this.removeFormatOutput = '';
      }
    }
  },
  created() {
    // 初始化时获取玩法列表
    this.getGameMethods();
  },
  methods: {
    // 显示玩法格式弹窗
    showBetFormatDialog() {
      this.$refs.betFormatDialog.show();
    },
    // 检查玩家状态
    async checkPlayerStatus() {
      try {
        const response = await checkAndSetDefaultPlayer();

        if (response.code === 500 && response.needCreatePlayer) {
          // 没有玩家，显示提示并跳转
          this.$confirm(response.msg + ' 是否立即前往创建？', '提示', {
            confirmButtonText: '前往创建',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // 关闭当前对话框
            this.dialogVisible = false;
            this.$emit('update:visible', false);
            // 跳转到玩家页面
            this.$router.push('/game/customer');
          }).catch(() => {
            // 用户取消，关闭对话框
            this.dialogVisible = false;
            this.$emit('update:visible', false);
          });
          return;
        }

        if (response.code === 200 && response.hasPlayers) {
          // 有玩家，继续初始化
          
          if (response.defaultUserId) {
            
          }

          // 继续初始化对话框
          this.initializeDialog();
        }
      } catch (error) {
        console.error('检查玩家状态失败:', error);
        this.$message.error('检查玩家状态失败，请重试');
        this.dialogVisible = false;
        this.$emit('update:visible', false);
      }
    },

    // 初始化对话框
    initializeDialog() {
      // 显示对话框时获取期号和玩法列表
      this.getCurrentIssueNumber();
      this.getGameMethods();
      this.getCurrentUser();
    },

    // 获取玩法列表
    getGameMethods() {
      this.loadingGameMethods = true;
      return request({
        url: '/game/method/list',
        method: 'get',
        params: {
          pageNum: 1,
          pageSize: 1000  // 设置足够大的页面大小以获取所有数据
        }
      }).then(response => {
        let data = [];
        if (response.code === 200) {
          if (Array.isArray(response.data)) {
            data = response.data;
          } else if (response.rows && Array.isArray(response.rows)) {
            data = response.rows;
          } else if (typeof response.data === 'string') {
            try {
              const parsedData = JSON.parse(response.data);
              data = Array.isArray(parsedData) ? parsedData : [];
            } catch (e) {
              data = [];
            }
          }
        }
        this.gameMethodsData = data;
        this.filteredGameMethods = data;
        this.loadingGameMethods = false;
      }).catch(() => {
        this.gameMethodsData = [];
        this.filteredGameMethods = [];
        this.loadingGameMethods = false;
      });
    },
    // 打开对话框
    show(title = '新增下注') {
      this.dialogTitle = title;
      this.dialogVisible = true;
    },
    // 关闭对话框
    close() {
      this.dialogVisible = false;
      this.$emit('update:visible', false);
      this.$emit('cancel');  // 保持向后兼容
    },
    // 重置表单
    resetForm() {
      this.formData = {
        fc3dIssueNumber: '',
        tcIssueNumber: '',
        shibie: '',
        betGroups: [{
          methodId: undefined,
          money: '',
          betNumbers: '',
          danshuang: null,
          daxiao: null,
          dingwei: null,
          hezhi: null,
          lotteryId: 1
        }]
      };
      this.totalAmount = 0;
      this.totalBets = 0;
      // 重置号码数量状态
      this.numberCount = 0;
      this.isNumberCountExceeded = false;
    },
    addBetGroup() {
      const newGroup = {
        methodId: undefined,
        money: '',
        betNumbers: '',
        danshuang: null,
        daxiao: null,
        dingwei: null,
        hezhi: null,
        lotteryId: this.futiSwitch === 'tc' ? 2 : 1
      };

      // 直接操作formData
      if (!this.formData.betGroups) {
        this.formData.betGroups = [];
      }
      this.formData.betGroups.push({ ...newGroup });

      this.$nextTick(() => {
        this.updateTotals();
      });
    },
    removeBetGroup(idx) {
      if (this.formData.betGroups.length <= 1) {
        // 如果只剩一个投注组，则清空它而不是删除
        this.formData.betGroups = [{
          methodId: undefined,
          money: '',
          betNumbers: '',
          danshuang: null,
          daxiao: null,
          dingwei: null,
          hezhi: null,
          lotteryId: 1
        }];
      } else {
        // 如果有多个投注组，则删除指定的组
        this.formData.betGroups.splice(idx, 1);
      }

      this.$nextTick(() => {
        this.updateTotals();
        this.$forceUpdate(); // 强制更新视图
      });
    },
    updateTotals() {
      this.totalAmount = this.formData.betGroups.reduce((sum, g) => sum + Number(this.getGroupAmount(g)), 0);
      this.totalBets = this.formData.betGroups.reduce((sum, g) => sum + Number(this.getGroupBets(g)), 0);
    },
    getHezhiOptions(methodId) {
      const map = {
        21: [7, 20],
        22: [8, 19],
        23: [9, 18],
        24: [10, 17],
        25: [11, 16],
        26: [12, 15],
        27: [13, 14],
        37: [0, 27],
        38: [1, 26],
        39: [2, 25],
        40: [3, 24],
        41: [4, 23],
        42: [5, 22],
        43: [6, 21]
      };
      return map[methodId] || [];
    },
    onMethodChange(group) {
      
      const index = this.formData.betGroups.findIndex(g => g === group);
      if (index > -1) {
        // 创建一个新的组对象，确保包含所有必要的字段
        const updatedGroup = {
          ...group,
          danshuang: null,
          daxiao: null,
          dingwei: null,
          hezhi: null
          // 不重置 betNumbers 和 money
        };

        // 根据玩法类型设置默认值
        if (updatedGroup.methodId === 29) {
          updatedGroup.danshuang = 200;
        } else if (updatedGroup.methodId === 30) {
          updatedGroup.daxiao = 300;
        } else if (updatedGroup.methodId === 2) {
          updatedGroup.dingwei = 100;
        } else if ([21, 22, 23, 24, 25, 26, 27, 37, 38, 39, 40, 41, 42, 43].includes(updatedGroup.methodId)) {
          const options = this.getHezhiOptions(updatedGroup.methodId);
          if (options && options.length > 0) {
            updatedGroup.hezhi = options[0];
          }
        }

        // 更新组数据
        this.$set(this.formData.betGroups, index, updatedGroup);
        this.$forceUpdate();
      }
    },
    // 获取当前期号
    getCurrentIssueNumber() {
      const loadingInstance = this.$loading({
        lock: true,
        text: '正在获取期号...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
        customClass: 'bet-dialog-loading'
      });

      // 使用Promise.all同时获取两个期号
      Promise.all([
        // 获取福彩3D期号
        getCurrentQihao(1).catch(error => {
          console.error('获取福彩3D期号失败:', error);
          return { code: 500, msg: '获取失败' };
        }),
        // 获取体彩排三期号
        getCurrentQihao(2).catch(error => {
          console.error('获取体彩排三期号失败:', error);
          return { code: 500, msg: '获取失败' };
        })
      ]).then(([fc3dResponse, tcResponse]) => {
        // 处理福彩3D期号
        if (fc3dResponse.code === 200) {
          this.formData.fc3dIssueNumber = fc3dResponse.msg;
          
        } else {
          console.error('获取福彩3D期号失败:', fc3dResponse);
          this.$message.error('获取福彩3D期号失败');
        }

        // 处理体彩排三期号
        if (tcResponse.code === 200) {
          this.formData.tcIssueNumber = tcResponse.msg;
         
        } else {
          console.error('获取体彩排三期号失败:', tcResponse);
          this.$message.error('获取体彩排三期号失败');
        }
      }).finally(() => {
        loadingInstance.close();
      });
    },
    // 新增：动态计算号码数量的方法
    calculateNumberCount(input) {
      if (!input || !input.trim()) return 0;

      // 清理输入，去除首尾空白
      const cleanInput = input.trim();

      // 使用更精确的正则匹配号码组
      // 匹配2-9位连续数字，考虑各种分隔符
      const numberPattern = /\d{2,9}/g;
      const allMatches = cleanInput.match(numberPattern);

      if (!allMatches) return 0;

      // 按行分析，更准确地识别号码和金额
      const lines = cleanInput.split(/[\r\n]+/).filter(line => line.trim());
      let totalCount = 0;

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;

        const lineNumbers = trimmedLine.match(numberPattern);
        if (!lineNumbers) continue;

        // 检查金额相关关键字
        const moneyKeywords = /[元块米钱赶组直吊放防各]/;
        const hasMoneyKeyword = moneyKeywords.test(trimmedLine);

        // 检查是否有明显的金额分隔符（如+、/等）
        const hasMoneyDelimiter = /[+＋\/]/.test(trimmedLine);

        if (hasMoneyKeyword || hasMoneyDelimiter) {
          // 如果包含金额关键字或分隔符，最后1-2个数字可能是金额
          // 更保守的估计：减去1个可能的金额
          totalCount += Math.max(0, lineNumbers.length - 1);
        } else {
          // 纯号码行，所有数字都算作号码
          totalCount += lineNumbers.length;
        }
      }

      return totalCount;
    },
    // 智能预处理批量号码+金额输入，避免金额被误识别为号码
    smartPreprocessInput(input) {
      if (!input) return input;
      // 支持多行或"—"等超级分隔符
      let parts = input.split(/[—\-\n\r]+/).map(s => s.trim()).filter(Boolean);
      if (parts.length > 1) {
        // 金额表达关键字更宽松，兼容赶/组/直/吊/放/防/元/各等
        const moneyExpr = /(赶|组|直|吊|放|防|元|各)[^\d]*\d+.*$/;
        const last = parts[parts.length - 1];
        if (moneyExpr.test(last)) {
          // 合并为"号码—号码—...—金额表达"
          return parts.slice(0, -1).join('—') + '—' + last;
        }
      }
      return input;
    },
    // 新增：检查首组号码是否为三位数的方法
    isFirstNumberThreeDigits(input) {
      if (!input || !input.trim()) return false;

      // 清理输入，去除首尾空白
      const cleanInput = input.trim();

      // 匹配第一个2-9位连续数字
      const firstNumberMatch = cleanInput.match(/\d{2,9}/);

      if (!firstNumberMatch) return false;

      // 检查第一个号码是否为三位数
      return firstNumberMatch[0].length === 3;
    },
    handleNumberRecognition(value) {
      // 实时计算并更新号码数量
      this.numberCount = this.calculateNumberCount(value);

      // 判断如果首组号码为三位数则不限制号码组数为30
      const isFirstThreeDigits = this.isFirstNumberThreeDigits(value);
      this.isNumberCountExceeded = isFirstThreeDigits ? false : this.numberCount > 30;

      if (!value) {
        this.formData.betGroups = [{
          methodId: undefined,
          money: '',
          betNumbers: '',
          danshuang: null,
          daxiao: null,
          dingwei: null,
          hezhi: null,
          lotteryId: this.futiSwitch === 'tc' ? 2 : 1
        }];
        return;
      }

      // 如果超过30个号码，不进行识别处理，但仍然显示计数
      if (this.isNumberCountExceeded) {
        
        return;
      }
      // 1. 先整体识别（必须用原始 value，不能预处理！）
      let result = handleNumberRecognition(value);
      // 2. 如果整体没命中，再按行分割，每行单独识别，不做任何合并
      if (!result || !result.groups || !result.groups.some(g => g.methodId)) {
        const lines = value.split(/[\r\n]+/).map(line => line.trim()).filter(Boolean);
        let allGroups = [];
        for (const line of lines) {
          const lineResult = handleNumberRecognition(line);
          if (lineResult && lineResult.groups && lineResult.groups.length > 0) {
            allGroups = allGroups.concat(lineResult.groups);
          }
        }
        result = { groups: allGroups };
      }
      
      

      if (result) {
        // 如果存在groups，说明有多个和值组
        if (result.groups && result.groups.length > 0) {
     
          // 清空现有的投注组
          this.$set(this.formData, 'betGroups', []);
          // 添加所有和值组
          result.groups.forEach(group => {
            const newGroup = {
              methodId: group.methodId,
              danshuang: group.danshuang,
              daxiao: group.daxiao,
              dingwei: group.dingwei,
              hezhi: group.hezhi,
              betNumbers: group.betNumbers,
              money: group.money != null ? String(group.money) : '', // 确保money为字符串类型
              lotteryId: this.futiSwitch === 'tc' ? 2 : (group.lotteryId || 1)
            };

            this.formData.betGroups.push(newGroup);
          });
        } else {

          // 单个投注组的情况
          const newGroup = {
            methodId: result.methodId,
            danshuang: result.danshuang,
            daxiao: result.daxiao,
            dingwei: result.dingwei,
            hezhi: result.hezhi,
            betNumbers: result.betNumbers,
            money: result.money != null ? String(result.money) : '', // 确保money为字符串类型
            lotteryId: this.futiSwitch === 'tc' ? 2 : (result.lotteryId || 1)
          };

          this.$set(this.formData, 'betGroups', [newGroup]);
        }

        // 同步到localForm
        const localGroups = this.formData.betGroups.map(group => ({
          ...group,
          danshuang: group.danshuang || null,
          daxiao: group.daxiao || null,
          dingwei: group.dingwei || null,
          hezhi: group.hezhi || null,
          betNumbers: group.betNumbers || '',
          money: group.money || null,
          lotteryId: group.lotteryId || 1
        }));
   
        this.$set(this.localForm, 'betGroups', localGroups);

        // 强制更新视图
        this.$nextTick(() => {
          
          this.updateTotals();
          // 确保每个投注组都正确更新
          this.formData.betGroups.forEach((group, index) => {
            const updatedGroup = { ...group };
           
            this.$set(this.formData.betGroups, index, updatedGroup);
          });
    
          this.$forceUpdate();
        });
      }
    },
    formatBetNumbersToDisplay(jsonStr) {
      try {
        if (!jsonStr) return '';

        // 如果不是JSON格式，直接返回
        if (!jsonStr.startsWith('{')) {
          return jsonStr;
        }

        const data = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr;
        if (!data.numbers || !Array.isArray(data.numbers)) return jsonStr;

        return data.numbers.map(num => {
          // 跨度玩法特殊处理
          if (num.kuadu !== undefined) {
            return `跨度${num.kuadu}`;
          }
          // 胆拖玩法特殊处理
          if (num.danma !== undefined && num.tuoma !== undefined) {
            return `胆${num.danma}拖${num.tuoma}`;
          }
          // 其他玩法
          const values = Object.values(num);
          return values.join('');
        }).join(',');
      } catch (error) {
        console.error('格式化下注号码失败:', error, 'jsonStr:', jsonStr);
        return jsonStr;
      }
    },
    // 获取用于显示的下注号码
    getDisplayBetNumbers(group) {
      if (!group.betNumbers) return '';

      // 跨度玩法 (60-69) 和胆拖玩法 (44-59) 需要特殊格式化
      if ((group.methodId >= 44 && group.methodId <= 59) || (group.methodId >= 60 && group.methodId <= 69)) {
        try {
          // 检查是否是JSON格式
          if (group.betNumbers.startsWith('{') && group.betNumbers.includes('numbers')) {
            return this.formatBetNumbersToDisplay(group.betNumbers);
          }
          // 如果不是JSON格式，直接返回（用户正在编辑）
          return group.betNumbers;
        } catch (error) {
          console.error('格式化显示号码失败:', error);
          return group.betNumbers;
        }
      }

      // 其他玩法直接显示
      return group.betNumbers;
    },
    // 处理下注号码输入
    onBetNumbersInput(value, index) {
      const group = this.formData.betGroups[index];

      // 跨度玩法和胆拖玩法需要保持JSON格式存储，但显示为用户友好格式
      if ((group.methodId >= 44 && group.methodId <= 59) || (group.methodId >= 60 && group.methodId <= 69)) {
        // 对于这些特殊玩法，用户输入的友好格式需要转换为JSON格式存储
        // 但这里我们暂时直接存储用户输入，让识别逻辑处理
        this.$set(this.formData.betGroups, index, { ...group, betNumbers: value });
      } else {
        // 其他玩法直接存储
        this.$set(this.formData.betGroups, index, { ...group, betNumbers: value });
      }

      this.updateTotals();
    },
    initFormData() {
      try {
        // 保存原始数据
        this.originalUserId = this.row.userId;
        this.originalSerialNumber = this.row.serialNumber;

        // 设置期号
        this.formData.fc3dIssueNumber = this.row.issueNumber;
        this.formData.tcIssueNumber = this.row.issueNumber;

        // 格式化下注号码
        const formattedBetNumbers = this.formatBetNumbersToDisplay(this.row.betNumbers);

        // 创建投注组
        const group = {
          methodId: this.row.methodId,
          money: this.row.money != null ? String(this.row.money) : '', // 确保money为字符串类型
          betNumbers: formattedBetNumbers, // 使用格式化后的号码
          danshuang: this.row.danshuang,
          daxiao: this.row.daxiao,
          dingwei: this.row.dingwei,
          hezhi: this.row.hezhi,
          lotteryId: this.row.lotteryId
        };

        // 设置投注组
        this.formData.betGroups = [group];

        // 如果有识别文本，也设置
        if (this.row.shibie) {
          this.formData.shibie = this.row.shibie;
        }

        // 如果有流水号，也设置
        if (this.row.serialNumber) {
       
          this.serialNumber = this.row.serialNumber;
        }

        // 更新总计
        this.$nextTick(() => {
          this.updateTotals();
          // 强制更新视图以确保特殊玩法正确显示
          this.$forceUpdate();
        });
      } catch (error) {
        console.error('初始化表单数据失败:', error);
      }
    },
    formatNumbersForRequest(betNumbers, methodId) {
      if (!betNumbers) return { numbers: [] };

      // 胆拖玩法 (44-59) - 只存储danma和tuoma
      if (methodId >= 44 && methodId <= 59) {
        return this.formatDantuoNumbers(betNumbers, methodId);
      }

      // 跨度玩法 (60-69) - 只存储kuadu
      if (methodId >= 60 && methodId <= 69) {
        return this.formatKuaduNumbers(betNumbers, methodId);
      }

      // 分割号码组
      const numberGroups = betNumbers.split(',').filter(n => n.trim());

      // 根据玩法格式化
      const formatMap = {
        // 一位数玩法（独胆、一码定位、杀码等）
        1: (num) => ({ a: parseInt(num) }),
        2: (num) => ({ a: parseInt(num) }),
        33: (num) => ({ a: parseInt(num) }),
        34: (num) => ({ a: num }), // 两码定位，保持原始模式如"9*6"
        35: (num) => ({ a: num }), // 一码单双
        36: (num) => ({ a: num }), // 一码大小
        // 两位数玩法（两码组合、对子等）
        3: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1] };
        },
        4: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1] };
        },
        // 两码组三
        70: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1] };
        },

        // 三位数玩法（三码直选、组选、防对等）
        5: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1], c: digits[2] };
        },
        6: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1], c: digits[2] };
        },
        7: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1], c: digits[2] };
        },
        100: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1], c: digits[2] };
        },

        // 四码
        8: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3] };
        },
        9: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3] };
        },

        // 五码
        10: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4] };
        },
        11: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4] };
        },

        // 六码
        12: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5] };
        },
        13: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5] };
        },

        // 七码
        14: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6] };
        },
        15: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6] };
        },

        // 八码
        16: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6], h: digits[7] };
        },
        17: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6], h: digits[7] };
        },

        // 九码
        18: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6], h: digits[7], i: digits[8] };
        },
        19: (num) => {
          const digits = num.split('').map(n => parseInt(n));
          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6], h: digits[7], i: digits[8] };
        },

        // 包打组六、组三（不需要号码，返回空对象）
        20: () => ({}),
        31: () => ({}),

        // 和值玩法（和值直接传递，不需要号码）
        21: () => ({}),
        22: () => ({}),
        23: () => ({}),
        24: () => ({}),
        25: () => ({}),
        26: () => ({}),
        27: () => ({}),

        // 新增和值玩法（和值直接传递，不需要号码）
        37: () => ({}),
        38: () => ({}),
        39: () => ({}),
        40: () => ({}),
        41: () => ({}),
        42: () => ({}),
        43: () => ({}),

        // 直选复式（暂时按空对象处理）
        28: () => ({}),

        // 和值单双
        29: () => ({}),
        // 和值大小
        30: () => ({}),

      };

      const formatter = formatMap[methodId];
      if (!formatter) return { numbers: [] };

      return {
        numbers: numberGroups.map(num => formatter(num.trim()))
      };
    },
    submitForm() {
      // 先验证金额
      const moneyValidation = this.validateMoney();
      if (!moneyValidation.valid) {
        this.$alert(moneyValidation.message, '错误', {
          confirmButtonText: '确定',
          type: 'error',
          dangerouslyUseHTMLString: true
        });
        return;
      }

      this.$refs["form"].validate(valid => {
        if (valid) {
          const loadingInstance = this.$loading({
            lock: true,
            text: '正在提交下注...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });

          // 修正 userId 赋值逻辑
          const betRecords = this.formData.betGroups.map((group, index) => {
            // 格式化下注号码为JSON格式
            const numbers = this.formatNumbersForRequest(group.betNumbers, group.methodId);
            // 从 sessionStorage 获取 sysUserId
            const sysUserId = sessionStorage.getItem('sysUserId');

            const record = {
              lotteryId: group.lotteryId,
              issueNumber: group.lotteryId === 1 ? this.formData.fc3dIssueNumber : this.formData.tcIssueNumber,
              methodId: group.methodId,
              // 修改：userId 始终用当前选择的 selectedUserId
              userId: Number(this.selectedUserId),
              // 新增：从 sessionStorage 获取并传递 sysUserId
              sysUserId: sysUserId ? Number(sysUserId) : null,
              betNumbers: JSON.stringify(numbers),
              money: group.money,
              danshuang: group.danshuang,
              daxiao: group.daxiao,
              dingwei: group.dingwei,
              hezhi: group.hezhi,
              totalAmount: this.getGroupAmount(group),
              totalBets: this.getGroupBets(group),
              shibie: this.formData.shibie,
              betTime: this.formatDate(new Date()),
              serialNumber: this.serialNumber // 新增：传递流水号
            };
            // 如果是修改操作，添加betId
            if (this.row && index === 0) {
              record.betId = this.row.betId;
            }
            // 逐行打印每个 record
          
            return record;
          });

          // 提交前打印所有 betRecords
       

          // 根据是否是修改操作选择不同的提交方式
          const url = this.row ? '/game/record' : '/game/record/batch';
          const method = this.row ? 'put' : 'post';
          const data = this.row ? betRecords[0] : betRecords;

          request({
            url,
            method,
            data
          }).then(response => {
            if (response.code === 200) {
              this.$modal.msgSuccess('操作成功');
              this.$emit('success');
              // 清空号码识别框和投注组
              this.formData.shibie = '';
              this.formData.betGroups = [{
                methodId: undefined,
                money: '',
                betNumbers: '',
                danshuang: null,
                daxiao: null,
                dingwei: null,
                hezhi: null,
                lotteryId: 1
              }];
              this.updateTotals();
              // 新增：仅在修改时关闭弹窗，新增时不关闭
              if (this.row) {
                this.close();
              }
            } else {
              // 如果后端返回了具体哪些投注失败
              if (response.data && Array.isArray(response.data.failedIndices)) {
                // 保留失败的投注组
                const failedGroups = this.formData.betGroups.filter((_, index) =>
                  response.data.failedIndices.includes(index)
                );
                this.$set(this.formData, 'betGroups', failedGroups);
                this.$modal.msgError(`${response.data.failedIndices.length}个下注失败，请重试`);
              } else {
                // 检查错误是否已经被响应拦截器处理
                if (response._errorHandled) {
                  // 错误已经被响应拦截器处理并显示，这里不再重复处理
          
                } else {
                  // 未被处理的错误，显示通用错误信息
                  this.$modal.msgError(response.msg || '操作失败，请重试');
                }
              }
            }
          }).catch(error => {
            console.error('网络请求失败:', error);

            // 现在只有真正的网络错误才会进入 catch 块
            // 业务错误已经在响应拦截器中处理并返回到 then 块
            this.$modal.msgError('网络连接失败，请检查网络后重试');
          }).finally(() => {
            loadingInstance.close();
            this.updateTotals();
          });
        }
      });
    },
    getGroupBets(group) {
      // 胆拖玩法 (44-59) - 始终显示为1注
      if (group.methodId >= 44 && group.methodId <= 59) {
        return 1;
      }

      // 跨度玩法 (60-69) - 始终显示为1注
      if (group.methodId >= 60 && group.methodId <= 69) {
        return 1;
      }

      if (this.isSpecialMethod(group.methodId)) {
        if ([21,22,23,24,25,26,27,37,38,39,40,41,42,43].includes(group.methodId)) {
          return group.hezhi ? 1 : 0;
        }
        if ([29,30].includes(group.methodId)) {
          if (group.danshuang || group.daxiao) {
            return 1;
          }
          return 0;
        }
        if ([20,31].includes(group.methodId)) {  // 包打组六和包打组三
          return 1;
        }
        return 0;
      }
      if (group.betNumbers) {
        return group.betNumbers.split(',').filter(s => s.trim()).length;
      }
      return 0;
    },
    getGroupAmount(group) {
      const bets = this.getGroupBets(group);
      const money = Number(group.money) || 0;
      return bets * money;
    },
    isSpecialMethod(methodId) {
      return [20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 37, 38, 39, 40, 41, 42, 43].includes(methodId);
    },
    // 胆拖格式化方法
    formatDantuoNumbers(betNumbers, methodId) {
      // 解析胆码和拖码
      const parts = betNumbers.split('拖');
      if (parts.length !== 2) return { numbers: [] };

      const danma = parts[0].replace(/[^0-9]/g, '');
      const tuoma = parts[1].replace(/[^0-9,，\s]/g, '').split(/[,，\s]+/).filter(n => n && n !== danma);

      return {
        numbers: [{
          danma: danma,
          tuoma: tuoma.join(',')
        }]
      };
    },
    // 跨度格式化方法
    formatKuaduNumbers(betNumbers, methodId) {
      console.log('formatKuaduNumbers 输入:', betNumbers, 'methodId:', methodId);

      // 如果已经是JSON格式，直接返回
      if (betNumbers.startsWith('{')) {
        try {
          const parsed = JSON.parse(betNumbers);
          console.log('已是JSON格式:', parsed);
          return parsed;
        } catch (error) {
          console.error('JSON解析失败:', error);
        }
      }

      // 解析跨度值 - 支持多种格式
      let kuaduValue = null;

      // 格式1: "跨度5"
      let match = betNumbers.match(/跨度(\d)/);
      if (match) {
        kuaduValue = match[1];
      } else {
        // 格式2: "跨5-50" 或 "跨5 50"
        match = betNumbers.match(/跨(\d)[\s\-]*\d+/);
        if (match) {
          kuaduValue = match[1];
        } else {
          // 格式3: 从methodId推导跨度值
          if (methodId >= 60 && methodId <= 69) {
            kuaduValue = (methodId - 60).toString();
          }
        }
      }

      console.log('解析出的跨度值:', kuaduValue);

      if (kuaduValue === null) {
        console.warn('无法解析跨度值，返回空数组');
        return { numbers: [] };
      }

      const result = {
        numbers: [{
          kuadu: kuaduValue
        }]
      };

      console.log('formatKuaduNumbers 输出:', result);
      return result;
    },
    updateGroupValue(group, field, value) {

      const index = this.formData.betGroups.findIndex(g => g === group);
      if (index > -1) {
        const updatedGroup = {
          ...this.formData.betGroups[index],
          [field]: value
        };
        this.$set(this.formData.betGroups, index, updatedGroup);
        this.$forceUpdate();
      }
    },


    // 格式化日期为 yyyy-MM-dd HH:mm:ss
    formatDate(date) {
      const pad = (num) => (num < 10 ? `0${num}` : num);
      const year = date.getFullYear();
      const month = pad(date.getMonth() + 1);
      const day = pad(date.getDate());
      const hours = pad(date.getHours());
      const minutes = pad(date.getMinutes());
      const seconds = pad(date.getSeconds());
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    getCurrentUser() {
      let userId = sessionStorage.getItem('User_Id');

      if (userId) {
        // 如果session中有User_Id，直接使用
        this.selectedUserId = Number(userId);
        this.loadUserById(userId);
      } else {
        // 如果session中没有User_Id，先获取用户列表，然后使用第一个用户
        this.initializeFirstUser();
      }
    },

    loadUserById(userId) {
      request({
        url: `/game/customer/${userId}`,
        method: 'get'
      }).then(res => {
        if (res.code === 200 && res.data) {
          this.currentUser = res.data;
        } else {
          this.currentUser = { name: '未知用户' };
        }
      }).catch(error => {
        console.error('获取用户信息失败:', error);
        this.currentUser = { name: '未知用户' };
      });
    },

    initializeFirstUser() {
      // 获取当前用户的玩家列表
      request({
        url: '/game/customer/list',
        method: 'get',
        params: { pageNum: 1, pageSize: 1000 }
      }).then(res => {
        if (res.code === 200 && res.rows && res.rows.length > 0) {
          // 使用第一个玩家作为默认用户
          const firstUser = res.rows[0];
          this.selectedUserId = firstUser.userId;
          this.currentUser = firstUser;
          // 保存到session中
          sessionStorage.setItem('User_Id', firstUser.userId);
        
        } else {
          console.warn('当前用户没有玩家数据');
          this.currentUser = { name: '无玩家数据' };
          this.selectedUserId = null;
        }
      }).catch(error => {
        console.error('获取玩家列表失败:', error);
        this.currentUser = { name: '获取失败' };
        this.selectedUserId = null;
      });
    },
    getAllUsers() {
      request({
        url: '/game/customer/list',
        method: 'get',
        params: { pageNum: 1, pageSize: 1000 }
      }).then(res => {
        if (res.code === 200 && res.rows) {
          this.userList = res.rows;
        } else {
          this.userList = [];
        }
      });
    },
    switchUser() {
      if (!this.selectedUserId) return;
      sessionStorage.setItem('User_Id', this.selectedUserId);
      this.getCurrentUser();
      this.$message.success('切换用户成功');
    },
    onFutiSwitchChange(val) {
      sessionStorage.setItem('futiSwitch', val);
      if (val === 'tc') {
        // 仅体彩，所有下注单都设为2
        if (this.formData && this.formData.betGroups) {
          this.formData.betGroups.forEach(group => {
            group.lotteryId = 2;
            // 确保money字段始终为字符串类型
            if (group.money != null && typeof group.money !== 'string') {
              group.money = String(group.money);
            }
          });
        }
      } else {
        // 切换为全部彩种时，确保数据类型正确
        if (this.formData && this.formData.betGroups) {
          this.formData.betGroups.forEach(group => {
            // 确保money字段始终为字符串类型
            if (group.money != null && typeof group.money !== 'string') {
              group.money = String(group.money);
            }
          });
        }
      }
      // 切换为全部彩种时不做强制限制
      this.$forceUpdate();
    },
    // remote 搜索用的 filterMethod
    filterMethod(query) {
      if (!query) {
        this.filteredGameMethods = this.gameMethodsData;
      } else {
        this.filteredGameMethods = this.gameMethodsData.filter(item => {
          return (item.methodName && item.methodName.indexOf(query) !== -1) ||
                 (query === '3' && item.methodName && item.methodName.indexOf('三') !== -1);
        });
      }
    },
    showFormatDialog() {
      this.formatDialogVisible = true;
      this.formatInput = '';
      this.formatOutput = '';
      this.formatTab = 'unified'; // 每次打开都默认显示字符转换
    },
    /** 显示统计对话框 */
    showStatistics() {
      this.statisticsVisible = true;
    },
    /** 验证金额 */
    validateMoney() {
      for (let i = 0; i < this.formData.betGroups.length; i++) {
        const group = this.formData.betGroups[i];
        const money = group.money;
        const groupNumber = i + 1; // 下注单号从1开始

        // 确保money是字符串类型，防止trim()方法报错
        const moneyStr = money != null ? String(money) : '';

        // 检查是否为空
        if (!moneyStr || moneyStr.trim() === '') {
          return {
            valid: false,
            message: `第<span style="color: #ff4757; font-weight: bold; font-size: 16px;">${groupNumber}</span>组下注金额不能为<span style="color: #ff4757; font-weight: bold; font-size: 16px;">空</span>`
          };
        }

        // 检查是否为数字
        const numMoney = Number(moneyStr.trim());
        if (isNaN(numMoney) || numMoney <= 0) {
          return {
            valid: false,
            message: `第<span style="color: #ff4757; font-weight: bold; font-size: 16px;">${groupNumber}</span>组下注金额不能为<span style="color: #ff4757; font-weight: bold; font-size: 16px;">"${moneyStr}"</span>，请修改`
          };
        }
      }

      return {
        valid: true,
        message: ''
      };
    },
    /** 检查统计权限 */
    async checkStatisticsPermission() {
      try {
        // 调用专门的统计权限API
        const response = await getStatisticsPermission();


        if (response && response.code === 200 && response.data !== undefined) {

          this.hasStatisticsPermission = response.data === '1';
        } else {
 
          this.hasStatisticsPermission = false;
        }

  
      } catch (error) {
        console.error('获取用户权限失败:', error);
        this.hasStatisticsPermission = false;
      }
    },
    handleFormatInput() {
      // 句号tab：所有句号类字符转为-
      let out = (this.formatInput || '').replace(/[。．.]/g, '-');
      // 连续多个短横合并为一个
      out = out.replace(/-+/g, '-');
      // 先替换所有汉字数字为阿拉伯数字
      let lines = out.split('\n').map(line => this.replaceChineseNumber(line));
      lines = this.autoConvertChineseMoney(lines);
      this.formatOutput = lines.filter(line => line).join('\n');
    },
    insertFormatResult() {
      this.formData.shibie = this.formatOutput;
      this.formatDialogVisible = false;
      this.handleNumberRecognition(this.formatOutput);
    },
    handleColonFormatInput() {
      // 冒号tab：所有冒号类字符转为-
      let out = (this.colonFormatInput || '').replace(/[：:]/g, '-');
      // 连续多个短横合并为一个
      out = out.replace(/-+/g, '-');
      // 先替换所有汉字数字为阿拉伯数字
      let lines = out.split('\n').map(line => this.replaceChineseNumber(line));
      lines = this.autoConvertChineseMoney(lines);
      this.colonFormatOutput = lines.filter(line => line).join('\n');
    },
    insertColonFormatResult() {
      this.formData.shibie = this.colonFormatOutput;
      this.formatDialogVisible = false;
      this.handleNumberRecognition(this.colonFormatOutput);
    },

    handleMoneyFormatInput() {
      // 只做汉字金额转数字，且只替换最后一个汉字金额为/数字
      let lines = (this.moneyFormatInput || '').split('\n').map(line => this.replaceChineseNumber(line));
      const chineseMoneyReg = /([一二三四五六七八九十百千万两零]+)(元|块|米)?/g;
      lines = lines.map(line => {
        let lastMatch;
        let match;
        while ((match = chineseMoneyReg.exec(line)) !== null) {
          lastMatch = match;
        }
        if (lastMatch) {
          const num = this.chineseToNumber(lastMatch[1]);
          const unit = lastMatch[2] || '';
          const start = lastMatch.index;
          const end = start + lastMatch[0].length;
          let prefix = line.slice(0, start);
          if (!/[\/\s,，.。-]+$/.test(prefix)) {
            prefix += '/';
          }
          return prefix + num + unit + line.slice(end);
        }
        return line;
      });
      this.moneyFormatOutput = lines.filter(line => line).join('\n');
    },
    insertMoneyFormatResult() {
      this.formData.shibie = this.moneyFormatOutput;
      this.formatDialogVisible = false;
      this.handleNumberRecognition(this.moneyFormatOutput);
    },
    handleUnifiedFormatInput() {
      // 统一字符转换：智能识别并转换各种分隔符和汉字金额
      let out = this.unifiedFormatInput || '';

      // 1. 先处理汉字金额转换（在其他转换之前）
      let lines = out.split('\n').map(line => line.trim()).filter(Boolean);
      lines = this.autoConvertChineseMoney(lines);
      out = lines.join('\n');

      // 2. 替换剩余的汉字数字为阿拉伯数字（不包括已转换的金额）
      out = this.replaceChineseNumber(out);

      // 3. 转换各种分隔符为空格（支持一个或多个连续字符）
      // 逗号、句号、冒号、下划线都转为空格
      out = out.replace(/[,，]+/g, ' ');        // 逗号（中英文）
      out = out.replace(/[。．.]+/g, ' ');       // 句号（中英文）
      out = out.replace(/[：:]+/g, ' ');        // 冒号（中英文）
      out = out.replace(/_+/g, ' ');            // 下划线

      // 4. 处理加号类字符（转换为空格）
      out = out.replace(/[➕＋﹢✚✛✜✙❇️❌❎]+/g, ' ');

      // 5. 合并多个连续空格为一个
      out = out.replace(/\s+/g, ' ');

      this.unifiedFormatOutput = out.split('\n').map(line => line.trim()).filter(Boolean).join('\n');
    },
    insertUnifiedFormatResult() {
      this.formData.shibie = this.unifiedFormatOutput;
      this.formatDialogVisible = false;
      this.handleNumberRecognition(this.unifiedFormatOutput);
    },
    handleChainFormatInput() {
      // 链子分类：按号码长度分组到不同行
      let input = this.chainFormatInput || '';

      // 1. 先进行基础的字符转换
      input = this.replaceChineseNumber(input);

      // 2. 提取所有数字（支持各种分隔符）
      // 使用正则匹配所有连续的数字
      const numberPattern = /\d+/g;
      const numbers = input.match(numberPattern) || [];

      // 3. 按长度分组，同时记录每个长度首次出现的位置
      const lengthGroups = {};
      const lengthFirstIndex = {};

      numbers.forEach((num, index) => {
        const length = num.length;
        if (!lengthGroups[length]) {
          lengthGroups[length] = [];
          lengthFirstIndex[length] = index; // 记录该长度首次出现的位置
        }
        lengthGroups[length].push(num);
      });

      // 4. 按首次出现的顺序排序并生成输出
      const sortedLengths = Object.keys(lengthGroups).sort((a, b) => lengthFirstIndex[a] - lengthFirstIndex[b]);
      const resultLines = [];

      sortedLengths.forEach(length => {
        const nums = lengthGroups[length];
        if (nums.length > 0) {
          // 每行格式：直接显示号码列表，不加长度标识
          const line = nums.join(' ');
          resultLines.push(line);
        }
      });

      this.chainFormatOutput = resultLines.join('\n');
    },
    insertChainFormatResult() {
      this.formData.shibie = this.chainFormatOutput;
      this.formatDialogVisible = false;
      this.handleNumberRecognition(this.chainFormatOutput);
    },
    handleAmountFormatInput() {
      // 金额整合：将相同金额且相同位数的号码合并到一行
      // 支持每行一个号码，也支持一行内用空格分隔的多个号码
      const input = this.amountFormatInput || '';
      const lines = input.split('\n').filter(line => line.trim());

      // 解析每行的号码和金额
      const groups = {};
      const groupOrder = []; // 记录分组首次出现的顺序

      lines.forEach(line => {
        const trimmedLine = line.trim();
        if (!trimmedLine) return;

        // 先尝试按空格分割，看是否有多个号码-金额对
        const parts = trimmedLine.split(/\s+/).filter(part => part.trim());

        // 如果只有一个部分，按原来的逻辑处理
        if (parts.length === 1) {
          this.processAmountFormatItem(parts[0], groups, groupOrder);
        } else {
          // 如果有多个部分，逐个处理每个号码-金额对
          parts.forEach(part => {
            this.processAmountFormatItem(part, groups, groupOrder);
          });
        }
      });

      // 按首次出现的顺序生成输出
      const resultLines = [];
      groupOrder.forEach(groupKey => {
        const group = groups[groupKey];
        if (group && group.numbers.length > 0) {
          // 合并相同金额和位数的号码到一行
          const line = group.numbers.join('-') + '-' + group.amount;
          resultLines.push(line);
        }
      });

      this.amountFormatOutput = resultLines.join('\n');
    },

    // 新增辅助函数：处理单个号码-金额项
    processAmountFormatItem(item, groups, groupOrder) {
      const trimmedItem = item.trim();
      if (!trimmedItem) return;

      // 查找最后一个短横线，分离号码和金额
      const lastDashIndex = trimmedItem.lastIndexOf('-');
      if (lastDashIndex === -1) return; // 没有找到分隔符，跳过

      const number = trimmedItem.substring(0, lastDashIndex).trim();
      const amount = trimmedItem.substring(lastDashIndex + 1).trim();

      if (!number || !amount) return; // 号码或金额为空，跳过

      // 按金额和号码位数分组
      const numberLength = number.length;
      const groupKey = `${amount}_${numberLength}`; // 金额_位数作为分组键

      if (!groups[groupKey]) {
        groups[groupKey] = {
          amount: amount,
          length: numberLength,
          numbers: []
        };
        groupOrder.push(groupKey); // 记录分组首次出现的顺序
      }
      groups[groupKey].numbers.push(number);
    },
    insertAmountFormatResult() {
      this.formData.shibie = this.amountFormatOutput;
      this.formatDialogVisible = false;
      this.handleNumberRecognition(this.amountFormatOutput);
    },
    handleRemoveFormatInput() {
      // 去除分隔符和英文字母：只保留汉字、数字和换行
      const input = this.removeFormatInput || '';

      // 使用正则表达式只保留汉字、数字和换行符
      // \u4e00-\u9fff 匹配汉字
      // \d 匹配数字
      // \n\r 匹配换行符
      // 去除所有英文字母（大小写）和其他字符
      const output = input.replace(/[^\u4e00-\u9fff\d\n\r]/g, '');

      // 清理多余的空行，但保留必要的换行
      this.removeFormatOutput = output.split('\n')
        .map(line => line.trim())
        .filter((line, index, arr) => {
          // 保留非空行，以及前一行非空的空行（作为分隔）
          return line || (index > 0 && arr[index - 1]);
        })
        .join('\n')
        .replace(/\n{3,}/g, '\n\n'); // 最多保留两个连续换行
    },
    insertRemoveFormatResult() {
      this.formData.shibie = this.removeFormatOutput;
      this.formatDialogVisible = false;
      this.handleNumberRecognition(this.removeFormatOutput);
    },
    handleUnderscoreFormatInput() {
      // 下划线tab：将一个或多个连续的下划线转换为空格
      let out = (this.underscoreFormatInput || '').replace(/_+/g, ' ');
      // 先替换所有汉字数字为阿拉伯数字
      let lines = out.split('\n').map(line => this.replaceChineseNumber(line));
      lines = this.autoConvertChineseMoney(lines);
      this.underscoreFormatOutput = lines.filter(line => line).join('\n');
    },
    insertUnderscoreFormatResult() {
      this.formData.shibie = this.underscoreFormatOutput;
      this.formatDialogVisible = false;
      this.handleNumberRecognition(this.underscoreFormatOutput);
    },
    // 工具函数：替换所有汉字数字为阿拉伯数字
    replaceChineseNumber(str) {
      // 先处理完整的中文数字（包括单位），然后再处理单个汉字数字
      // 匹配完整的中文数字表达式，如"二十五"、"一百二十三"等
      const chineseNumberPattern = /[零一二两三四五六七八九十百千万]+/g;

      return str.replace(chineseNumberPattern, (match) => {
        // 如果匹配的是单个字符且不包含单位，使用简单映射
        if (match.length === 1 && !/[十百千万]/.test(match)) {
          const map = { '零':0,'一':1,'二':2,'两':2,'三':3,'四':4,'五':5,'六':6,'七':7,'八':8,'九':9 };
          return map[match] || match;
        }
        // 否则使用完整的中文数字转换
        return this.chineseToNumber(match);
      });
    },
    chineseToNumber(chinese) {
      if (!chinese) return 0;

      // 先判断是否为纯数字
      if (/^\d+$/.test(chinese)) {
        return parseInt(chinese, 10);
      }

      // 移除单位
      const str = chinese.replace(/元|块|米/g, '');

      // 数字映射
      const numMap = {
        '零': 0, '一': 1, '二': 2, '两': 2, '三': 3, '四': 4,
        '五': 5, '六': 6, '七': 7, '八': 8, '九': 9
      };

      // 单位映射
      const unitMap = { '十': 10, '百': 100, '千': 1000, '万': 10000 };

      let result = 0;
      let temp = 0;
      let hasNum = false;

      for (let i = 0; i < str.length; i++) {
        const char = str[i];

        if (numMap.hasOwnProperty(char)) {
          // 是数字
          temp = numMap[char];
          hasNum = true;
        } else if (unitMap.hasOwnProperty(char)) {
          // 是单位
          const unit = unitMap[char];

          if (char === '十' && !hasNum) {
            // "十"前面没有数字，如"十五"，十前面默认为1
            temp = 1;
          }

          if (unit === 10000) {
            // 万
            result = (result + temp) * unit;
            temp = 0;
          } else if (unit >= 100) {
            // 百、千
            result += temp * unit;
            temp = 0;
          } else {
            // 十
            result += temp * unit;
            temp = 0;
          }
          hasNum = false;
        }
      }

      // 处理最后剩余的数字
      result += temp;

      return result;
    },
    autoConvertChineseMoney(lines) {
      if (typeof lines === 'string') lines = lines.split(/\n/);
      // 更精确的中文金额正则，支持复杂的中文数字
      const chineseMoneyReg = /([零一二三四五六七八九十百千万两]+)(元|块|米)?/g;
      return lines.map(line => {
        // 查找所有汉字金额
        let lastMatch;
        let match;
        // 重置正则的lastIndex
        chineseMoneyReg.lastIndex = 0;
        while ((match = chineseMoneyReg.exec(line)) !== null) {
          // 验证匹配的是否真的是数字（包含数字字符或单位字符）
          if (/[一二三四五六七八九十百千万两零]/.test(match[1])) {
            lastMatch = match;
          }
        }
        if (lastMatch) {
          const num = this.chineseToNumber(lastMatch[1]);
          const unit = lastMatch[2] || '';
          // 替换最后一个汉字金额为数字
          const start = lastMatch.index;
          const end = start + lastMatch[0].length;
          let prefix = line.slice(0, start);

          // 确保汉字金额前面有空格，避免和号码混淆
          if (prefix && !/[\s\/,，.。-]$/.test(prefix)) {
            prefix += ' ';
          }

          return prefix + num + unit + line.slice(end);
        }
        return line;
      });
    },
    // 初始化本组序号
    initSerialNumber() {
      // 如果是修改模式（有row数据）且已有流水号，不要重新初始化
      if (this.row && this.row.serialNumber) {

        this.serialNumber = this.row.serialNumber;
        this.originalSerialNumber = this.row.serialNumber;
        // 仍然获取最大流水号用于生成新流水号时参考
        getMaxSerialNumber().then(max => {
          this.maxSerialNumber = max ? Number(max) : 1;
        }).catch(() => {
          this.maxSerialNumber = 1;
        });
        return;
      }

      // 新增模式或没有流水号时，获取最大流水号
      
      getMaxSerialNumber().then(max => {
        if (max == null) {
          this.maxSerialNumber = 1;
          this.serialNumber = 1;
        } else {
          this.maxSerialNumber = Number(max);
          this.serialNumber = Number(max);
        }
        
      }).catch(() => {
        this.maxSerialNumber = 1;
        this.serialNumber = 1;
      });
    },
    // 增加本组序号，只能加到最大+1
    increaseSerialNumber() {
      const next = Number(this.maxSerialNumber) + 1;
      if (this.serialNumber < next) {
        this.serialNumber = next;
      }
      // 如果已经是最大+1，则不再增加
    },
    // 生成新的流水号
    generateNewSerialNumber() {
      this.generateLoading = true;
      generateSerialNumber().then(response => {
        if (response.code === 200) {
          this.serialNumber = response.serialNumber;
          this.maxSerialNumber = response.serialNumber;
          this.$message.success(`生成新流水号: ${response.serialNumber}`);
        } else {
          this.$message.error('生成流水号失败: ' + response.msg);
        }
      }).catch(error => {
        console.error('生成流水号失败:', error);
        this.$message.error('生成流水号失败');
      }).finally(() => {
        this.generateLoading = false;
      });
    },
    // 处理用户ID变化
    handleUserIdChange(newUserId, oldUserId) {
    
      if (newUserId === this.originalUserId) {
        // 改回原用户，恢复原流水号
        this.serialNumber = this.originalSerialNumber;
        this.$message.info(`已恢复原流水号: ${this.originalSerialNumber}`);
      } else {
        // 改为其他用户，自动生成新流水号
        this.autoGenerateSerialNumber();
      }
    },
    // 自动生成流水号（不显示成功消息）
    autoGenerateSerialNumber() {
      generateSerialNumber().then(response => {
        if (response.code === 200) {
          this.serialNumber = response.serialNumber;
          this.maxSerialNumber = response.serialNumber;
          this.$message.info(`用户变更，自动生成新流水号: ${response.serialNumber}`);
        } else {
          this.$message.error('自动生成流水号失败: ' + response.msg);
        }
      }).catch(error => {
        console.error('自动生成流水号失败:', error);
        this.$message.error('自动生成流水号失败');
      });
    },
  }
}
</script>

<style>
/* 使用全局样式 */
.bet-dialog-loading {
  z-index: 9999 !important;
}

.bet-dialog-loading .el-loading-mask {
  z-index: 9999 !important;
}
</style>

<style scoped>
/* 现代化弹窗样式 */
.modern-bet-dialog {
  .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  }

  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
  }

  .el-dialog__title {
    color: white;
    font-weight: 600;
    font-size: 18px;
  }

  .el-dialog__headerbtn .el-dialog__close {
    color: white;
    font-size: 20px;

    &:hover {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

/* 福体开关样式 */
.lottery-switch {
  font-weight: 500;
  margin-left: 8px;
}

/* 表单样式 */
.modern-bet-form {
  .el-form-item__label {
    font-weight: 500;
    color: #495057;
  }
}

/* 合并的信息卡片样式 */
.info-card {
  background: white;
  padding: 8px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  margin-bottom: 8px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  color: #495057;
  font-size: 13px;
  min-width: 60px;

  i {
    color: #667eea;
    font-size: 12px;
  }
}

.compact-input {
  .el-input__inner {
    height: 28px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    }
  }
}

.compact-select {
  .el-input__inner {
    height: 28px;
    font-size: 12px;
    border-radius: 4px;
  }
}

.compact-btn {
  height: 28px;
  padding: 0 8px;
  font-size: 12px;
  border-radius: 4px;

  i {
    margin-right: 2px;
    font-size: 12px;
  }
}

/* 识别区域样式 */
.recognition-section {
  background: white;
  padding: 6px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  margin-bottom: 6px;
}

.recognition-wrapper {
  .el-form-item__content {
    position: relative;
  }
}

.recognition-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
  font-size: 12px;
  color: #495057;

  i {
    color: #667eea;
    font-size: 12px;
  }

  .el-tag {
    margin-left: auto;
  }

  .el-button {
    margin-left: auto;
  }
}

.recognition-textarea {
  .el-textarea__inner {
    border-radius: 6px;
    border-color: #e9ecef;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    transition: all 0.3s ease;

    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    }
  }
}

.option-group {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 6px 10px;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(233, 236, 239, 0.8);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  min-height: 36px;

  &:hover {
    background: rgba(248, 249, 250, 1);
    border-color: rgba(102, 126, 234, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
}

.option-label {
  min-width: 55px;
  margin-right: 8px;
  font-weight: 600;
  color: #495057;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;

  &::before {
    content: '●';
    color: #667eea;
    font-size: 10px;
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* 投注组总额和投注数美化 */
.group-summary {
  display: flex;
  gap: 16px;
  margin-left: auto;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;

  &.amount {
    background: rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.2);

    .summary-icon {
      color: #667eea;
    }

    .summary-value {
      color: #667eea;
      font-size: 15px;
    }
  }

  &.bets {
    background: rgba(255, 107, 53, 0.1);
    border: 1px solid rgba(255, 107, 53, 0.2);

    .summary-icon {
      color: #ff6b35;
    }

    .summary-value {
      color: #ff6b35;
    }
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.summary-icon {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.summary-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.summary-label {
  font-size: 10px;
  color: #666;
  line-height: 1;
  margin-bottom: 2px;
}

.summary-value {
  font-size: 12px;
  font-weight: 700;
  line-height: 1;
}

.el-radio-group {
  display: inline-flex;
  gap: 6px;
  flex-shrink: 0;
}

.el-radio-button {
  margin-right: 0;

  .el-radio-button__inner {
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
    border-color: #e9ecef;
    padding: 6px 8px;
    font-size: 11px;
    min-width: 65px;
    text-align: center;

    &:hover {
      border-color: #667eea;
      color: #667eea;
    }
  }

  /* 福彩3D按钮样式 */
  &[data-lottery="fc3d"] {
    .el-radio-button__inner {
      border-color: #ff6b35;
      color: #ff6b35;

      &:hover {
        border-color: #ff6b35;
        background: rgba(255, 107, 53, 0.1);
      }
    }

    &.is-active {
      .el-radio-button__inner {
        background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
        border-color: #ff6b35;
        color: white;
        box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
      }
    }
  }

  /* 体彩排三按钮样式 */
  &[data-lottery="tc"] {
    .el-radio-button__inner {
      border-color: #409eff;
      color: #409eff;
      min-width: 70px;

      &:hover {
        border-color: #409eff;
        background: rgba(64, 158, 255, 0.1);
      }
    }

    &.is-active {
      .el-radio-button__inner {
        background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
        border-color: #409eff;
        color: white;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
      }
    }
  }
}

.bet-groups-list-wrapper {
  position: relative;
  margin-bottom: 8px;
  margin-top: 0;
}

.bet-groups-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 0 4px;
}

.bet-group-card {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 3px 12px 0 rgba(0, 0, 0, 0.08);
  margin-bottom: 8px;
  padding: 10px 14px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  &::after {
   
    position: absolute;
    top: 8px;
    right: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: 700;
    z-index: 10;
    box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
  }
}

.bet-group-card:hover {
  box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  border-color: #667eea;
}

.bet-group-even {
  background: linear-gradient(135deg, #fff8f5 0%, #fef5f0 100%) !important;
  border-color: #ffb8a1 !important;

  &::before {
    background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
  }

  &::after {
    
    background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
    box-shadow: 0 2px 6px rgba(255, 107, 53, 0.3);
  }

  &:hover {
    box-shadow: 0 6px 20px 0 rgba(255, 107, 53, 0.2);
    border-color: #ff6b35;
  }
}

.bet-group {
  background: #f5f7fa;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  border: 1px solid #e4e7ed;
}

.bet-group:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.bet-group-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #dcdfe6;
}

.bet-group-content {
  margin-left: 10px;
}

.bet-group-footer {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #dcdfe6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-form-item--small.el-form-item {
  margin-bottom: 3px;
}

.modern-bet-form .el-form-item {
  margin-bottom: 6px;
}

.el-textarea__inner {
  font-family: monospace;
}

.el-input-number--small {
  width: 130px;
}

.total-info {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
  padding: 6px 12px;
  border-radius: 6px;
  box-shadow: 0 1px 6px 0 rgba(102, 126, 234, 0.1);
  margin-top: 4px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  position: relative;
  font-size: 13px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 6px 6px 0 0;
  }
}

.dialog-footer {
  text-align: center;
  
  /* background: #f8f9fa; */
  border-radius: 0 0 6px 6px;
  margin: 4px -6px -6px -6px;
}

.dialog-footer .el-button {
  min-width: 80px;
  margin: 0 6px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 13px;
  height: 32px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
  }

  &.el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;

    &:hover {
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
  }
}

:deep(.bet-dialog-loading) {
  z-index: 3000 !important;
}

.bet-group-add-btn {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: -12px;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: 2px solid white;
  box-shadow: 0 2px 8px 0 rgba(102, 126, 234, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  cursor: pointer;
  z-index: 20;
  transition: all 0.3s ease;

  &::before {
    content: '+';
    font-weight: bold;
  }
}

.bet-group-add-btn:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
  transform: translateX(-50%) translateY(-1px) scale(1.05);
  box-shadow: 0 4px 12px 0 rgba(102, 126, 234, 0.4);
}
.el-dialog__body{
  margin-top: -30px;
}

/* 简洁格式转换弹窗样式 */
.simple-format-dialog {
  .el-textarea__inner {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
  }

  .el-form-item {
    margin-bottom: 16px;
  }

  .el-button {
    margin-right: 8px;
  }
}

/* 号码数量超限样式 */
.number-count-exceeded .el-textarea__inner {
  border-color: #f56c6c !important;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;
}

.number-count-exceeded .el-textarea__inner:focus {
  border-color: #f56c6c !important;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.3) !important;
}

/* 格式转换弹窗美化样式 */
.format-dialog {
  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    border-radius: 8px 8px 0 0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: white;
    }

    .el-dialog__close {
      color: white;
      font-size: 20px;

      &:hover {
        color: #f0f0f0;
      }
    }
  }

  .el-dialog__body {
    padding: 0;
    background: #f8f9fa;
  }
}

.format-dialog-content {
  padding: 20px;
}

.format-tabs {
  .el-tabs__header {
    margin: 0 0 20px 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 12px;
    display: flex;
    justify-content: center;
  }

  .el-tabs__nav {
    border: none;
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .el-tabs__item {
    border: none;
    border-radius: 6px;
    margin-right: 8px;
    padding: 12px 24px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 120px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 44px;

    &:hover {
      background: #e3f2fd;
      color: #1976d2;
    }

    &.is-active {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    i {
      margin-right: 6px;
      font-size: 16px;
      display: inline-block;
      vertical-align: middle;
    }

    span {
      display: inline-block;
      vertical-align: middle;
    }
  }

  .el-tabs__content {
    padding: 0;
  }
}

.tab-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8eaed;
}

.input-section, .output-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 600;
  color: #333;
  font-size: 14px;

  i {
    margin-right: 8px;
    font-size: 16px;
    color: #667eea;
  }
}

.arrow-section {
  text-align: center;
  margin: 16px 0;

  i {
    font-size: 24px;
    color: #667eea;
    animation: bounce 2s infinite;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8px);
  }
  60% {
    transform: translateY(-4px);
  }
}

.format-input, .format-output {
  .el-textarea__inner {
    border-radius: 8px;
    border: 2px solid #e8eaed;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    transition: all 0.3s ease;

    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    &::placeholder {
      color: #999;
      font-style: italic;
    }
  }
}

.format-output {
  .el-textarea__inner {
    background: #f8f9fa;
    color: #2d3748;
    font-weight: 500;
  }
}

.button-section {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e8eaed;
}

.action-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
  }

  &:disabled {
    background: #d1d5db;
    box-shadow: none;
    transform: none;
  }
}

.cancel-button {
  border: 2px solid #e8eaed;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  background: white;
  color: #6b7280;
  transition: all 0.3s ease;

  &:hover {
    border-color: #667eea;
    color: #667eea;
    background: #f8f9ff;
  }
}
</style>