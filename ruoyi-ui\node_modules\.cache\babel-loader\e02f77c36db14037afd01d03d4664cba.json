{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetDialog.vue", "mtime": 1756432531741}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\babel.config.js", "mtime": 1750852368688}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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********************************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"}, {"version": 3, "names": ["_numberRecognition", "require", "_request", "_interopRequireDefault", "_serial", "_record", "_qihao", "_customer", "_user", "_BetStatistics", "_BetFormatDialog", "name", "components", "BetStatistics", "BetFormatDialog", "props", "visible", "type", "Boolean", "default", "title", "String", "row", "Object", "data", "dialogVisible", "dialogTitle", "totalAmount", "totalBets", "pendingRequests", "gameMethodsData", "filteredGameMethods", "loadingGameMethods", "formData", "fc3dIssueNumber", "tcIssueNumber", "shibie", "betGroups", "methodId", "undefined", "money", "betNumbers", "danshuang", "<PERSON><PERSON><PERSON>", "dingwei", "hezhi", "lotteryId", "numberCount", "isNumberCountExceeded", "rules", "required", "message", "trigger", "localForm", "currentUser", "userList", "selectedUserId", "futiSwitch", "sessionStorage", "getItem", "formatDialogVisible", "formatInput", "formatOutput", "formatTab", "colonFormatInput", "colonFormatOutput", "moneyFormatInput", "moneyFormatOutput", "underscoreFormatInput", "underscoreFormatOutput", "unifiedFormatInput", "unifiedFormatOutput", "chainFormatInput", "chainFormatOutput", "amountFormatInput", "amountFormatOutput", "removeFormatInput", "removeFormatOutput", "serialNumber", "maxSerialNumber", "generateLoading", "originalUserId", "originalSerialNumber", "statisticsVisible", "hasStatisticsPermission", "computed", "showStatisticsButton", "watch", "val", "checkPlayerStatus", "getAllUsers", "checkStatisticsPermission", "userId", "Number", "initSerialNumber", "initFormData", "onFutiSwitchChange", "$emit", "resetForm", "handler", "newUserId", "oldUserId", "betId", "handleUserIdChange", "immediate", "created", "getGameMethods", "methods", "showBetFormatDialog", "$refs", "betFormatDialog", "show", "_this", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "response", "_t", "w", "_context", "n", "p", "checkAndSetDefaultPlayer", "v", "code", "needCreatePlayer", "$confirm", "msg", "confirmButtonText", "cancelButtonText", "then", "$router", "push", "catch", "a", "hasPlayers", "defaultUserId", "initializeDialog", "console", "error", "$message", "getCurrentIssueNumber", "getCurrentUser", "_this2", "request", "url", "method", "params", "pageNum", "pageSize", "Array", "isArray", "rows", "parsedData", "JSON", "parse", "e", "arguments", "length", "close", "addBetGroup", "_this3", "newGroup", "_objectSpread3", "$nextTick", "updateTotals", "removeBetGroup", "idx", "_this4", "splice", "$forceUpdate", "_this5", "reduce", "sum", "g", "getGroupAmount", "getGroupBets", "getHezhiOptions", "map", "onMethodChange", "group", "index", "findIndex", "updatedGroup", "includes", "options", "$set", "_this6", "loadingInstance", "$loading", "lock", "text", "spinner", "background", "customClass", "Promise", "all", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "_ref2", "_slicedToArray2", "fc3dResponse", "tcResponse", "finally", "calculateNumberCount", "input", "trim", "cleanInput", "numberPattern", "allMatches", "match", "lines", "split", "filter", "line", "totalCount", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "done", "value", "trimmedLine", "lineNumbers", "moneyKeywords", "hasMoneyKeyword", "test", "hasMoneyDelimiter", "Math", "max", "err", "f", "smartPreprocessInput", "parts", "moneyExpr", "last", "slice", "join", "isFirstNumberThreeDigits", "firstNumberMatch", "handleNumberRecognition", "_this7", "isFirstThreeDigits", "result", "groups", "some", "allGroups", "_iterator2", "_step2", "lineResult", "concat", "for<PERSON>ach", "localGroups", "formatBetNumbersToDisplay", "jsonStr", "startsWith", "numbers", "num", "kuadu", "danma", "tuoma", "values", "getDisplayBetNumbers", "onBetNumbersInput", "_this8", "issueNumber", "formattedBetNumbers", "formatNumbersForRequest", "formatDantuoNumbers", "formatKuaduNumbers", "numberGroups", "formatMap", "_", "parseInt", "digits", "b", "c", "d", "h", "i", "formatter", "submitForm", "_this9", "moneyValidation", "validateMoney", "valid", "$alert", "dangerouslyUseHTMLString", "validate", "betRecords", "sysUserId", "record", "stringify", "betTime", "formatDate", "Date", "$modal", "msgSuccess", "failedIndices", "failedGroups", "msgError", "_errorHandled", "isSpecialMethod", "bets", "replace", "log", "parsed", "kuaduValue", "toString", "warn", "updateGroupValue", "field", "_defineProperty2", "date", "pad", "year", "getFullYear", "month", "getMonth", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "loadUserById", "initializeFirstUser", "_this0", "res", "_this1", "firstUser", "setItem", "_this10", "switchUser", "success", "filterMethod", "query", "item", "methodName", "indexOf", "showFormatDialog", "showStatistics", "groupNumber", "moneyStr", "numMoney", "isNaN", "_this11", "_callee2", "_t2", "_context2", "getStatisticsPermission", "handleFormatInput", "_this12", "out", "replaceChineseNumber", "autoConvertChineseMoney", "insertFormatResult", "handleColonFormatInput", "_this13", "insertColonFormatResult", "handleMoneyFormatInput", "_this14", "chineseMoneyReg", "lastMatch", "exec", "chineseToNumber", "unit", "start", "end", "prefix", "insertMoneyFormatResult", "handleUnifiedFormatInput", "insertUnifiedFormatResult", "handleChainFormatInput", "lengthGroups", "lengthFirstIndex", "sortedLengths", "keys", "sort", "resultLines", "nums", "insertChainFormatResult", "handleAmountFormatInput", "_this15", "groupOrder", "part", "processAmountFormatItem", "groupKey", "amount", "trimmedItem", "lastDashIndex", "lastIndexOf", "number", "substring", "numberLength", "insertAmountFormatResult", "handleRemoveFormatInput", "output", "arr", "insertRemoveFormatResult", "handleUnderscoreFormatInput", "_this16", "insertUnderscoreFormatResult", "str", "_this17", "chineseNumberPattern", "chinese", "numMap", "unitMap", "temp", "hasNum", "char", "hasOwnProperty", "_this18", "lastIndex", "_this19", "getMaxSerialNumber", "increaseSerialNumber", "next", "generateNewSerialNumber", "_this20", "generateSerialNumber", "info", "autoGenerateSerialNumber", "_this21"], "sources": ["src/views/game/record/components/BetDialog.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\" width=\"600px\" append-to-body\r\n      :close-on-click-modal=\"false\" class=\"modern-bet-dialog\" style=\"height: 108%;  top: -55px;\">\r\n\r\n      <el-form ref=\"form\" :model=\"formData\" :rules=\"rules\" label-width=\"80px\" class=\"modern-bet-form\">\r\n        <div class=\"info-card\">\r\n          <!-- 福体开关行 -->\r\n          <div class=\"info-row\">\r\n            <div class=\"info-group\">\r\n              <div class=\"info-label\">\r\n                <i class=\"el-icon-s-operation\"></i>\r\n                <span>福体开关</span>\r\n              </div>\r\n              <el-switch v-model=\"futiSwitch\" active-value=\"all\" inactive-value=\"tc\" active-text=\"全部彩种\" inactive-text=\"仅体彩\"\r\n                @change=\"onFutiSwitchChange\"\r\n                size=\"small\"\r\n                class=\"lottery-switch\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 用户信息行 -->\r\n          <div class=\"info-row\">\r\n            <div class=\"info-group\">\r\n              <div class=\"info-label\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>当前用户</span>\r\n              </div>\r\n              <el-input v-model=\"currentUser.name\" disabled placeholder=\"未获取\" class=\"compact-input\" style=\"width: 100px;\" />\r\n            </div>\r\n            <div class=\"info-group\">\r\n              <div class=\"info-label\">\r\n                <i class=\"el-icon-switch-button\"></i>\r\n                <span>切换用户</span>\r\n              </div>\r\n              <el-select v-model=\"selectedUserId\" placeholder=\"请选择用户\" class=\"compact-select\" style=\"width: 120px;\">\r\n                <el-option v-for=\"user in userList\" :key=\"user.userId\" :label=\"user.name\" :value=\"user.userId\" />\r\n              </el-select>\r\n              <el-button\r\n                type=\"primary\"\r\n                :disabled=\"!selectedUserId\"\r\n                @click=\"switchUser\"\r\n                v-if=\"!row\"\r\n                class=\"compact-btn\"\r\n              >切换</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 流水号信息行 -->\r\n          <div class=\"info-row\">\r\n            <div class=\"info-group\">\r\n              <div class=\"info-label\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>流水号</span>\r\n              </div>\r\n              <el-input v-model.number=\"serialNumber\" placeholder=\"流水号\" class=\"compact-input\" style=\"width: 120px;\" />\r\n              <el-button type=\"success\" @click=\"generateNewSerialNumber\" :loading=\"generateLoading\" class=\"compact-btn\">\r\n                <i class=\"el-icon-refresh\"></i>\r\n                生成新流水号\r\n              </el-button>\r\n              <el-button type=\"primary\" @click=\"showFormatDialog\" class=\"compact-btn\">\r\n                <i class=\"el-icon-s-operation\"></i>\r\n                格式转换工具\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"showStatisticsButton\"\r\n                type=\"success\"\r\n                @click=\"showStatistics\"\r\n                class=\"compact-btn\">\r\n                <i class=\"el-icon-data-analysis\"></i>\r\n                下注统计\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 隐藏的期号字段，用于后端传输 -->\r\n          <div style=\"display: none;\">\r\n            <el-input v-model=\"formData.fc3dIssueNumber\" />\r\n            <el-input v-model=\"formData.tcIssueNumber\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"recognition-section\">\r\n          <el-form-item label=\"号码识别\" prop=\"shibie\">\r\n            <div class=\"recognition-wrapper\">\r\n              <div class=\"recognition-header\">\r\n                <i class=\"el-icon-view\"></i>\r\n                <span v-if=\"!isNumberCountExceeded\">智能识别</span>\r\n                <span v-else-if=\"isFirstNumberThreeDigits(formData.shibie)\" style=\"color: #67c23a;\">首组为三位数，不限制组数 ({{ numberCount }}组)</span>\r\n                <span v-else style=\"color: #f56c6c;\">单次识别不可超过30组，你现在是{{ numberCount }}组</span>\r\n                <el-button size=\"mini\" type=\"primary\" @click=\"showBetFormatDialog\">玩法格式</el-button>\r\n              </div>\r\n              <el-input v-model=\"formData.shibie\" type=\"textarea\" :rows=\"4\"\r\n                placeholder=\"请将号码输入此处，识别后自动生成下注号码\"\r\n                @input=\"handleNumberRecognition\" size=\"small\" :resize=\"'vertical'\"\r\n                :class=\"['recognition-textarea', { 'number-count-exceeded': isNumberCountExceeded }]\" />\r\n            </div>\r\n          </el-form-item>\r\n        </div>\r\n\r\n        <div class=\"bet-groups-list-wrapper\">\r\n          <div class=\"bet-groups-list\">\r\n            <div v-for=\"(group, idx) in formData.betGroups\" :key=\"idx\"\r\n              :class=\"['bet-group-card', { 'bet-group-even': idx % 2 === 1 }]\">\r\n              <div style=\"display:flex;align-items:center;gap:8px;margin-bottom:8px;\">\r\n                <el-form-item :label=\"'玩法名称'\" :prop=\"'betGroups.' + idx + '.methodId'\" style=\"flex:1;margin-bottom:0;\">\r\n                  <el-select v-model=\"group.methodId\"\r\n                             filterable\r\n                             remote\r\n                             :remote-method=\"filterMethod\"\r\n                             :loading=\"loadingGameMethods\"\r\n                             clearable\r\n                             placeholder=\"请选择玩法名称\"\r\n                             size=\"small\"\r\n                             style=\"width:100%\"\r\n                             @change=\"onMethodChange(group)\">\r\n                    <el-option v-for=\"item in filteredGameMethods\"\r\n                               :key=\"item.methodId\"\r\n                               :label=\"item.methodName\"\r\n                               :value=\"item.methodId\">\r\n                      {{ item.methodName }}\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item :label=\"'金额'\" :prop=\"'betGroups.' + idx + '.money'\" style=\"width:200px;margin-bottom:0;\">\r\n                  <el-input v-model=\"group.money\" placeholder=\"请输入金额\" size=\"small\" @input=\"updateTotals\" />\r\n                </el-form-item>\r\n                <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"removeBetGroup(idx)\" circle size=\"mini\" />\r\n              </div>\r\n\r\n              <div\r\n                v-if=\"group.methodId === 29 || group.methodId === 30 || group.methodId === 2 || [21, 22, 23, 24, 25, 26, 27, 37, 38, 39, 40, 41, 42, 43].includes(group.methodId)\"\r\n                style=\"margin-left: 80px; margin-bottom: 8px;\">\r\n                <template v-if=\"group.methodId === 29\">\r\n                  <div class=\"option-group\">\r\n                    <span class=\"option-label\" style=\"font-size: 16px;font-weight: 600;\">单双：</span>\r\n                    <el-radio-group v-model=\"group.danshuang\" size=\"medium\">\r\n                      <el-radio-button :label=\"200\">单</el-radio-button>\r\n                      <el-radio-button :label=\"201\">双</el-radio-button>\r\n                    </el-radio-group>\r\n                  </div>\r\n                </template>\r\n                <template v-else-if=\"group.methodId === 30\">\r\n                  <div class=\"option-group\">\r\n                    <span class=\"option-label\" style=\"font-size: 16px;font-weight: 600;\">大小：</span>\r\n                    <el-radio-group v-model=\"group.daxiao\" size=\"medium\">\r\n                      <el-radio-button :label=\"300\">大</el-radio-button>\r\n                      <el-radio-button :label=\"301\">小</el-radio-button>\r\n                    </el-radio-group>\r\n                  </div>\r\n                </template>\r\n                <template v-else-if=\"group.methodId === 2\">\r\n                  <div class=\"option-group\">\r\n                    <span class=\"option-label\" style=\"font-size: 16px;font-weight: 600;\">定位：</span>\r\n                    <el-radio-group v-model=\"group.dingwei\" size=\"medium\">\r\n                      <el-radio-button :label=\"100\">百位</el-radio-button>\r\n                      <el-radio-button :label=\"101\">十位</el-radio-button>\r\n                      <el-radio-button :label=\"102\">个位</el-radio-button>\r\n                    </el-radio-group>\r\n                  </div>\r\n                </template>\r\n                <template v-else-if=\"[21, 22, 23, 24, 25, 26, 27, 37, 38, 39, 40, 41, 42, 43].includes(group.methodId)\">\r\n                  <div class=\"option-group\">\r\n                    <span class=\"option-label\" style=\"font-size: 16px;font-weight: 600;\">和值：</span>\r\n                    <el-radio-group v-model=\"group.hezhi\" size=\"medium\">\r\n                      <el-radio-button v-for=\"hz in getHezhiOptions(group.methodId)\" :key=\"hz\"\r\n                        :label=\"hz\">和值{{ hz }}</el-radio-button>\r\n                    </el-radio-group>\r\n                  </div>\r\n                </template>\r\n              </div>\r\n\r\n              <el-form-item :label=\"'下注号码'\" :prop=\"'betGroups.' + idx + '.betNumbers'\" v-if=\"!isSpecialMethod(group.methodId)\"\r\n                style=\"margin-bottom:8px;\">\r\n                <el-input :value=\"getDisplayBetNumbers(group)\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入下注号码\"\r\n                  @input=\"onBetNumbersInput($event, idx)\" />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"彩种类型\" style=\"margin-bottom:8px;\">\r\n                <div style=\"display:flex;align-items:center;\">\r\n                  <el-radio-group v-model=\"group.lotteryId\" size=\"small\" style=\"margin-right:16px;flex-shrink:0;\"\r\n                    :fill=\"group.lotteryId === 2 ? '#1890ff' : '#ff4949'\">\r\n                    <el-radio-button :label=\"1\" data-lottery=\"fc3d\"\r\n                      style=\"margin-right:6px;\">福彩3D</el-radio-button>\r\n                    <el-radio-button :label=\"2\" data-lottery=\"tc\"\r\n                      style=\"margin-right:0;\">体彩排三</el-radio-button>\r\n                  </el-radio-group>\r\n                  <div class=\"group-summary\">\r\n                    <div class=\"summary-item amount\">\r\n                      <div class=\"summary-icon\">\r\n                        <i class=\"el-icon-wallet\"></i>\r\n                      </div>\r\n                      <div class=\"summary-content\">\r\n                        <div class=\"summary-label\">总额</div>\r\n                        <div class=\"summary-value\">￥{{ getGroupAmount(group) }}</div>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"summary-item bets\">\r\n                      <div class=\"summary-icon\">\r\n                        <i class=\"el-icon-tickets\"></i>\r\n                      </div>\r\n                      <div class=\"summary-content\">\r\n                        <div class=\"summary-label\">投注数</div>\r\n                        <div class=\"summary-value\">{{ getGroupBets(group) }}注</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </el-form-item>\r\n            </div>\r\n          </div>\r\n          <button class=\"bet-group-add-btn\" @click=\"addBetGroup\" title=\"添加投注组合\">\r\n            <!-- <i class=\"el-icon-plus\"></i> -->\r\n          </button>\r\n        </div>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-statistic title=\"下注总额\" :value=\"totalAmount\" size=\"small\">\r\n              <template slot=\"prefix\">￥</template>\r\n            </el-statistic>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-statistic title=\"总投注数\" :value=\"totalBets\" size=\"small\">\r\n              <template slot=\"suffix\">注</template>\r\n            </el-statistic>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-form-item label=\"是否结算\" prop=\"jiesuan\" v-if=\"dialogTitle === '修改下注管理'\">\r\n          <el-switch v-model=\"formData.jiesuan\" :active-value=\"1\" :inactive-value=\"0\" active-text=\"已结算\"\r\n            inactive-text=\"未结算\" size=\"small\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <div style=\"display: flex; align-items: center; justify-content: center; width: 100%;\">\r\n          <el-button type=\"primary\" @click=\"submitForm\" size=\"small\">确 定</el-button>\r\n          <el-button @click=\"close\" size=\"small\">取 消</el-button>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog\r\n      title=\"格式转换工具\"\r\n      :visible.sync=\"formatDialogVisible\"\r\n      width=\"650px\"\r\n      append-to-body\r\n      class=\"format-dialog\"\r\n      :close-on-click-modal=\"false\">\r\n\r\n      <div class=\"format-dialog-content\">\r\n        <el-tabs v-model=\"formatTab\" type=\"card\" class=\"format-tabs\">\r\n          <el-tab-pane name=\"unified\">\r\n            <span slot=\"label\">\r\n              <i class=\"el-icon-edit\"></i>\r\n              字符转换\r\n            </span>\r\n            <div class=\"tab-content\">\r\n              <div class=\"input-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-upload2\"></i>\r\n                  <span>输入内容</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"unifiedFormatInput\"\r\n                  placeholder=\"智能识别并转换：汉字金额(如二十)、逗号、句号、冒号、下划线、加号等转为空格&#10;示例：123,456。。789：：：012___345➕678二十元\"\r\n                  @input=\"handleUnifiedFormatInput\"\r\n                  class=\"format-input\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"arrow-section\">\r\n                <i class=\"el-icon-bottom\"></i>\r\n              </div>\r\n\r\n              <div class=\"output-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-download\"></i>\r\n                  <span>转换结果</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  :value=\"unifiedFormatOutput\"\r\n                  readonly\r\n                  placeholder=\"转换结果将在这里显示...\"\r\n                  class=\"format-output\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"button-section\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-check\"\r\n                  @click=\"insertUnifiedFormatResult\"\r\n                  :disabled=\"!unifiedFormatOutput\"\r\n                  class=\"action-button\">\r\n                  插入到号码识别\r\n                </el-button>\r\n                <el-button\r\n                  icon=\"el-icon-close\"\r\n                  @click=\"formatDialogVisible = false\"\r\n                  class=\"cancel-button\">\r\n                  关闭\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane name=\"chain\">\r\n            <span slot=\"label\">\r\n              <i class=\"el-icon-s-grid\"></i>\r\n              链子分类\r\n            </span>\r\n            <div class=\"tab-content\">\r\n              <div class=\"input-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-upload2\"></i>\r\n                  <span>输入内容</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"chainFormatInput\"\r\n                  placeholder=\"按号码长度自动分类到不同行\r\n示例：123-1234-568-56789-503-9012-12345678\"\r\n                  @input=\"handleChainFormatInput\"\r\n                  class=\"format-input\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"arrow-section\">\r\n                <i class=\"el-icon-bottom\"></i>\r\n              </div>\r\n\r\n              <div class=\"output-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-download\"></i>\r\n                  <span>分类结果</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"6\"\r\n                  :value=\"chainFormatOutput\"\r\n                  readonly\r\n                  placeholder=\"分类结果将在这里显示...\"\r\n                  class=\"format-output\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"button-section\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-check\"\r\n                  @click=\"insertChainFormatResult\"\r\n                  :disabled=\"!chainFormatOutput\"\r\n                  class=\"action-button\">\r\n                  插入到号码识别\r\n                </el-button>\r\n                <el-button\r\n                  icon=\"el-icon-close\"\r\n                  @click=\"formatDialogVisible = false\"\r\n                  class=\"cancel-button\">\r\n                  关闭\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane name=\"amount\">\r\n            <span slot=\"label\">\r\n              <i class=\"el-icon-money\"></i>\r\n              金额整合\r\n            </span>\r\n            <div class=\"tab-content\">\r\n              <div class=\"input-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-upload2\"></i>\r\n                  <span>输入内容</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"6\"\r\n                  v-model=\"amountFormatInput\"\r\n                  placeholder=\"将相同金额且相同位数的号码合并到一行\r\n  支持每行一个或一行内空格分隔多个\r\n  示例：57-50  59-50  79-50  88-100\r\n  结果： 57-59-79-50\r\n        88-100\"\r\n                  @input=\"handleAmountFormatInput\"\r\n                  class=\"format-input\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"arrow-section\">\r\n                <i class=\"el-icon-bottom\"></i>\r\n              </div>\r\n\r\n              <div class=\"output-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-download\"></i>\r\n                  <span>整合结果</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"6\"\r\n                  :value=\"amountFormatOutput\"\r\n                  readonly\r\n                  placeholder=\"整合结果将在这里显示...\"\r\n                  class=\"format-output\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"button-section\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-check\"\r\n                  @click=\"insertAmountFormatResult\"\r\n                  :disabled=\"!amountFormatOutput\"\r\n                  class=\"action-button\">\r\n                  插入到号码识别\r\n                </el-button>\r\n                <el-button\r\n                  icon=\"el-icon-close\"\r\n                  @click=\"formatDialogVisible = false\"\r\n                  class=\"cancel-button\">\r\n                  关闭\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane name=\"remove\">\r\n            <span slot=\"label\">\r\n              <i class=\"el-icon-delete\"></i>\r\n              去除分隔符\r\n            </span>\r\n            <div class=\"tab-content\">\r\n              <div class=\"input-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-upload2\"></i>\r\n                  <span>输入内容</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"6\"\r\n                  v-model=\"removeFormatInput\"\r\n                  placeholder=\"去除所有分隔符和英文字母，只保留汉字、数字和换行\r\n示例：123,456。。789：：：abc___def➕ghi二十元ABC\r\n结果：123456789二十元\"\r\n                  @input=\"handleRemoveFormatInput\"\r\n                  class=\"format-input\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"arrow-section\">\r\n                <i class=\"el-icon-bottom\"></i>\r\n              </div>\r\n\r\n              <div class=\"output-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-download\"></i>\r\n                  <span>清理结果</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"6\"\r\n                  :value=\"removeFormatOutput\"\r\n                  readonly\r\n                  placeholder=\"清理结果将在这里显示...\"\r\n                  class=\"format-output\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"button-section\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-check\"\r\n                  @click=\"insertRemoveFormatResult\"\r\n                  :disabled=\"!removeFormatOutput\"\r\n                  class=\"action-button\">\r\n                  插入到号码识别\r\n                </el-button>\r\n                <el-button\r\n                  icon=\"el-icon-close\"\r\n                  @click=\"formatDialogVisible = false\"\r\n                  class=\"cancel-button\">\r\n                  关闭\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 下注统计组件 -->\r\n    <bet-statistics :visible.sync=\"statisticsVisible\" />\r\n\r\n    <!-- 玩法格式弹窗组件 -->\r\n    <bet-format-dialog ref=\"betFormatDialog\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { handleNumberRecognition, handleSmartRecognition } from './numberRecognition.js';\r\nimport request from '@/utils/request';\r\nimport { getMaxSerialNumber } from '@/api/game/serial'\r\nimport { generateSerialNumber } from '@/api/game/record'\r\nimport { getCurrentQihao } from '@/api/game/qihao'\r\nimport { checkAndSetDefaultPlayer } from '@/api/game/customer'\r\nimport { getStatisticsPermission } from '@/api/system/user'\r\nimport BetStatistics from './BetStatistics.vue'\r\nimport BetFormatDialog from './BetFormatDialog.vue'\r\n\r\nexport default {\r\n  name: 'BetDialog',\r\n  components: {\r\n    BetStatistics,\r\n    BetFormatDialog\r\n  },\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: '新增下注'\r\n    },\r\n    row: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      totalAmount: 0,\r\n      totalBets: 0,\r\n      pendingRequests: [],\r\n      gameMethodsData: [], // 玩法数据\r\n      filteredGameMethods: [], // 用于 remote 搜索的玩法数据\r\n      loadingGameMethods: false, // 玩法加载状态\r\n      formData: {\r\n        fc3dIssueNumber: '',  // 福彩3D期号\r\n        tcIssueNumber: '',    // 体彩排三期号\r\n        shibie: '',\r\n        betGroups: [{\r\n          methodId: undefined,\r\n          money: '',\r\n          betNumbers: '',\r\n          danshuang: null,\r\n          daxiao: null,\r\n          dingwei: null,\r\n          hezhi: null,\r\n          lotteryId: 1\r\n        }]\r\n      },\r\n      // 新增：号码数量限制相关\r\n      numberCount: 0,\r\n      isNumberCountExceeded: false,\r\n      rules: {\r\n        betGroups: {\r\n          methodId: [{ required: true, message: '请选择玩法', trigger: 'change' }],\r\n          money: [{ required: true, message: '请输入金额', trigger: 'blur' }]\r\n        }\r\n      },\r\n      localForm: {\r\n        fc3dIssueNumber: '',  // 福彩3D期号\r\n        tcIssueNumber: '',    // 体彩排三期号\r\n        shibie: '',\r\n        betGroups: []\r\n      },\r\n      currentUser: { name: '' },\r\n      userList: [],\r\n      selectedUserId: null,\r\n      futiSwitch: sessionStorage.getItem('futiSwitch') || 'all',\r\n      formatDialogVisible: false,\r\n      formatInput: '',\r\n      formatOutput: '',\r\n      formatTab: 'unified',\r\n      colonFormatInput: '',\r\n      colonFormatOutput: '',\r\n      moneyFormatInput: '',\r\n      moneyFormatOutput: '',\r\n\r\n      underscoreFormatInput: '',\r\n      underscoreFormatOutput: '',\r\n      unifiedFormatInput: '',\r\n      unifiedFormatOutput: '',\r\n      chainFormatInput: '',\r\n      chainFormatOutput: '',\r\n      amountFormatInput: '',\r\n      amountFormatOutput: '',\r\n      removeFormatInput: '',\r\n      removeFormatOutput: '',\r\n      serialNumber: 1, // 新增本组序号\r\n      maxSerialNumber: 1, // 新增最大流水号\r\n      generateLoading: false, // 生成流水号加载状态\r\n      originalUserId: null, // 原始用户ID\r\n      originalSerialNumber: null, // 原始流水号\r\n      statisticsVisible: false, // 统计对话框显示状态\r\n      hasStatisticsPermission: false // 是否有统计权限\r\n    }\r\n  },\r\n  computed: {\r\n    /** 是否显示统计按钮 */\r\n    showStatisticsButton() {\r\n      return this.hasStatisticsPermission;\r\n    }\r\n  },\r\n  watch: {\r\n    visible(val) {\r\n      this.dialogVisible = val;\r\n      if (val) {\r\n        // 显示对话框时先检查玩家状态\r\n        this.checkPlayerStatus();\r\n        this.getAllUsers();\r\n        // 检查统计权限\r\n        this.checkStatisticsPermission();\r\n        // 新增：区分新增和修改，设置 selectedUserId\r\n        if (this.row && this.row.userId) {\r\n          // 修改时\r\n          this.selectedUserId = this.row.userId;\r\n        } else {\r\n          // 新增时，先检查session中是否有User_Id\r\n          let userId = sessionStorage.getItem('User_Id');\r\n          if (userId) {\r\n            this.selectedUserId = Number(userId);\r\n          } else {\r\n            // 如果没有，等待initializeFirstUser()设置\r\n            this.selectedUserId = null;\r\n          }\r\n        }\r\n        // 先初始化流水号，再设置表单数据\r\n      \r\n        this.initSerialNumber(); // 弹窗打开时初始化本组序号\r\n        // 如果是修改操作，初始化表单数据\r\n        if (this.row) {\r\n       \r\n          this.initFormData();\r\n        }\r\n        // 打开弹窗时根据开关状态处理\r\n        this.onFutiSwitchChange(this.futiSwitch);\r\n      }\r\n    },\r\n    dialogVisible(val) {\r\n      this.$emit('update:visible', val);\r\n      if (!val) {\r\n        this.resetForm();\r\n      }\r\n    },\r\n    // 监听用户ID变化，自动处理流水号\r\n    selectedUserId: {\r\n      handler(newUserId, oldUserId) {\r\n        // 只在修改模式下处理，且确保有原始数据\r\n        if (this.row && this.row.betId && this.originalUserId !== null && oldUserId !== undefined) {\r\n          this.handleUserIdChange(newUserId, oldUserId);\r\n        }\r\n      },\r\n      immediate: false\r\n    },\r\n    formatDialogVisible(val) {\r\n      if (!val) {\r\n        // 弹窗关闭时清空所有格式内容\r\n        this.formatInput = '';\r\n        this.formatOutput = '';\r\n        this.colonFormatInput = '';\r\n        this.colonFormatOutput = '';\r\n        this.moneyFormatInput = '';\r\n        this.moneyFormatOutput = '';\r\n\r\n        this.underscoreFormatInput = '';\r\n        this.underscoreFormatOutput = '';\r\n        this.unifiedFormatInput = '';\r\n        this.unifiedFormatOutput = '';\r\n        this.chainFormatInput = '';\r\n        this.chainFormatOutput = '';\r\n        this.amountFormatInput = '';\r\n        this.amountFormatOutput = '';\r\n        this.removeFormatInput = '';\r\n        this.removeFormatOutput = '';\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化时获取玩法列表\r\n    this.getGameMethods();\r\n  },\r\n  methods: {\r\n    // 显示玩法格式弹窗\r\n    showBetFormatDialog() {\r\n      this.$refs.betFormatDialog.show();\r\n    },\r\n    // 检查玩家状态\r\n    async checkPlayerStatus() {\r\n      try {\r\n        const response = await checkAndSetDefaultPlayer();\r\n\r\n        if (response.code === 500 && response.needCreatePlayer) {\r\n          // 没有玩家，显示提示并跳转\r\n          this.$confirm(response.msg + ' 是否立即前往创建？', '提示', {\r\n            confirmButtonText: '前往创建',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            // 关闭当前对话框\r\n            this.dialogVisible = false;\r\n            this.$emit('update:visible', false);\r\n            // 跳转到玩家页面\r\n            this.$router.push('/game/customer');\r\n          }).catch(() => {\r\n            // 用户取消，关闭对话框\r\n            this.dialogVisible = false;\r\n            this.$emit('update:visible', false);\r\n          });\r\n          return;\r\n        }\r\n\r\n        if (response.code === 200 && response.hasPlayers) {\r\n          // 有玩家，继续初始化\r\n          \r\n          if (response.defaultUserId) {\r\n            \r\n          }\r\n\r\n          // 继续初始化对话框\r\n          this.initializeDialog();\r\n        }\r\n      } catch (error) {\r\n        console.error('检查玩家状态失败:', error);\r\n        this.$message.error('检查玩家状态失败，请重试');\r\n        this.dialogVisible = false;\r\n        this.$emit('update:visible', false);\r\n      }\r\n    },\r\n\r\n    // 初始化对话框\r\n    initializeDialog() {\r\n      // 显示对话框时获取期号和玩法列表\r\n      this.getCurrentIssueNumber();\r\n      this.getGameMethods();\r\n      this.getCurrentUser();\r\n    },\r\n\r\n    // 获取玩法列表\r\n    getGameMethods() {\r\n      this.loadingGameMethods = true;\r\n      return request({\r\n        url: '/game/method/list',\r\n        method: 'get',\r\n        params: {\r\n          pageNum: 1,\r\n          pageSize: 1000  // 设置足够大的页面大小以获取所有数据\r\n        }\r\n      }).then(response => {\r\n        let data = [];\r\n        if (response.code === 200) {\r\n          if (Array.isArray(response.data)) {\r\n            data = response.data;\r\n          } else if (response.rows && Array.isArray(response.rows)) {\r\n            data = response.rows;\r\n          } else if (typeof response.data === 'string') {\r\n            try {\r\n              const parsedData = JSON.parse(response.data);\r\n              data = Array.isArray(parsedData) ? parsedData : [];\r\n            } catch (e) {\r\n              data = [];\r\n            }\r\n          }\r\n        }\r\n        this.gameMethodsData = data;\r\n        this.filteredGameMethods = data;\r\n        this.loadingGameMethods = false;\r\n      }).catch(() => {\r\n        this.gameMethodsData = [];\r\n        this.filteredGameMethods = [];\r\n        this.loadingGameMethods = false;\r\n      });\r\n    },\r\n    // 打开对话框\r\n    show(title = '新增下注') {\r\n      this.dialogTitle = title;\r\n      this.dialogVisible = true;\r\n    },\r\n    // 关闭对话框\r\n    close() {\r\n      this.dialogVisible = false;\r\n      this.$emit('update:visible', false);\r\n      this.$emit('cancel');  // 保持向后兼容\r\n    },\r\n    // 重置表单\r\n    resetForm() {\r\n      this.formData = {\r\n        fc3dIssueNumber: '',\r\n        tcIssueNumber: '',\r\n        shibie: '',\r\n        betGroups: [{\r\n          methodId: undefined,\r\n          money: '',\r\n          betNumbers: '',\r\n          danshuang: null,\r\n          daxiao: null,\r\n          dingwei: null,\r\n          hezhi: null,\r\n          lotteryId: 1\r\n        }]\r\n      };\r\n      this.totalAmount = 0;\r\n      this.totalBets = 0;\r\n      // 重置号码数量状态\r\n      this.numberCount = 0;\r\n      this.isNumberCountExceeded = false;\r\n    },\r\n    addBetGroup() {\r\n      const newGroup = {\r\n        methodId: undefined,\r\n        money: '',\r\n        betNumbers: '',\r\n        danshuang: null,\r\n        daxiao: null,\r\n        dingwei: null,\r\n        hezhi: null,\r\n        lotteryId: this.futiSwitch === 'tc' ? 2 : 1\r\n      };\r\n\r\n      // 直接操作formData\r\n      if (!this.formData.betGroups) {\r\n        this.formData.betGroups = [];\r\n      }\r\n      this.formData.betGroups.push({ ...newGroup });\r\n\r\n      this.$nextTick(() => {\r\n        this.updateTotals();\r\n      });\r\n    },\r\n    removeBetGroup(idx) {\r\n      if (this.formData.betGroups.length <= 1) {\r\n        // 如果只剩一个投注组，则清空它而不是删除\r\n        this.formData.betGroups = [{\r\n          methodId: undefined,\r\n          money: '',\r\n          betNumbers: '',\r\n          danshuang: null,\r\n          daxiao: null,\r\n          dingwei: null,\r\n          hezhi: null,\r\n          lotteryId: 1\r\n        }];\r\n      } else {\r\n        // 如果有多个投注组，则删除指定的组\r\n        this.formData.betGroups.splice(idx, 1);\r\n      }\r\n\r\n      this.$nextTick(() => {\r\n        this.updateTotals();\r\n        this.$forceUpdate(); // 强制更新视图\r\n      });\r\n    },\r\n    updateTotals() {\r\n      this.totalAmount = this.formData.betGroups.reduce((sum, g) => sum + Number(this.getGroupAmount(g)), 0);\r\n      this.totalBets = this.formData.betGroups.reduce((sum, g) => sum + Number(this.getGroupBets(g)), 0);\r\n    },\r\n    getHezhiOptions(methodId) {\r\n      const map = {\r\n        21: [7, 20],\r\n        22: [8, 19],\r\n        23: [9, 18],\r\n        24: [10, 17],\r\n        25: [11, 16],\r\n        26: [12, 15],\r\n        27: [13, 14],\r\n        37: [0, 27],\r\n        38: [1, 26],\r\n        39: [2, 25],\r\n        40: [3, 24],\r\n        41: [4, 23],\r\n        42: [5, 22],\r\n        43: [6, 21]\r\n      };\r\n      return map[methodId] || [];\r\n    },\r\n    onMethodChange(group) {\r\n      \r\n      const index = this.formData.betGroups.findIndex(g => g === group);\r\n      if (index > -1) {\r\n        // 创建一个新的组对象，确保包含所有必要的字段\r\n        const updatedGroup = {\r\n          ...group,\r\n          danshuang: null,\r\n          daxiao: null,\r\n          dingwei: null,\r\n          hezhi: null\r\n          // 不重置 betNumbers 和 money\r\n        };\r\n\r\n        // 根据玩法类型设置默认值\r\n        if (updatedGroup.methodId === 29) {\r\n          updatedGroup.danshuang = 200;\r\n        } else if (updatedGroup.methodId === 30) {\r\n          updatedGroup.daxiao = 300;\r\n        } else if (updatedGroup.methodId === 2) {\r\n          updatedGroup.dingwei = 100;\r\n        } else if ([21, 22, 23, 24, 25, 26, 27, 37, 38, 39, 40, 41, 42, 43].includes(updatedGroup.methodId)) {\r\n          const options = this.getHezhiOptions(updatedGroup.methodId);\r\n          if (options && options.length > 0) {\r\n            updatedGroup.hezhi = options[0];\r\n          }\r\n        }\r\n\r\n        // 更新组数据\r\n        this.$set(this.formData.betGroups, index, updatedGroup);\r\n        this.$forceUpdate();\r\n      }\r\n    },\r\n    // 获取当前期号\r\n    getCurrentIssueNumber() {\r\n      const loadingInstance = this.$loading({\r\n        lock: true,\r\n        text: '正在获取期号...',\r\n        spinner: 'el-icon-loading',\r\n        background: 'rgba(0, 0, 0, 0.7)',\r\n        customClass: 'bet-dialog-loading'\r\n      });\r\n\r\n      // 使用Promise.all同时获取两个期号\r\n      Promise.all([\r\n        // 获取福彩3D期号\r\n        getCurrentQihao(1).catch(error => {\r\n          console.error('获取福彩3D期号失败:', error);\r\n          return { code: 500, msg: '获取失败' };\r\n        }),\r\n        // 获取体彩排三期号\r\n        getCurrentQihao(2).catch(error => {\r\n          console.error('获取体彩排三期号失败:', error);\r\n          return { code: 500, msg: '获取失败' };\r\n        })\r\n      ]).then(([fc3dResponse, tcResponse]) => {\r\n        // 处理福彩3D期号\r\n        if (fc3dResponse.code === 200) {\r\n          this.formData.fc3dIssueNumber = fc3dResponse.msg;\r\n          \r\n        } else {\r\n          console.error('获取福彩3D期号失败:', fc3dResponse);\r\n          this.$message.error('获取福彩3D期号失败');\r\n        }\r\n\r\n        // 处理体彩排三期号\r\n        if (tcResponse.code === 200) {\r\n          this.formData.tcIssueNumber = tcResponse.msg;\r\n         \r\n        } else {\r\n          console.error('获取体彩排三期号失败:', tcResponse);\r\n          this.$message.error('获取体彩排三期号失败');\r\n        }\r\n      }).finally(() => {\r\n        loadingInstance.close();\r\n      });\r\n    },\r\n    // 新增：动态计算号码数量的方法\r\n    calculateNumberCount(input) {\r\n      if (!input || !input.trim()) return 0;\r\n\r\n      // 清理输入，去除首尾空白\r\n      const cleanInput = input.trim();\r\n\r\n      // 使用更精确的正则匹配号码组\r\n      // 匹配2-9位连续数字，考虑各种分隔符\r\n      const numberPattern = /\\d{2,9}/g;\r\n      const allMatches = cleanInput.match(numberPattern);\r\n\r\n      if (!allMatches) return 0;\r\n\r\n      // 按行分析，更准确地识别号码和金额\r\n      const lines = cleanInput.split(/[\\r\\n]+/).filter(line => line.trim());\r\n      let totalCount = 0;\r\n\r\n      for (const line of lines) {\r\n        const trimmedLine = line.trim();\r\n        if (!trimmedLine) continue;\r\n\r\n        const lineNumbers = trimmedLine.match(numberPattern);\r\n        if (!lineNumbers) continue;\r\n\r\n        // 检查金额相关关键字\r\n        const moneyKeywords = /[元块米钱赶组直吊放防各]/;\r\n        const hasMoneyKeyword = moneyKeywords.test(trimmedLine);\r\n\r\n        // 检查是否有明显的金额分隔符（如+、/等）\r\n        const hasMoneyDelimiter = /[+＋\\/]/.test(trimmedLine);\r\n\r\n        if (hasMoneyKeyword || hasMoneyDelimiter) {\r\n          // 如果包含金额关键字或分隔符，最后1-2个数字可能是金额\r\n          // 更保守的估计：减去1个可能的金额\r\n          totalCount += Math.max(0, lineNumbers.length - 1);\r\n        } else {\r\n          // 纯号码行，所有数字都算作号码\r\n          totalCount += lineNumbers.length;\r\n        }\r\n      }\r\n\r\n      return totalCount;\r\n    },\r\n    // 智能预处理批量号码+金额输入，避免金额被误识别为号码\r\n    smartPreprocessInput(input) {\r\n      if (!input) return input;\r\n      // 支持多行或\"—\"等超级分隔符\r\n      let parts = input.split(/[—\\-\\n\\r]+/).map(s => s.trim()).filter(Boolean);\r\n      if (parts.length > 1) {\r\n        // 金额表达关键字更宽松，兼容赶/组/直/吊/放/防/元/各等\r\n        const moneyExpr = /(赶|组|直|吊|放|防|元|各)[^\\d]*\\d+.*$/;\r\n        const last = parts[parts.length - 1];\r\n        if (moneyExpr.test(last)) {\r\n          // 合并为\"号码—号码—...—金额表达\"\r\n          return parts.slice(0, -1).join('—') + '—' + last;\r\n        }\r\n      }\r\n      return input;\r\n    },\r\n    // 新增：检查首组号码是否为三位数的方法\r\n    isFirstNumberThreeDigits(input) {\r\n      if (!input || !input.trim()) return false;\r\n\r\n      // 清理输入，去除首尾空白\r\n      const cleanInput = input.trim();\r\n\r\n      // 匹配第一个2-9位连续数字\r\n      const firstNumberMatch = cleanInput.match(/\\d{2,9}/);\r\n\r\n      if (!firstNumberMatch) return false;\r\n\r\n      // 检查第一个号码是否为三位数\r\n      return firstNumberMatch[0].length === 3;\r\n    },\r\n    handleNumberRecognition(value) {\r\n      // 实时计算并更新号码数量\r\n      this.numberCount = this.calculateNumberCount(value);\r\n\r\n      // 判断如果首组号码为三位数则不限制号码组数为30\r\n      const isFirstThreeDigits = this.isFirstNumberThreeDigits(value);\r\n      this.isNumberCountExceeded = isFirstThreeDigits ? false : this.numberCount > 30;\r\n\r\n      if (!value) {\r\n        this.formData.betGroups = [{\r\n          methodId: undefined,\r\n          money: '',\r\n          betNumbers: '',\r\n          danshuang: null,\r\n          daxiao: null,\r\n          dingwei: null,\r\n          hezhi: null,\r\n          lotteryId: this.futiSwitch === 'tc' ? 2 : 1\r\n        }];\r\n        return;\r\n      }\r\n\r\n      // 如果超过30个号码，不进行识别处理，但仍然显示计数\r\n      if (this.isNumberCountExceeded) {\r\n        \r\n        return;\r\n      }\r\n      // 1. 先整体识别（必须用原始 value，不能预处理！）\r\n      let result = handleNumberRecognition(value);\r\n      // 2. 如果整体没命中，再按行分割，每行单独识别，不做任何合并\r\n      if (!result || !result.groups || !result.groups.some(g => g.methodId)) {\r\n        const lines = value.split(/[\\r\\n]+/).map(line => line.trim()).filter(Boolean);\r\n        let allGroups = [];\r\n        for (const line of lines) {\r\n          const lineResult = handleNumberRecognition(line);\r\n          if (lineResult && lineResult.groups && lineResult.groups.length > 0) {\r\n            allGroups = allGroups.concat(lineResult.groups);\r\n          }\r\n        }\r\n        result = { groups: allGroups };\r\n      }\r\n      \r\n      \r\n\r\n      if (result) {\r\n        // 如果存在groups，说明有多个和值组\r\n        if (result.groups && result.groups.length > 0) {\r\n     \r\n          // 清空现有的投注组\r\n          this.$set(this.formData, 'betGroups', []);\r\n          // 添加所有和值组\r\n          result.groups.forEach(group => {\r\n            const newGroup = {\r\n              methodId: group.methodId,\r\n              danshuang: group.danshuang,\r\n              daxiao: group.daxiao,\r\n              dingwei: group.dingwei,\r\n              hezhi: group.hezhi,\r\n              betNumbers: group.betNumbers,\r\n              money: group.money != null ? String(group.money) : '', // 确保money为字符串类型\r\n              lotteryId: this.futiSwitch === 'tc' ? 2 : (group.lotteryId || 1)\r\n            };\r\n\r\n            this.formData.betGroups.push(newGroup);\r\n          });\r\n        } else {\r\n\r\n          // 单个投注组的情况\r\n          const newGroup = {\r\n            methodId: result.methodId,\r\n            danshuang: result.danshuang,\r\n            daxiao: result.daxiao,\r\n            dingwei: result.dingwei,\r\n            hezhi: result.hezhi,\r\n            betNumbers: result.betNumbers,\r\n            money: result.money != null ? String(result.money) : '', // 确保money为字符串类型\r\n            lotteryId: this.futiSwitch === 'tc' ? 2 : (result.lotteryId || 1)\r\n          };\r\n\r\n          this.$set(this.formData, 'betGroups', [newGroup]);\r\n        }\r\n\r\n        // 同步到localForm\r\n        const localGroups = this.formData.betGroups.map(group => ({\r\n          ...group,\r\n          danshuang: group.danshuang || null,\r\n          daxiao: group.daxiao || null,\r\n          dingwei: group.dingwei || null,\r\n          hezhi: group.hezhi || null,\r\n          betNumbers: group.betNumbers || '',\r\n          money: group.money || null,\r\n          lotteryId: group.lotteryId || 1\r\n        }));\r\n   \r\n        this.$set(this.localForm, 'betGroups', localGroups);\r\n\r\n        // 强制更新视图\r\n        this.$nextTick(() => {\r\n          \r\n          this.updateTotals();\r\n          // 确保每个投注组都正确更新\r\n          this.formData.betGroups.forEach((group, index) => {\r\n            const updatedGroup = { ...group };\r\n           \r\n            this.$set(this.formData.betGroups, index, updatedGroup);\r\n          });\r\n    \r\n          this.$forceUpdate();\r\n        });\r\n      }\r\n    },\r\n    formatBetNumbersToDisplay(jsonStr) {\r\n      try {\r\n        if (!jsonStr) return '';\r\n\r\n        // 如果不是JSON格式，直接返回\r\n        if (!jsonStr.startsWith('{')) {\r\n          return jsonStr;\r\n        }\r\n\r\n        const data = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr;\r\n        if (!data.numbers || !Array.isArray(data.numbers)) return jsonStr;\r\n\r\n        return data.numbers.map(num => {\r\n          // 跨度玩法特殊处理\r\n          if (num.kuadu !== undefined) {\r\n            return `跨度${num.kuadu}`;\r\n          }\r\n          // 胆拖玩法特殊处理\r\n          if (num.danma !== undefined && num.tuoma !== undefined) {\r\n            return `胆${num.danma}拖${num.tuoma}`;\r\n          }\r\n          // 其他玩法\r\n          const values = Object.values(num);\r\n          return values.join('');\r\n        }).join(',');\r\n      } catch (error) {\r\n        console.error('格式化下注号码失败:', error, 'jsonStr:', jsonStr);\r\n        return jsonStr;\r\n      }\r\n    },\r\n    // 获取用于显示的下注号码\r\n    getDisplayBetNumbers(group) {\r\n      if (!group.betNumbers) return '';\r\n\r\n      // 跨度玩法 (60-69) 和胆拖玩法 (44-59) 需要特殊格式化\r\n      if ((group.methodId >= 44 && group.methodId <= 59) || (group.methodId >= 60 && group.methodId <= 69)) {\r\n        try {\r\n          // 检查是否是JSON格式\r\n          if (group.betNumbers.startsWith('{') && group.betNumbers.includes('numbers')) {\r\n            return this.formatBetNumbersToDisplay(group.betNumbers);\r\n          }\r\n          // 如果不是JSON格式，直接返回（用户正在编辑）\r\n          return group.betNumbers;\r\n        } catch (error) {\r\n          console.error('格式化显示号码失败:', error);\r\n          return group.betNumbers;\r\n        }\r\n      }\r\n\r\n      // 其他玩法直接显示\r\n      return group.betNumbers;\r\n    },\r\n    // 处理下注号码输入\r\n    onBetNumbersInput(value, index) {\r\n      const group = this.formData.betGroups[index];\r\n\r\n      // 跨度玩法和胆拖玩法需要保持JSON格式存储，但显示为用户友好格式\r\n      if ((group.methodId >= 44 && group.methodId <= 59) || (group.methodId >= 60 && group.methodId <= 69)) {\r\n        // 对于这些特殊玩法，用户输入的友好格式需要转换为JSON格式存储\r\n        // 但这里我们暂时直接存储用户输入，让识别逻辑处理\r\n        this.$set(this.formData.betGroups, index, { ...group, betNumbers: value });\r\n      } else {\r\n        // 其他玩法直接存储\r\n        this.$set(this.formData.betGroups, index, { ...group, betNumbers: value });\r\n      }\r\n\r\n      this.updateTotals();\r\n    },\r\n    initFormData() {\r\n      try {\r\n        // 保存原始数据\r\n        this.originalUserId = this.row.userId;\r\n        this.originalSerialNumber = this.row.serialNumber;\r\n\r\n        // 设置期号\r\n        this.formData.fc3dIssueNumber = this.row.issueNumber;\r\n        this.formData.tcIssueNumber = this.row.issueNumber;\r\n\r\n        // 格式化下注号码\r\n        const formattedBetNumbers = this.formatBetNumbersToDisplay(this.row.betNumbers);\r\n\r\n        // 创建投注组\r\n        const group = {\r\n          methodId: this.row.methodId,\r\n          money: this.row.money != null ? String(this.row.money) : '', // 确保money为字符串类型\r\n          betNumbers: formattedBetNumbers, // 使用格式化后的号码\r\n          danshuang: this.row.danshuang,\r\n          daxiao: this.row.daxiao,\r\n          dingwei: this.row.dingwei,\r\n          hezhi: this.row.hezhi,\r\n          lotteryId: this.row.lotteryId\r\n        };\r\n\r\n        // 设置投注组\r\n        this.formData.betGroups = [group];\r\n\r\n        // 如果有识别文本，也设置\r\n        if (this.row.shibie) {\r\n          this.formData.shibie = this.row.shibie;\r\n        }\r\n\r\n        // 如果有流水号，也设置\r\n        if (this.row.serialNumber) {\r\n       \r\n          this.serialNumber = this.row.serialNumber;\r\n        }\r\n\r\n        // 更新总计\r\n        this.$nextTick(() => {\r\n          this.updateTotals();\r\n          // 强制更新视图以确保特殊玩法正确显示\r\n          this.$forceUpdate();\r\n        });\r\n      } catch (error) {\r\n        console.error('初始化表单数据失败:', error);\r\n      }\r\n    },\r\n    formatNumbersForRequest(betNumbers, methodId) {\r\n      if (!betNumbers) return { numbers: [] };\r\n\r\n      // 胆拖玩法 (44-59) - 只存储danma和tuoma\r\n      if (methodId >= 44 && methodId <= 59) {\r\n        return this.formatDantuoNumbers(betNumbers, methodId);\r\n      }\r\n\r\n      // 跨度玩法 (60-69) - 只存储kuadu\r\n      if (methodId >= 60 && methodId <= 69) {\r\n        return this.formatKuaduNumbers(betNumbers, methodId);\r\n      }\r\n\r\n      // 分割号码组\r\n      const numberGroups = betNumbers.split(',').filter(n => n.trim());\r\n\r\n      // 根据玩法格式化\r\n      const formatMap = {\r\n        // 一位数玩法（独胆、一码定位、杀码等）\r\n        1: (num) => ({ a: parseInt(num) }),\r\n        2: (num) => ({ a: parseInt(num) }),\r\n        33: (num) => ({ a: parseInt(num) }),\r\n        34: (num) => ({ a: num }), // 两码定位，保持原始模式如\"9*6\"\r\n        35: (num) => ({ a: num }), // 一码单双\r\n        36: (num) => ({ a: num }), // 一码大小\r\n        // 两位数玩法（两码组合、对子等）\r\n        3: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1] };\r\n        },\r\n        4: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1] };\r\n        },\r\n        // 两码组三\r\n        70: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1] };\r\n        },\r\n\r\n        // 三位数玩法（三码直选、组选、防对等）\r\n        5: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2] };\r\n        },\r\n        6: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2] };\r\n        },\r\n        7: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2] };\r\n        },\r\n        100: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2] };\r\n        },\r\n\r\n        // 四码\r\n        8: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3] };\r\n        },\r\n        9: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3] };\r\n        },\r\n\r\n        // 五码\r\n        10: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4] };\r\n        },\r\n        11: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4] };\r\n        },\r\n\r\n        // 六码\r\n        12: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5] };\r\n        },\r\n        13: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5] };\r\n        },\r\n\r\n        // 七码\r\n        14: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6] };\r\n        },\r\n        15: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6] };\r\n        },\r\n\r\n        // 八码\r\n        16: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6], h: digits[7] };\r\n        },\r\n        17: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6], h: digits[7] };\r\n        },\r\n\r\n        // 九码\r\n        18: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6], h: digits[7], i: digits[8] };\r\n        },\r\n        19: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6], h: digits[7], i: digits[8] };\r\n        },\r\n\r\n        // 包打组六、组三（不需要号码，返回空对象）\r\n        20: () => ({}),\r\n        31: () => ({}),\r\n\r\n        // 和值玩法（和值直接传递，不需要号码）\r\n        21: () => ({}),\r\n        22: () => ({}),\r\n        23: () => ({}),\r\n        24: () => ({}),\r\n        25: () => ({}),\r\n        26: () => ({}),\r\n        27: () => ({}),\r\n\r\n        // 新增和值玩法（和值直接传递，不需要号码）\r\n        37: () => ({}),\r\n        38: () => ({}),\r\n        39: () => ({}),\r\n        40: () => ({}),\r\n        41: () => ({}),\r\n        42: () => ({}),\r\n        43: () => ({}),\r\n\r\n        // 直选复式（暂时按空对象处理）\r\n        28: () => ({}),\r\n\r\n        // 和值单双\r\n        29: () => ({}),\r\n        // 和值大小\r\n        30: () => ({}),\r\n\r\n      };\r\n\r\n      const formatter = formatMap[methodId];\r\n      if (!formatter) return { numbers: [] };\r\n\r\n      return {\r\n        numbers: numberGroups.map(num => formatter(num.trim()))\r\n      };\r\n    },\r\n    submitForm() {\r\n      // 先验证金额\r\n      const moneyValidation = this.validateMoney();\r\n      if (!moneyValidation.valid) {\r\n        this.$alert(moneyValidation.message, '错误', {\r\n          confirmButtonText: '确定',\r\n          type: 'error',\r\n          dangerouslyUseHTMLString: true\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          const loadingInstance = this.$loading({\r\n            lock: true,\r\n            text: '正在提交下注...',\r\n            spinner: 'el-icon-loading',\r\n            background: 'rgba(0, 0, 0, 0.7)'\r\n          });\r\n\r\n          // 修正 userId 赋值逻辑\r\n          const betRecords = this.formData.betGroups.map((group, index) => {\r\n            // 格式化下注号码为JSON格式\r\n            const numbers = this.formatNumbersForRequest(group.betNumbers, group.methodId);\r\n            // 从 sessionStorage 获取 sysUserId\r\n            const sysUserId = sessionStorage.getItem('sysUserId');\r\n\r\n            const record = {\r\n              lotteryId: group.lotteryId,\r\n              issueNumber: group.lotteryId === 1 ? this.formData.fc3dIssueNumber : this.formData.tcIssueNumber,\r\n              methodId: group.methodId,\r\n              // 修改：userId 始终用当前选择的 selectedUserId\r\n              userId: Number(this.selectedUserId),\r\n              // 新增：从 sessionStorage 获取并传递 sysUserId\r\n              sysUserId: sysUserId ? Number(sysUserId) : null,\r\n              betNumbers: JSON.stringify(numbers),\r\n              money: group.money,\r\n              danshuang: group.danshuang,\r\n              daxiao: group.daxiao,\r\n              dingwei: group.dingwei,\r\n              hezhi: group.hezhi,\r\n              totalAmount: this.getGroupAmount(group),\r\n              totalBets: this.getGroupBets(group),\r\n              shibie: this.formData.shibie,\r\n              betTime: this.formatDate(new Date()),\r\n              serialNumber: this.serialNumber // 新增：传递流水号\r\n            };\r\n            // 如果是修改操作，添加betId\r\n            if (this.row && index === 0) {\r\n              record.betId = this.row.betId;\r\n            }\r\n            // 逐行打印每个 record\r\n          \r\n            return record;\r\n          });\r\n\r\n          // 提交前打印所有 betRecords\r\n       \r\n\r\n          // 根据是否是修改操作选择不同的提交方式\r\n          const url = this.row ? '/game/record' : '/game/record/batch';\r\n          const method = this.row ? 'put' : 'post';\r\n          const data = this.row ? betRecords[0] : betRecords;\r\n\r\n          request({\r\n            url,\r\n            method,\r\n            data\r\n          }).then(response => {\r\n            if (response.code === 200) {\r\n              this.$modal.msgSuccess('操作成功');\r\n              this.$emit('success');\r\n              // 清空号码识别框和投注组\r\n              this.formData.shibie = '';\r\n              this.formData.betGroups = [{\r\n                methodId: undefined,\r\n                money: '',\r\n                betNumbers: '',\r\n                danshuang: null,\r\n                daxiao: null,\r\n                dingwei: null,\r\n                hezhi: null,\r\n                lotteryId: 1\r\n              }];\r\n              this.updateTotals();\r\n              // 新增：仅在修改时关闭弹窗，新增时不关闭\r\n              if (this.row) {\r\n                this.close();\r\n              }\r\n            } else {\r\n              // 如果后端返回了具体哪些投注失败\r\n              if (response.data && Array.isArray(response.data.failedIndices)) {\r\n                // 保留失败的投注组\r\n                const failedGroups = this.formData.betGroups.filter((_, index) =>\r\n                  response.data.failedIndices.includes(index)\r\n                );\r\n                this.$set(this.formData, 'betGroups', failedGroups);\r\n                this.$modal.msgError(`${response.data.failedIndices.length}个下注失败，请重试`);\r\n              } else {\r\n                // 检查错误是否已经被响应拦截器处理\r\n                if (response._errorHandled) {\r\n                  // 错误已经被响应拦截器处理并显示，这里不再重复处理\r\n          \r\n                } else {\r\n                  // 未被处理的错误，显示通用错误信息\r\n                  this.$modal.msgError(response.msg || '操作失败，请重试');\r\n                }\r\n              }\r\n            }\r\n          }).catch(error => {\r\n            console.error('网络请求失败:', error);\r\n\r\n            // 现在只有真正的网络错误才会进入 catch 块\r\n            // 业务错误已经在响应拦截器中处理并返回到 then 块\r\n            this.$modal.msgError('网络连接失败，请检查网络后重试');\r\n          }).finally(() => {\r\n            loadingInstance.close();\r\n            this.updateTotals();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    getGroupBets(group) {\r\n      // 胆拖玩法 (44-59) - 始终显示为1注\r\n      if (group.methodId >= 44 && group.methodId <= 59) {\r\n        return 1;\r\n      }\r\n\r\n      // 跨度玩法 (60-69) - 始终显示为1注\r\n      if (group.methodId >= 60 && group.methodId <= 69) {\r\n        return 1;\r\n      }\r\n\r\n      if (this.isSpecialMethod(group.methodId)) {\r\n        if ([21,22,23,24,25,26,27,37,38,39,40,41,42,43].includes(group.methodId)) {\r\n          return group.hezhi ? 1 : 0;\r\n        }\r\n        if ([29,30].includes(group.methodId)) {\r\n          if (group.danshuang || group.daxiao) {\r\n            return 1;\r\n          }\r\n          return 0;\r\n        }\r\n        if ([20,31].includes(group.methodId)) {  // 包打组六和包打组三\r\n          return 1;\r\n        }\r\n        return 0;\r\n      }\r\n      if (group.betNumbers) {\r\n        return group.betNumbers.split(',').filter(s => s.trim()).length;\r\n      }\r\n      return 0;\r\n    },\r\n    getGroupAmount(group) {\r\n      const bets = this.getGroupBets(group);\r\n      const money = Number(group.money) || 0;\r\n      return bets * money;\r\n    },\r\n    isSpecialMethod(methodId) {\r\n      return [20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 37, 38, 39, 40, 41, 42, 43].includes(methodId);\r\n    },\r\n    // 胆拖格式化方法\r\n    formatDantuoNumbers(betNumbers, methodId) {\r\n      // 解析胆码和拖码\r\n      const parts = betNumbers.split('拖');\r\n      if (parts.length !== 2) return { numbers: [] };\r\n\r\n      const danma = parts[0].replace(/[^0-9]/g, '');\r\n      const tuoma = parts[1].replace(/[^0-9,，\\s]/g, '').split(/[,，\\s]+/).filter(n => n && n !== danma);\r\n\r\n      return {\r\n        numbers: [{\r\n          danma: danma,\r\n          tuoma: tuoma.join(',')\r\n        }]\r\n      };\r\n    },\r\n    // 跨度格式化方法\r\n    formatKuaduNumbers(betNumbers, methodId) {\r\n      console.log('formatKuaduNumbers 输入:', betNumbers, 'methodId:', methodId);\r\n\r\n      // 如果已经是JSON格式，直接返回\r\n      if (betNumbers.startsWith('{')) {\r\n        try {\r\n          const parsed = JSON.parse(betNumbers);\r\n          console.log('已是JSON格式:', parsed);\r\n          return parsed;\r\n        } catch (error) {\r\n          console.error('JSON解析失败:', error);\r\n        }\r\n      }\r\n\r\n      // 解析跨度值 - 支持多种格式\r\n      let kuaduValue = null;\r\n\r\n      // 格式1: \"跨度5\"\r\n      let match = betNumbers.match(/跨度(\\d)/);\r\n      if (match) {\r\n        kuaduValue = match[1];\r\n      } else {\r\n        // 格式2: \"跨5-50\" 或 \"跨5 50\"\r\n        match = betNumbers.match(/跨(\\d)[\\s\\-]*\\d+/);\r\n        if (match) {\r\n          kuaduValue = match[1];\r\n        } else {\r\n          // 格式3: 从methodId推导跨度值\r\n          if (methodId >= 60 && methodId <= 69) {\r\n            kuaduValue = (methodId - 60).toString();\r\n          }\r\n        }\r\n      }\r\n\r\n      console.log('解析出的跨度值:', kuaduValue);\r\n\r\n      if (kuaduValue === null) {\r\n        console.warn('无法解析跨度值，返回空数组');\r\n        return { numbers: [] };\r\n      }\r\n\r\n      const result = {\r\n        numbers: [{\r\n          kuadu: kuaduValue\r\n        }]\r\n      };\r\n\r\n      console.log('formatKuaduNumbers 输出:', result);\r\n      return result;\r\n    },\r\n    updateGroupValue(group, field, value) {\r\n\r\n      const index = this.formData.betGroups.findIndex(g => g === group);\r\n      if (index > -1) {\r\n        const updatedGroup = {\r\n          ...this.formData.betGroups[index],\r\n          [field]: value\r\n        };\r\n        this.$set(this.formData.betGroups, index, updatedGroup);\r\n        this.$forceUpdate();\r\n      }\r\n    },\r\n\r\n\r\n    // 格式化日期为 yyyy-MM-dd HH:mm:ss\r\n    formatDate(date) {\r\n      const pad = (num) => (num < 10 ? `0${num}` : num);\r\n      const year = date.getFullYear();\r\n      const month = pad(date.getMonth() + 1);\r\n      const day = pad(date.getDate());\r\n      const hours = pad(date.getHours());\r\n      const minutes = pad(date.getMinutes());\r\n      const seconds = pad(date.getSeconds());\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n    getCurrentUser() {\r\n      let userId = sessionStorage.getItem('User_Id');\r\n\r\n      if (userId) {\r\n        // 如果session中有User_Id，直接使用\r\n        this.selectedUserId = Number(userId);\r\n        this.loadUserById(userId);\r\n      } else {\r\n        // 如果session中没有User_Id，先获取用户列表，然后使用第一个用户\r\n        this.initializeFirstUser();\r\n      }\r\n    },\r\n\r\n    loadUserById(userId) {\r\n      request({\r\n        url: `/game/customer/${userId}`,\r\n        method: 'get'\r\n      }).then(res => {\r\n        if (res.code === 200 && res.data) {\r\n          this.currentUser = res.data;\r\n        } else {\r\n          this.currentUser = { name: '未知用户' };\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取用户信息失败:', error);\r\n        this.currentUser = { name: '未知用户' };\r\n      });\r\n    },\r\n\r\n    initializeFirstUser() {\r\n      // 获取当前用户的玩家列表\r\n      request({\r\n        url: '/game/customer/list',\r\n        method: 'get',\r\n        params: { pageNum: 1, pageSize: 1000 }\r\n      }).then(res => {\r\n        if (res.code === 200 && res.rows && res.rows.length > 0) {\r\n          // 使用第一个玩家作为默认用户\r\n          const firstUser = res.rows[0];\r\n          this.selectedUserId = firstUser.userId;\r\n          this.currentUser = firstUser;\r\n          // 保存到session中\r\n          sessionStorage.setItem('User_Id', firstUser.userId);\r\n        \r\n        } else {\r\n          console.warn('当前用户没有玩家数据');\r\n          this.currentUser = { name: '无玩家数据' };\r\n          this.selectedUserId = null;\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取玩家列表失败:', error);\r\n        this.currentUser = { name: '获取失败' };\r\n        this.selectedUserId = null;\r\n      });\r\n    },\r\n    getAllUsers() {\r\n      request({\r\n        url: '/game/customer/list',\r\n        method: 'get',\r\n        params: { pageNum: 1, pageSize: 1000 }\r\n      }).then(res => {\r\n        if (res.code === 200 && res.rows) {\r\n          this.userList = res.rows;\r\n        } else {\r\n          this.userList = [];\r\n        }\r\n      });\r\n    },\r\n    switchUser() {\r\n      if (!this.selectedUserId) return;\r\n      sessionStorage.setItem('User_Id', this.selectedUserId);\r\n      this.getCurrentUser();\r\n      this.$message.success('切换用户成功');\r\n    },\r\n    onFutiSwitchChange(val) {\r\n      sessionStorage.setItem('futiSwitch', val);\r\n      if (val === 'tc') {\r\n        // 仅体彩，所有下注单都设为2\r\n        if (this.formData && this.formData.betGroups) {\r\n          this.formData.betGroups.forEach(group => {\r\n            group.lotteryId = 2;\r\n            // 确保money字段始终为字符串类型\r\n            if (group.money != null && typeof group.money !== 'string') {\r\n              group.money = String(group.money);\r\n            }\r\n          });\r\n        }\r\n      } else {\r\n        // 切换为全部彩种时，确保数据类型正确\r\n        if (this.formData && this.formData.betGroups) {\r\n          this.formData.betGroups.forEach(group => {\r\n            // 确保money字段始终为字符串类型\r\n            if (group.money != null && typeof group.money !== 'string') {\r\n              group.money = String(group.money);\r\n            }\r\n          });\r\n        }\r\n      }\r\n      // 切换为全部彩种时不做强制限制\r\n      this.$forceUpdate();\r\n    },\r\n    // remote 搜索用的 filterMethod\r\n    filterMethod(query) {\r\n      if (!query) {\r\n        this.filteredGameMethods = this.gameMethodsData;\r\n      } else {\r\n        this.filteredGameMethods = this.gameMethodsData.filter(item => {\r\n          return (item.methodName && item.methodName.indexOf(query) !== -1) ||\r\n                 (query === '3' && item.methodName && item.methodName.indexOf('三') !== -1);\r\n        });\r\n      }\r\n    },\r\n    showFormatDialog() {\r\n      this.formatDialogVisible = true;\r\n      this.formatInput = '';\r\n      this.formatOutput = '';\r\n      this.formatTab = 'unified'; // 每次打开都默认显示字符转换\r\n    },\r\n    /** 显示统计对话框 */\r\n    showStatistics() {\r\n      this.statisticsVisible = true;\r\n    },\r\n    /** 验证金额 */\r\n    validateMoney() {\r\n      for (let i = 0; i < this.formData.betGroups.length; i++) {\r\n        const group = this.formData.betGroups[i];\r\n        const money = group.money;\r\n        const groupNumber = i + 1; // 下注单号从1开始\r\n\r\n        // 确保money是字符串类型，防止trim()方法报错\r\n        const moneyStr = money != null ? String(money) : '';\r\n\r\n        // 检查是否为空\r\n        if (!moneyStr || moneyStr.trim() === '') {\r\n          return {\r\n            valid: false,\r\n            message: `第<span style=\"color: #ff4757; font-weight: bold; font-size: 16px;\">${groupNumber}</span>组下注金额不能为<span style=\"color: #ff4757; font-weight: bold; font-size: 16px;\">空</span>`\r\n          };\r\n        }\r\n\r\n        // 检查是否为数字\r\n        const numMoney = Number(moneyStr.trim());\r\n        if (isNaN(numMoney) || numMoney <= 0) {\r\n          return {\r\n            valid: false,\r\n            message: `第<span style=\"color: #ff4757; font-weight: bold; font-size: 16px;\">${groupNumber}</span>组下注金额不能为<span style=\"color: #ff4757; font-weight: bold; font-size: 16px;\">\"${moneyStr}\"</span>，请修改`\r\n          };\r\n        }\r\n      }\r\n\r\n      return {\r\n        valid: true,\r\n        message: ''\r\n      };\r\n    },\r\n    /** 检查统计权限 */\r\n    async checkStatisticsPermission() {\r\n      try {\r\n        // 调用专门的统计权限API\r\n        const response = await getStatisticsPermission();\r\n\r\n\r\n        if (response && response.code === 200 && response.data !== undefined) {\r\n\r\n          this.hasStatisticsPermission = response.data === '1';\r\n        } else {\r\n \r\n          this.hasStatisticsPermission = false;\r\n        }\r\n\r\n  \r\n      } catch (error) {\r\n        console.error('获取用户权限失败:', error);\r\n        this.hasStatisticsPermission = false;\r\n      }\r\n    },\r\n    handleFormatInput() {\r\n      // 句号tab：所有句号类字符转为-\r\n      let out = (this.formatInput || '').replace(/[。．.]/g, '-');\r\n      // 连续多个短横合并为一个\r\n      out = out.replace(/-+/g, '-');\r\n      // 先替换所有汉字数字为阿拉伯数字\r\n      let lines = out.split('\\n').map(line => this.replaceChineseNumber(line));\r\n      lines = this.autoConvertChineseMoney(lines);\r\n      this.formatOutput = lines.filter(line => line).join('\\n');\r\n    },\r\n    insertFormatResult() {\r\n      this.formData.shibie = this.formatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.formatOutput);\r\n    },\r\n    handleColonFormatInput() {\r\n      // 冒号tab：所有冒号类字符转为-\r\n      let out = (this.colonFormatInput || '').replace(/[：:]/g, '-');\r\n      // 连续多个短横合并为一个\r\n      out = out.replace(/-+/g, '-');\r\n      // 先替换所有汉字数字为阿拉伯数字\r\n      let lines = out.split('\\n').map(line => this.replaceChineseNumber(line));\r\n      lines = this.autoConvertChineseMoney(lines);\r\n      this.colonFormatOutput = lines.filter(line => line).join('\\n');\r\n    },\r\n    insertColonFormatResult() {\r\n      this.formData.shibie = this.colonFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.colonFormatOutput);\r\n    },\r\n\r\n    handleMoneyFormatInput() {\r\n      // 只做汉字金额转数字，且只替换最后一个汉字金额为/数字\r\n      let lines = (this.moneyFormatInput || '').split('\\n').map(line => this.replaceChineseNumber(line));\r\n      const chineseMoneyReg = /([一二三四五六七八九十百千万两零]+)(元|块|米)?/g;\r\n      lines = lines.map(line => {\r\n        let lastMatch;\r\n        let match;\r\n        while ((match = chineseMoneyReg.exec(line)) !== null) {\r\n          lastMatch = match;\r\n        }\r\n        if (lastMatch) {\r\n          const num = this.chineseToNumber(lastMatch[1]);\r\n          const unit = lastMatch[2] || '';\r\n          const start = lastMatch.index;\r\n          const end = start + lastMatch[0].length;\r\n          let prefix = line.slice(0, start);\r\n          if (!/[\\/\\s,，.。-]+$/.test(prefix)) {\r\n            prefix += '/';\r\n          }\r\n          return prefix + num + unit + line.slice(end);\r\n        }\r\n        return line;\r\n      });\r\n      this.moneyFormatOutput = lines.filter(line => line).join('\\n');\r\n    },\r\n    insertMoneyFormatResult() {\r\n      this.formData.shibie = this.moneyFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.moneyFormatOutput);\r\n    },\r\n    handleUnifiedFormatInput() {\r\n      // 统一字符转换：智能识别并转换各种分隔符和汉字金额\r\n      let out = this.unifiedFormatInput || '';\r\n\r\n      // 1. 先处理汉字金额转换（在其他转换之前）\r\n      let lines = out.split('\\n').map(line => line.trim()).filter(Boolean);\r\n      lines = this.autoConvertChineseMoney(lines);\r\n      out = lines.join('\\n');\r\n\r\n      // 2. 替换剩余的汉字数字为阿拉伯数字（不包括已转换的金额）\r\n      out = this.replaceChineseNumber(out);\r\n\r\n      // 3. 转换各种分隔符为空格（支持一个或多个连续字符）\r\n      // 逗号、句号、冒号、下划线都转为空格\r\n      out = out.replace(/[,，]+/g, ' ');        // 逗号（中英文）\r\n      out = out.replace(/[。．.]+/g, ' ');       // 句号（中英文）\r\n      out = out.replace(/[：:]+/g, ' ');        // 冒号（中英文）\r\n      out = out.replace(/_+/g, ' ');            // 下划线\r\n\r\n      // 4. 处理加号类字符（转换为空格）\r\n      out = out.replace(/[➕＋﹢✚✛✜✙❇️❌❎]+/g, ' ');\r\n\r\n      // 5. 合并多个连续空格为一个\r\n      out = out.replace(/\\s+/g, ' ');\r\n\r\n      this.unifiedFormatOutput = out.split('\\n').map(line => line.trim()).filter(Boolean).join('\\n');\r\n    },\r\n    insertUnifiedFormatResult() {\r\n      this.formData.shibie = this.unifiedFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.unifiedFormatOutput);\r\n    },\r\n    handleChainFormatInput() {\r\n      // 链子分类：按号码长度分组到不同行\r\n      let input = this.chainFormatInput || '';\r\n\r\n      // 1. 先进行基础的字符转换\r\n      input = this.replaceChineseNumber(input);\r\n\r\n      // 2. 提取所有数字（支持各种分隔符）\r\n      // 使用正则匹配所有连续的数字\r\n      const numberPattern = /\\d+/g;\r\n      const numbers = input.match(numberPattern) || [];\r\n\r\n      // 3. 按长度分组，同时记录每个长度首次出现的位置\r\n      const lengthGroups = {};\r\n      const lengthFirstIndex = {};\r\n\r\n      numbers.forEach((num, index) => {\r\n        const length = num.length;\r\n        if (!lengthGroups[length]) {\r\n          lengthGroups[length] = [];\r\n          lengthFirstIndex[length] = index; // 记录该长度首次出现的位置\r\n        }\r\n        lengthGroups[length].push(num);\r\n      });\r\n\r\n      // 4. 按首次出现的顺序排序并生成输出\r\n      const sortedLengths = Object.keys(lengthGroups).sort((a, b) => lengthFirstIndex[a] - lengthFirstIndex[b]);\r\n      const resultLines = [];\r\n\r\n      sortedLengths.forEach(length => {\r\n        const nums = lengthGroups[length];\r\n        if (nums.length > 0) {\r\n          // 每行格式：直接显示号码列表，不加长度标识\r\n          const line = nums.join(' ');\r\n          resultLines.push(line);\r\n        }\r\n      });\r\n\r\n      this.chainFormatOutput = resultLines.join('\\n');\r\n    },\r\n    insertChainFormatResult() {\r\n      this.formData.shibie = this.chainFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.chainFormatOutput);\r\n    },\r\n    handleAmountFormatInput() {\r\n      // 金额整合：将相同金额且相同位数的号码合并到一行\r\n      // 支持每行一个号码，也支持一行内用空格分隔的多个号码\r\n      const input = this.amountFormatInput || '';\r\n      const lines = input.split('\\n').filter(line => line.trim());\r\n\r\n      // 解析每行的号码和金额\r\n      const groups = {};\r\n      const groupOrder = []; // 记录分组首次出现的顺序\r\n\r\n      lines.forEach(line => {\r\n        const trimmedLine = line.trim();\r\n        if (!trimmedLine) return;\r\n\r\n        // 先尝试按空格分割，看是否有多个号码-金额对\r\n        const parts = trimmedLine.split(/\\s+/).filter(part => part.trim());\r\n\r\n        // 如果只有一个部分，按原来的逻辑处理\r\n        if (parts.length === 1) {\r\n          this.processAmountFormatItem(parts[0], groups, groupOrder);\r\n        } else {\r\n          // 如果有多个部分，逐个处理每个号码-金额对\r\n          parts.forEach(part => {\r\n            this.processAmountFormatItem(part, groups, groupOrder);\r\n          });\r\n        }\r\n      });\r\n\r\n      // 按首次出现的顺序生成输出\r\n      const resultLines = [];\r\n      groupOrder.forEach(groupKey => {\r\n        const group = groups[groupKey];\r\n        if (group && group.numbers.length > 0) {\r\n          // 合并相同金额和位数的号码到一行\r\n          const line = group.numbers.join('-') + '-' + group.amount;\r\n          resultLines.push(line);\r\n        }\r\n      });\r\n\r\n      this.amountFormatOutput = resultLines.join('\\n');\r\n    },\r\n\r\n    // 新增辅助函数：处理单个号码-金额项\r\n    processAmountFormatItem(item, groups, groupOrder) {\r\n      const trimmedItem = item.trim();\r\n      if (!trimmedItem) return;\r\n\r\n      // 查找最后一个短横线，分离号码和金额\r\n      const lastDashIndex = trimmedItem.lastIndexOf('-');\r\n      if (lastDashIndex === -1) return; // 没有找到分隔符，跳过\r\n\r\n      const number = trimmedItem.substring(0, lastDashIndex).trim();\r\n      const amount = trimmedItem.substring(lastDashIndex + 1).trim();\r\n\r\n      if (!number || !amount) return; // 号码或金额为空，跳过\r\n\r\n      // 按金额和号码位数分组\r\n      const numberLength = number.length;\r\n      const groupKey = `${amount}_${numberLength}`; // 金额_位数作为分组键\r\n\r\n      if (!groups[groupKey]) {\r\n        groups[groupKey] = {\r\n          amount: amount,\r\n          length: numberLength,\r\n          numbers: []\r\n        };\r\n        groupOrder.push(groupKey); // 记录分组首次出现的顺序\r\n      }\r\n      groups[groupKey].numbers.push(number);\r\n    },\r\n    insertAmountFormatResult() {\r\n      this.formData.shibie = this.amountFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.amountFormatOutput);\r\n    },\r\n    handleRemoveFormatInput() {\r\n      // 去除分隔符和英文字母：只保留汉字、数字和换行\r\n      const input = this.removeFormatInput || '';\r\n\r\n      // 使用正则表达式只保留汉字、数字和换行符\r\n      // \\u4e00-\\u9fff 匹配汉字\r\n      // \\d 匹配数字\r\n      // \\n\\r 匹配换行符\r\n      // 去除所有英文字母（大小写）和其他字符\r\n      const output = input.replace(/[^\\u4e00-\\u9fff\\d\\n\\r]/g, '');\r\n\r\n      // 清理多余的空行，但保留必要的换行\r\n      this.removeFormatOutput = output.split('\\n')\r\n        .map(line => line.trim())\r\n        .filter((line, index, arr) => {\r\n          // 保留非空行，以及前一行非空的空行（作为分隔）\r\n          return line || (index > 0 && arr[index - 1]);\r\n        })\r\n        .join('\\n')\r\n        .replace(/\\n{3,}/g, '\\n\\n'); // 最多保留两个连续换行\r\n    },\r\n    insertRemoveFormatResult() {\r\n      this.formData.shibie = this.removeFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.removeFormatOutput);\r\n    },\r\n    handleUnderscoreFormatInput() {\r\n      // 下划线tab：将一个或多个连续的下划线转换为空格\r\n      let out = (this.underscoreFormatInput || '').replace(/_+/g, ' ');\r\n      // 先替换所有汉字数字为阿拉伯数字\r\n      let lines = out.split('\\n').map(line => this.replaceChineseNumber(line));\r\n      lines = this.autoConvertChineseMoney(lines);\r\n      this.underscoreFormatOutput = lines.filter(line => line).join('\\n');\r\n    },\r\n    insertUnderscoreFormatResult() {\r\n      this.formData.shibie = this.underscoreFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.underscoreFormatOutput);\r\n    },\r\n    // 工具函数：替换所有汉字数字为阿拉伯数字\r\n    replaceChineseNumber(str) {\r\n      // 先处理完整的中文数字（包括单位），然后再处理单个汉字数字\r\n      // 匹配完整的中文数字表达式，如\"二十五\"、\"一百二十三\"等\r\n      const chineseNumberPattern = /[零一二两三四五六七八九十百千万]+/g;\r\n\r\n      return str.replace(chineseNumberPattern, (match) => {\r\n        // 如果匹配的是单个字符且不包含单位，使用简单映射\r\n        if (match.length === 1 && !/[十百千万]/.test(match)) {\r\n          const map = { '零':0,'一':1,'二':2,'两':2,'三':3,'四':4,'五':5,'六':6,'七':7,'八':8,'九':9 };\r\n          return map[match] || match;\r\n        }\r\n        // 否则使用完整的中文数字转换\r\n        return this.chineseToNumber(match);\r\n      });\r\n    },\r\n    chineseToNumber(chinese) {\r\n      if (!chinese) return 0;\r\n\r\n      // 先判断是否为纯数字\r\n      if (/^\\d+$/.test(chinese)) {\r\n        return parseInt(chinese, 10);\r\n      }\r\n\r\n      // 移除单位\r\n      const str = chinese.replace(/元|块|米/g, '');\r\n\r\n      // 数字映射\r\n      const numMap = {\r\n        '零': 0, '一': 1, '二': 2, '两': 2, '三': 3, '四': 4,\r\n        '五': 5, '六': 6, '七': 7, '八': 8, '九': 9\r\n      };\r\n\r\n      // 单位映射\r\n      const unitMap = { '十': 10, '百': 100, '千': 1000, '万': 10000 };\r\n\r\n      let result = 0;\r\n      let temp = 0;\r\n      let hasNum = false;\r\n\r\n      for (let i = 0; i < str.length; i++) {\r\n        const char = str[i];\r\n\r\n        if (numMap.hasOwnProperty(char)) {\r\n          // 是数字\r\n          temp = numMap[char];\r\n          hasNum = true;\r\n        } else if (unitMap.hasOwnProperty(char)) {\r\n          // 是单位\r\n          const unit = unitMap[char];\r\n\r\n          if (char === '十' && !hasNum) {\r\n            // \"十\"前面没有数字，如\"十五\"，十前面默认为1\r\n            temp = 1;\r\n          }\r\n\r\n          if (unit === 10000) {\r\n            // 万\r\n            result = (result + temp) * unit;\r\n            temp = 0;\r\n          } else if (unit >= 100) {\r\n            // 百、千\r\n            result += temp * unit;\r\n            temp = 0;\r\n          } else {\r\n            // 十\r\n            result += temp * unit;\r\n            temp = 0;\r\n          }\r\n          hasNum = false;\r\n        }\r\n      }\r\n\r\n      // 处理最后剩余的数字\r\n      result += temp;\r\n\r\n      return result;\r\n    },\r\n    autoConvertChineseMoney(lines) {\r\n      if (typeof lines === 'string') lines = lines.split(/\\n/);\r\n      // 更精确的中文金额正则，支持复杂的中文数字\r\n      const chineseMoneyReg = /([零一二三四五六七八九十百千万两]+)(元|块|米)?/g;\r\n      return lines.map(line => {\r\n        // 查找所有汉字金额\r\n        let lastMatch;\r\n        let match;\r\n        // 重置正则的lastIndex\r\n        chineseMoneyReg.lastIndex = 0;\r\n        while ((match = chineseMoneyReg.exec(line)) !== null) {\r\n          // 验证匹配的是否真的是数字（包含数字字符或单位字符）\r\n          if (/[一二三四五六七八九十百千万两零]/.test(match[1])) {\r\n            lastMatch = match;\r\n          }\r\n        }\r\n        if (lastMatch) {\r\n          const num = this.chineseToNumber(lastMatch[1]);\r\n          const unit = lastMatch[2] || '';\r\n          // 替换最后一个汉字金额为数字\r\n          const start = lastMatch.index;\r\n          const end = start + lastMatch[0].length;\r\n          let prefix = line.slice(0, start);\r\n\r\n          // 确保汉字金额前面有空格，避免和号码混淆\r\n          if (prefix && !/[\\s\\/,，.。-]$/.test(prefix)) {\r\n            prefix += ' ';\r\n          }\r\n\r\n          return prefix + num + unit + line.slice(end);\r\n        }\r\n        return line;\r\n      });\r\n    },\r\n    // 初始化本组序号\r\n    initSerialNumber() {\r\n      // 如果是修改模式（有row数据）且已有流水号，不要重新初始化\r\n      if (this.row && this.row.serialNumber) {\r\n\r\n        this.serialNumber = this.row.serialNumber;\r\n        this.originalSerialNumber = this.row.serialNumber;\r\n        // 仍然获取最大流水号用于生成新流水号时参考\r\n        getMaxSerialNumber().then(max => {\r\n          this.maxSerialNumber = max ? Number(max) : 1;\r\n        }).catch(() => {\r\n          this.maxSerialNumber = 1;\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 新增模式或没有流水号时，获取最大流水号\r\n      \r\n      getMaxSerialNumber().then(max => {\r\n        if (max == null) {\r\n          this.maxSerialNumber = 1;\r\n          this.serialNumber = 1;\r\n        } else {\r\n          this.maxSerialNumber = Number(max);\r\n          this.serialNumber = Number(max);\r\n        }\r\n        \r\n      }).catch(() => {\r\n        this.maxSerialNumber = 1;\r\n        this.serialNumber = 1;\r\n      });\r\n    },\r\n    // 增加本组序号，只能加到最大+1\r\n    increaseSerialNumber() {\r\n      const next = Number(this.maxSerialNumber) + 1;\r\n      if (this.serialNumber < next) {\r\n        this.serialNumber = next;\r\n      }\r\n      // 如果已经是最大+1，则不再增加\r\n    },\r\n    // 生成新的流水号\r\n    generateNewSerialNumber() {\r\n      this.generateLoading = true;\r\n      generateSerialNumber().then(response => {\r\n        if (response.code === 200) {\r\n          this.serialNumber = response.serialNumber;\r\n          this.maxSerialNumber = response.serialNumber;\r\n          this.$message.success(`生成新流水号: ${response.serialNumber}`);\r\n        } else {\r\n          this.$message.error('生成流水号失败: ' + response.msg);\r\n        }\r\n      }).catch(error => {\r\n        console.error('生成流水号失败:', error);\r\n        this.$message.error('生成流水号失败');\r\n      }).finally(() => {\r\n        this.generateLoading = false;\r\n      });\r\n    },\r\n    // 处理用户ID变化\r\n    handleUserIdChange(newUserId, oldUserId) {\r\n    \r\n      if (newUserId === this.originalUserId) {\r\n        // 改回原用户，恢复原流水号\r\n        this.serialNumber = this.originalSerialNumber;\r\n        this.$message.info(`已恢复原流水号: ${this.originalSerialNumber}`);\r\n      } else {\r\n        // 改为其他用户，自动生成新流水号\r\n        this.autoGenerateSerialNumber();\r\n      }\r\n    },\r\n    // 自动生成流水号（不显示成功消息）\r\n    autoGenerateSerialNumber() {\r\n      generateSerialNumber().then(response => {\r\n        if (response.code === 200) {\r\n          this.serialNumber = response.serialNumber;\r\n          this.maxSerialNumber = response.serialNumber;\r\n          this.$message.info(`用户变更，自动生成新流水号: ${response.serialNumber}`);\r\n        } else {\r\n          this.$message.error('自动生成流水号失败: ' + response.msg);\r\n        }\r\n      }).catch(error => {\r\n        console.error('自动生成流水号失败:', error);\r\n        this.$message.error('自动生成流水号失败');\r\n      });\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 使用全局样式 */\r\n.bet-dialog-loading {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n.bet-dialog-loading .el-loading-mask {\r\n  z-index: 9999 !important;\r\n}\r\n</style>\r\n\r\n<style scoped>\r\n/* 现代化弹窗样式 */\r\n.modern-bet-dialog {\r\n  .el-dialog {\r\n    border-radius: 12px;\r\n    overflow: hidden;\r\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\r\n  }\r\n\r\n  .el-dialog__header {\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    padding: 20px 24px;\r\n  }\r\n\r\n  .el-dialog__title {\r\n    color: white;\r\n    font-weight: 600;\r\n    font-size: 18px;\r\n  }\r\n\r\n  .el-dialog__headerbtn .el-dialog__close {\r\n    color: white;\r\n    font-size: 20px;\r\n\r\n    &:hover {\r\n      color: rgba(255, 255, 255, 0.8);\r\n    }\r\n  }\r\n}\r\n\r\n/* 福体开关样式 */\r\n.lottery-switch {\r\n  font-weight: 500;\r\n  margin-left: 8px;\r\n}\r\n\r\n/* 表单样式 */\r\n.modern-bet-form {\r\n  .el-form-item__label {\r\n    font-weight: 500;\r\n    color: #495057;\r\n  }\r\n}\r\n\r\n/* 合并的信息卡片样式 */\r\n.info-card {\r\n  background: white;\r\n  padding: 8px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e9ecef;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-bottom: 6px;\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.info-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.info-label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  font-weight: 500;\r\n  color: #495057;\r\n  font-size: 13px;\r\n  min-width: 60px;\r\n\r\n  i {\r\n    color: #667eea;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n.compact-input {\r\n  .el-input__inner {\r\n    height: 28px;\r\n    font-size: 12px;\r\n    border-radius: 4px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:focus {\r\n      border-color: #667eea;\r\n      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);\r\n    }\r\n  }\r\n}\r\n\r\n.compact-select {\r\n  .el-input__inner {\r\n    height: 28px;\r\n    font-size: 12px;\r\n    border-radius: 4px;\r\n  }\r\n}\r\n\r\n.compact-btn {\r\n  height: 28px;\r\n  padding: 0 8px;\r\n  font-size: 12px;\r\n  border-radius: 4px;\r\n\r\n  i {\r\n    margin-right: 2px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n/* 识别区域样式 */\r\n.recognition-section {\r\n  background: white;\r\n  padding: 6px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e9ecef;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.recognition-wrapper {\r\n  .el-form-item__content {\r\n    position: relative;\r\n  }\r\n}\r\n\r\n.recognition-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  margin-bottom: 6px;\r\n  font-size: 12px;\r\n  color: #495057;\r\n\r\n  i {\r\n    color: #667eea;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .el-tag {\r\n    margin-left: auto;\r\n  }\r\n\r\n  .el-button {\r\n    margin-left: auto;\r\n  }\r\n}\r\n\r\n.recognition-textarea {\r\n  .el-textarea__inner {\r\n    border-radius: 6px;\r\n    border-color: #e9ecef;\r\n    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\r\n    font-size: 12px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:focus {\r\n      border-color: #667eea;\r\n      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);\r\n    }\r\n  }\r\n}\r\n\r\n.option-group {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  padding: 6px 10px;\r\n  background: rgba(248, 249, 250, 0.8);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(233, 236, 239, 0.8);\r\n  backdrop-filter: blur(5px);\r\n  transition: all 0.3s ease;\r\n  min-height: 36px;\r\n\r\n  &:hover {\r\n    background: rgba(248, 249, 250, 1);\r\n    border-color: rgba(102, 126, 234, 0.3);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  }\r\n}\r\n\r\n.option-label {\r\n  min-width: 55px;\r\n  margin-right: 8px;\r\n  font-weight: 600;\r\n  color: #495057;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  flex-shrink: 0;\r\n\r\n  &::before {\r\n    content: '●';\r\n    color: #667eea;\r\n    font-size: 10px;\r\n    animation: pulse 2s infinite;\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    opacity: 0.6;\r\n  }\r\n}\r\n\r\n/* 投注组总额和投注数美化 */\r\n.group-summary {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-left: auto;\r\n}\r\n\r\n.summary-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  padding: 4px 8px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n\r\n  &.amount {\r\n    background: rgba(102, 126, 234, 0.1);\r\n    border: 1px solid rgba(102, 126, 234, 0.2);\r\n\r\n    .summary-icon {\r\n      color: #667eea;\r\n    }\r\n\r\n    .summary-value {\r\n      color: #667eea;\r\n      font-size: 15px;\r\n    }\r\n  }\r\n\r\n  &.bets {\r\n    background: rgba(255, 107, 53, 0.1);\r\n    border: 1px solid rgba(255, 107, 53, 0.2);\r\n\r\n    .summary-icon {\r\n      color: #ff6b35;\r\n    }\r\n\r\n    .summary-value {\r\n      color: #ff6b35;\r\n    }\r\n  }\r\n\r\n  &:hover {\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  }\r\n}\r\n\r\n.summary-icon {\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.summary-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n}\r\n\r\n.summary-label {\r\n  font-size: 10px;\r\n  color: #666;\r\n  line-height: 1;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.summary-value {\r\n  font-size: 12px;\r\n  font-weight: 700;\r\n  line-height: 1;\r\n}\r\n\r\n.el-radio-group {\r\n  display: inline-flex;\r\n  gap: 6px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.el-radio-button {\r\n  margin-right: 0;\r\n\r\n  .el-radio-button__inner {\r\n    border-radius: 6px;\r\n    font-weight: 600;\r\n    transition: all 0.3s ease;\r\n    border-color: #e9ecef;\r\n    padding: 6px 8px;\r\n    font-size: 11px;\r\n    min-width: 65px;\r\n    text-align: center;\r\n\r\n    &:hover {\r\n      border-color: #667eea;\r\n      color: #667eea;\r\n    }\r\n  }\r\n\r\n  /* 福彩3D按钮样式 */\r\n  &[data-lottery=\"fc3d\"] {\r\n    .el-radio-button__inner {\r\n      border-color: #ff6b35;\r\n      color: #ff6b35;\r\n\r\n      &:hover {\r\n        border-color: #ff6b35;\r\n        background: rgba(255, 107, 53, 0.1);\r\n      }\r\n    }\r\n\r\n    &.is-active {\r\n      .el-radio-button__inner {\r\n        background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);\r\n        border-color: #ff6b35;\r\n        color: white;\r\n        box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);\r\n      }\r\n    }\r\n  }\r\n\r\n  /* 体彩排三按钮样式 */\r\n  &[data-lottery=\"tc\"] {\r\n    .el-radio-button__inner {\r\n      border-color: #409eff;\r\n      color: #409eff;\r\n      min-width: 70px;\r\n\r\n      &:hover {\r\n        border-color: #409eff;\r\n        background: rgba(64, 158, 255, 0.1);\r\n      }\r\n    }\r\n\r\n    &.is-active {\r\n      .el-radio-button__inner {\r\n        background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);\r\n        border-color: #409eff;\r\n        color: white;\r\n        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.bet-groups-list-wrapper {\r\n  position: relative;\r\n  margin-bottom: 8px;\r\n  margin-top: 0;\r\n}\r\n\r\n.bet-groups-list {\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  padding: 0 4px;\r\n}\r\n\r\n.bet-group-card {\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  box-shadow: 0 3px 12px 0 rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 8px;\r\n  padding: 10px 14px;\r\n  border: 2px solid #e9ecef;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 6px;\r\n    height: 100%;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  }\r\n\r\n  &::after {\r\n   \r\n    position: absolute;\r\n    top: 8px;\r\n    right: 12px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    padding: 4px 10px;\r\n    border-radius: 15px;\r\n    font-size: 11px;\r\n    font-weight: 700;\r\n    z-index: 10;\r\n    box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);\r\n  }\r\n}\r\n\r\n.bet-group-card:hover {\r\n  box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.15);\r\n  transform: translateY(-2px);\r\n  border-color: #667eea;\r\n}\r\n\r\n.bet-group-even {\r\n  background: linear-gradient(135deg, #fff8f5 0%, #fef5f0 100%) !important;\r\n  border-color: #ffb8a1 !important;\r\n\r\n  &::before {\r\n    background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);\r\n  }\r\n\r\n  &::after {\r\n    \r\n    background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);\r\n    box-shadow: 0 2px 6px rgba(255, 107, 53, 0.3);\r\n  }\r\n\r\n  &:hover {\r\n    box-shadow: 0 6px 20px 0 rgba(255, 107, 53, 0.2);\r\n    border-color: #ff6b35;\r\n  }\r\n}\r\n\r\n.bet-group {\r\n  background: #f5f7fa;\r\n  border-radius: 4px;\r\n  padding: 15px;\r\n  margin-bottom: 15px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.bet-group:hover {\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);\r\n}\r\n\r\n.bet-group-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #dcdfe6;\r\n}\r\n\r\n.bet-group-content {\r\n  margin-left: 10px;\r\n}\r\n\r\n.bet-group-footer {\r\n  margin-top: 10px;\r\n  padding-top: 10px;\r\n  border-top: 1px solid #dcdfe6;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.el-form-item--small.el-form-item {\r\n  margin-bottom: 3px;\r\n}\r\n\r\n.modern-bet-form .el-form-item {\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.el-textarea__inner {\r\n  font-family: monospace;\r\n}\r\n\r\n.el-input-number--small {\r\n  width: 130px;\r\n}\r\n\r\n.total-info {\r\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);\r\n  padding: 6px 12px;\r\n  border-radius: 6px;\r\n  box-shadow: 0 1px 6px 0 rgba(102, 126, 234, 0.1);\r\n  margin-top: 4px;\r\n  border: 1px solid rgba(102, 126, 234, 0.2);\r\n  position: relative;\r\n  font-size: 13px;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 2px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    border-radius: 6px 6px 0 0;\r\n  }\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center;\r\n  \r\n  /* background: #f8f9fa; */\r\n  border-radius: 0 0 6px 6px;\r\n  margin: 4px -6px -6px -6px;\r\n}\r\n\r\n.dialog-footer .el-button {\r\n  min-width: 80px;\r\n  margin: 0 6px;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n  font-size: 13px;\r\n  height: 32px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-1px);\r\n  }\r\n\r\n  &.el-button--primary {\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    border: none;\r\n\r\n    &:hover {\r\n      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\r\n    }\r\n  }\r\n}\r\n\r\n:deep(.bet-dialog-loading) {\r\n  z-index: 3000 !important;\r\n}\r\n\r\n.bet-group-add-btn {\r\n  position: absolute;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  bottom: -12px;\r\n  width: 28px;\r\n  height: 28px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  border: 2px solid white;\r\n  box-shadow: 0 2px 8px 0 rgba(102, 126, 234, 0.3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  z-index: 20;\r\n  transition: all 0.3s ease;\r\n\r\n  &::before {\r\n    content: '+';\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.bet-group-add-btn:hover {\r\n  background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);\r\n  transform: translateX(-50%) translateY(-1px) scale(1.05);\r\n  box-shadow: 0 4px 12px 0 rgba(102, 126, 234, 0.4);\r\n}\r\n.el-dialog__body{\r\n  margin-top: -30px;\r\n}\r\n\r\n/* 简洁格式转换弹窗样式 */\r\n.simple-format-dialog {\r\n  .el-textarea__inner {\r\n    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\r\n    font-size: 13px;\r\n  }\r\n\r\n  .el-form-item {\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  .el-button {\r\n    margin-right: 8px;\r\n  }\r\n}\r\n\r\n/* 号码数量超限样式 */\r\n.number-count-exceeded .el-textarea__inner {\r\n  border-color: #f56c6c !important;\r\n  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;\r\n}\r\n\r\n.number-count-exceeded .el-textarea__inner:focus {\r\n  border-color: #f56c6c !important;\r\n  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.3) !important;\r\n}\r\n\r\n/* 格式转换弹窗美化样式 */\r\n.format-dialog {\r\n  .el-dialog__header {\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    padding: 20px 24px;\r\n    border-radius: 8px 8px 0 0;\r\n\r\n    .el-dialog__title {\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      color: white;\r\n    }\r\n\r\n    .el-dialog__close {\r\n      color: white;\r\n      font-size: 20px;\r\n\r\n      &:hover {\r\n        color: #f0f0f0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.format-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.format-tabs {\r\n  .el-tabs__header {\r\n    margin: 0 0 20px 0;\r\n    background: white;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    padding: 12px;\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n\r\n  .el-tabs__nav {\r\n    border: none;\r\n    display: flex;\r\n    justify-content: center;\r\n    width: 100%;\r\n  }\r\n\r\n  .el-tabs__item {\r\n    border: none;\r\n    border-radius: 6px;\r\n    margin-right: 8px;\r\n    padding: 12px 24px;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n    min-width: 120px;\r\n    text-align: center;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 44px;\r\n\r\n    &:hover {\r\n      background: #e3f2fd;\r\n      color: #1976d2;\r\n    }\r\n\r\n    &.is-active {\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      color: white;\r\n      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n    }\r\n\r\n    i {\r\n      margin-right: 6px;\r\n      font-size: 16px;\r\n      display: inline-block;\r\n      vertical-align: middle;\r\n    }\r\n\r\n    span {\r\n      display: inline-block;\r\n      vertical-align: middle;\r\n    }\r\n  }\r\n\r\n  .el-tabs__content {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.tab-content {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e8eaed;\r\n}\r\n\r\n.input-section, .output-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  font-size: 14px;\r\n\r\n  i {\r\n    margin-right: 8px;\r\n    font-size: 16px;\r\n    color: #667eea;\r\n  }\r\n}\r\n\r\n.arrow-section {\r\n  text-align: center;\r\n  margin: 16px 0;\r\n\r\n  i {\r\n    font-size: 24px;\r\n    color: #667eea;\r\n    animation: bounce 2s infinite;\r\n  }\r\n}\r\n\r\n@keyframes bounce {\r\n  0%, 20%, 50%, 80%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  40% {\r\n    transform: translateY(-8px);\r\n  }\r\n  60% {\r\n    transform: translateY(-4px);\r\n  }\r\n}\r\n\r\n.format-input, .format-output {\r\n  .el-textarea__inner {\r\n    border-radius: 8px;\r\n    border: 2px solid #e8eaed;\r\n    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\r\n    font-size: 13px;\r\n    line-height: 1.6;\r\n    transition: all 0.3s ease;\r\n\r\n    &:focus {\r\n      border-color: #667eea;\r\n      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n    }\r\n\r\n    &::placeholder {\r\n      color: #999;\r\n      font-style: italic;\r\n    }\r\n  }\r\n}\r\n\r\n.format-output {\r\n  .el-textarea__inner {\r\n    background: #f8f9fa;\r\n    color: #2d3748;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.button-section {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 12px;\r\n  margin-top: 24px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #e8eaed;\r\n}\r\n\r\n.action-button {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 8px;\r\n  padding: 12px 24px;\r\n  font-weight: 600;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);\r\n  }\r\n\r\n  &:disabled {\r\n    background: #d1d5db;\r\n    box-shadow: none;\r\n    transform: none;\r\n  }\r\n}\r\n\r\n.cancel-button {\r\n  border: 2px solid #e8eaed;\r\n  border-radius: 8px;\r\n  padding: 12px 24px;\r\n  font-weight: 500;\r\n  background: white;\r\n  color: #6b7280;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    border-color: #667eea;\r\n    color: #667eea;\r\n    background: #f8f9ff;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyfA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AACA,IAAAM,SAAA,GAAAN,OAAA;AACA,IAAAO,KAAA,GAAAP,OAAA;AACA,IAAAQ,cAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,gBAAA,GAAAP,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAU,IAAA;EACAC,UAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,eAAA,EAAAA;EACA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACAG,GAAA;MACAL,IAAA,EAAAM,MAAA;MACAJ,OAAA;IACA;EACA;EACAK,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,WAAA;MACAC,WAAA;MACAC,SAAA;MACAC,eAAA;MACAC,eAAA;MAAA;MACAC,mBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,QAAA;QACAC,eAAA;QAAA;QACAC,aAAA;QAAA;QACAC,MAAA;QACAC,SAAA;UACAC,QAAA,EAAAC,SAAA;UACAC,KAAA;UACAC,UAAA;UACAC,SAAA;UACAC,MAAA;UACAC,OAAA;UACAC,KAAA;UACAC,SAAA;QACA;MACA;MACA;MACAC,WAAA;MACAC,qBAAA;MACAC,KAAA;QACAZ,SAAA;UACAC,QAAA;YAAAY,QAAA;YAAAC,OAAA;YAAAC,OAAA;UAAA;UACAZ,KAAA;YAAAU,QAAA;YAAAC,OAAA;YAAAC,OAAA;UAAA;QACA;MACA;MACAC,SAAA;QACAnB,eAAA;QAAA;QACAC,aAAA;QAAA;QACAC,MAAA;QACAC,SAAA;MACA;MACAiB,WAAA;QAAA3C,IAAA;MAAA;MACA4C,QAAA;MACAC,cAAA;MACAC,UAAA,EAAAC,cAAA,CAAAC,OAAA;MACAC,mBAAA;MACAC,WAAA;MACAC,YAAA;MACAC,SAAA;MACAC,gBAAA;MACAC,iBAAA;MACAC,gBAAA;MACAC,iBAAA;MAEAC,qBAAA;MACAC,sBAAA;MACAC,kBAAA;MACAC,mBAAA;MACAC,gBAAA;MACAC,iBAAA;MACAC,iBAAA;MACAC,kBAAA;MACAC,iBAAA;MACAC,kBAAA;MACAC,YAAA;MAAA;MACAC,eAAA;MAAA;MACAC,eAAA;MAAA;MACAC,cAAA;MAAA;MACAC,oBAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,uBAAA;IACA;EACA;EACAC,QAAA;IACA,eACAC,oBAAA,WAAAA,qBAAA;MACA,YAAAF,uBAAA;IACA;EACA;EACAG,KAAA;IACAvE,OAAA,WAAAA,QAAAwE,GAAA;MACA,KAAA/D,aAAA,GAAA+D,GAAA;MACA,IAAAA,GAAA;QACA;QACA,KAAAC,iBAAA;QACA,KAAAC,WAAA;QACA;QACA,KAAAC,yBAAA;QACA;QACA,SAAArE,GAAA,SAAAA,GAAA,CAAAsE,MAAA;UACA;UACA,KAAApC,cAAA,QAAAlC,GAAA,CAAAsE,MAAA;QACA;UACA;UACA,IAAAA,MAAA,GAAAlC,cAAA,CAAAC,OAAA;UACA,IAAAiC,MAAA;YACA,KAAApC,cAAA,GAAAqC,MAAA,CAAAD,MAAA;UACA;YACA;YACA,KAAApC,cAAA;UACA;QACA;QACA;;QAEA,KAAAsC,gBAAA;QACA;QACA,SAAAxE,GAAA;UAEA,KAAAyE,YAAA;QACA;QACA;QACA,KAAAC,kBAAA,MAAAvC,UAAA;MACA;IACA;IACAhC,aAAA,WAAAA,cAAA+D,GAAA;MACA,KAAAS,KAAA,mBAAAT,GAAA;MACA,KAAAA,GAAA;QACA,KAAAU,SAAA;MACA;IACA;IACA;IACA1C,cAAA;MACA2C,OAAA,WAAAA,QAAAC,SAAA,EAAAC,SAAA;QACA;QACA,SAAA/E,GAAA,SAAAA,GAAA,CAAAgF,KAAA,SAAArB,cAAA,aAAAoB,SAAA,KAAA9D,SAAA;UACA,KAAAgE,kBAAA,CAAAH,SAAA,EAAAC,SAAA;QACA;MACA;MACAG,SAAA;IACA;IACA5C,mBAAA,WAAAA,oBAAA4B,GAAA;MACA,KAAAA,GAAA;QACA;QACA,KAAA3B,WAAA;QACA,KAAAC,YAAA;QACA,KAAAE,gBAAA;QACA,KAAAC,iBAAA;QACA,KAAAC,gBAAA;QACA,KAAAC,iBAAA;QAEA,KAAAC,qBAAA;QACA,KAAAC,sBAAA;QACA,KAAAC,kBAAA;QACA,KAAAC,mBAAA;QACA,KAAAC,gBAAA;QACA,KAAAC,iBAAA;QACA,KAAAC,iBAAA;QACA,KAAAC,kBAAA;QACA,KAAAC,iBAAA;QACA,KAAAC,kBAAA;MACA;IACA;EACA;EACA4B,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA;IACAC,mBAAA,WAAAA,oBAAA;MACA,KAAAC,KAAA,CAAAC,eAAA,CAAAC,IAAA;IACA;IACA;IACAtB,iBAAA,WAAAA,kBAAA;MAAA,IAAAuB,KAAA;MAAA,WAAAC,kBAAA,CAAA9F,OAAA,mBAAA+F,aAAA,CAAA/F,OAAA,IAAAgG,CAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAAC,EAAA;QAAA,WAAAJ,aAAA,CAAA/F,OAAA,IAAAoG,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAEA,IAAAE,kCAAA;YAAA;cAAAN,QAAA,GAAAG,QAAA,CAAAI,CAAA;cAAA,MAEAP,QAAA,CAAAQ,IAAA,YAAAR,QAAA,CAAAS,gBAAA;gBAAAN,QAAA,CAAAC,CAAA;gBAAA;cAAA;cACA;cACAT,KAAA,CAAAe,QAAA,CAAAV,QAAA,CAAAW,GAAA;gBACAC,iBAAA;gBACAC,gBAAA;gBACAjH,IAAA;cACA,GAAAkH,IAAA;gBACA;gBACAnB,KAAA,CAAAvF,aAAA;gBACAuF,KAAA,CAAAf,KAAA;gBACA;gBACAe,KAAA,CAAAoB,OAAA,CAAAC,IAAA;cACA,GAAAC,KAAA;gBACA;gBACAtB,KAAA,CAAAvF,aAAA;gBACAuF,KAAA,CAAAf,KAAA;cACA;cAAA,OAAAuB,QAAA,CAAAe,CAAA;YAAA;cAIA,IAAAlB,QAAA,CAAAQ,IAAA,YAAAR,QAAA,CAAAmB,UAAA;gBACA;;gBAEA,IAAAnB,QAAA,CAAAoB,aAAA,GAEA;;gBAEA;gBACAzB,KAAA,CAAA0B,gBAAA;cACA;cAAAlB,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAEAe,OAAA,CAAAC,KAAA,cAAAtB,EAAA;cACAN,KAAA,CAAA6B,QAAA,CAAAD,KAAA;cACA5B,KAAA,CAAAvF,aAAA;cACAuF,KAAA,CAAAf,KAAA;YAAA;cAAA,OAAAuB,QAAA,CAAAe,CAAA;UAAA;QAAA,GAAAnB,OAAA;MAAA;IAEA;IAEA;IACAsB,gBAAA,WAAAA,iBAAA;MACA;MACA,KAAAI,qBAAA;MACA,KAAApC,cAAA;MACA,KAAAqC,cAAA;IACA;IAEA;IACArC,cAAA,WAAAA,eAAA;MAAA,IAAAsC,MAAA;MACA,KAAAhH,kBAAA;MACA,WAAAiH,gBAAA;QACAC,GAAA;QACAC,MAAA;QACAC,MAAA;UACAC,OAAA;UACAC,QAAA;QACA;MACA,GAAAnB,IAAA,WAAAd,QAAA;QACA,IAAA7F,IAAA;QACA,IAAA6F,QAAA,CAAAQ,IAAA;UACA,IAAA0B,KAAA,CAAAC,OAAA,CAAAnC,QAAA,CAAA7F,IAAA;YACAA,IAAA,GAAA6F,QAAA,CAAA7F,IAAA;UACA,WAAA6F,QAAA,CAAAoC,IAAA,IAAAF,KAAA,CAAAC,OAAA,CAAAnC,QAAA,CAAAoC,IAAA;YACAjI,IAAA,GAAA6F,QAAA,CAAAoC,IAAA;UACA,kBAAApC,QAAA,CAAA7F,IAAA;YACA;cACA,IAAAkI,UAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAvC,QAAA,CAAA7F,IAAA;cACAA,IAAA,GAAA+H,KAAA,CAAAC,OAAA,CAAAE,UAAA,IAAAA,UAAA;YACA,SAAAG,CAAA;cACArI,IAAA;YACA;UACA;QACA;QACAwH,MAAA,CAAAlH,eAAA,GAAAN,IAAA;QACAwH,MAAA,CAAAjH,mBAAA,GAAAP,IAAA;QACAwH,MAAA,CAAAhH,kBAAA;MACA,GAAAsG,KAAA;QACAU,MAAA,CAAAlH,eAAA;QACAkH,MAAA,CAAAjH,mBAAA;QACAiH,MAAA,CAAAhH,kBAAA;MACA;IACA;IACA;IACA+E,IAAA,WAAAA,KAAA;MAAA,IAAA3F,KAAA,GAAA0I,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAvH,SAAA,GAAAuH,SAAA;MACA,KAAApI,WAAA,GAAAN,KAAA;MACA,KAAAK,aAAA;IACA;IACA;IACAuI,KAAA,WAAAA,MAAA;MACA,KAAAvI,aAAA;MACA,KAAAwE,KAAA;MACA,KAAAA,KAAA;IACA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,KAAAjE,QAAA;QACAC,eAAA;QACAC,aAAA;QACAC,MAAA;QACAC,SAAA;UACAC,QAAA,EAAAC,SAAA;UACAC,KAAA;UACAC,UAAA;UACAC,SAAA;UACAC,MAAA;UACAC,OAAA;UACAC,KAAA;UACAC,SAAA;QACA;MACA;MACA,KAAAnB,WAAA;MACA,KAAAC,SAAA;MACA;MACA,KAAAmB,WAAA;MACA,KAAAC,qBAAA;IACA;IACAiH,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,QAAA;QACA7H,QAAA,EAAAC,SAAA;QACAC,KAAA;QACAC,UAAA;QACAC,SAAA;QACAC,MAAA;QACAC,OAAA;QACAC,KAAA;QACAC,SAAA,OAAAW,UAAA;MACA;;MAEA;MACA,UAAAxB,QAAA,CAAAI,SAAA;QACA,KAAAJ,QAAA,CAAAI,SAAA;MACA;MACA,KAAAJ,QAAA,CAAAI,SAAA,CAAAgG,IAAA,KAAA+B,cAAA,CAAAjJ,OAAA,MAAAgJ,QAAA;MAEA,KAAAE,SAAA;QACAH,MAAA,CAAAI,YAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,SAAAxI,QAAA,CAAAI,SAAA,CAAA0H,MAAA;QACA;QACA,KAAA9H,QAAA,CAAAI,SAAA;UACAC,QAAA,EAAAC,SAAA;UACAC,KAAA;UACAC,UAAA;UACAC,SAAA;UACAC,MAAA;UACAC,OAAA;UACAC,KAAA;UACAC,SAAA;QACA;MACA;QACA;QACA,KAAAb,QAAA,CAAAI,SAAA,CAAAqI,MAAA,CAAAF,GAAA;MACA;MAEA,KAAAH,SAAA;QACAI,MAAA,CAAAH,YAAA;QACAG,MAAA,CAAAE,YAAA;MACA;IACA;IACAL,YAAA,WAAAA,aAAA;MAAA,IAAAM,MAAA;MACA,KAAAjJ,WAAA,QAAAM,QAAA,CAAAI,SAAA,CAAAwI,MAAA,WAAAC,GAAA,EAAAC,CAAA;QAAA,OAAAD,GAAA,GAAAjF,MAAA,CAAA+E,MAAA,CAAAI,cAAA,CAAAD,CAAA;MAAA;MACA,KAAAnJ,SAAA,QAAAK,QAAA,CAAAI,SAAA,CAAAwI,MAAA,WAAAC,GAAA,EAAAC,CAAA;QAAA,OAAAD,GAAA,GAAAjF,MAAA,CAAA+E,MAAA,CAAAK,YAAA,CAAAF,CAAA;MAAA;IACA;IACAG,eAAA,WAAAA,gBAAA5I,QAAA;MACA,IAAA6I,GAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,GAAA,CAAA7I,QAAA;IACA;IACA8I,cAAA,WAAAA,eAAAC,KAAA;MAEA,IAAAC,KAAA,QAAArJ,QAAA,CAAAI,SAAA,CAAAkJ,SAAA,WAAAR,CAAA;QAAA,OAAAA,CAAA,KAAAM,KAAA;MAAA;MACA,IAAAC,KAAA;QACA;QACA,IAAAE,YAAA,OAAApB,cAAA,CAAAjJ,OAAA,MAAAiJ,cAAA,CAAAjJ,OAAA,MACAkK,KAAA;UACA3I,SAAA;UACAC,MAAA;UACAC,OAAA;UACAC,KAAA;UACA;QAAA,EACA;;QAEA;QACA,IAAA2I,YAAA,CAAAlJ,QAAA;UACAkJ,YAAA,CAAA9I,SAAA;QACA,WAAA8I,YAAA,CAAAlJ,QAAA;UACAkJ,YAAA,CAAA7I,MAAA;QACA,WAAA6I,YAAA,CAAAlJ,QAAA;UACAkJ,YAAA,CAAA5I,OAAA;QACA,oEAAA6I,QAAA,CAAAD,YAAA,CAAAlJ,QAAA;UACA,IAAAoJ,OAAA,QAAAR,eAAA,CAAAM,YAAA,CAAAlJ,QAAA;UACA,IAAAoJ,OAAA,IAAAA,OAAA,CAAA3B,MAAA;YACAyB,YAAA,CAAA3I,KAAA,GAAA6I,OAAA;UACA;QACA;;QAEA;QACA,KAAAC,IAAA,MAAA1J,QAAA,CAAAI,SAAA,EAAAiJ,KAAA,EAAAE,YAAA;QACA,KAAAb,YAAA;MACA;IACA;IACA;IACA7B,qBAAA,WAAAA,sBAAA;MAAA,IAAA8C,MAAA;MACA,IAAAC,eAAA,QAAAC,QAAA;QACAC,IAAA;QACAC,IAAA;QACAC,OAAA;QACAC,UAAA;QACAC,WAAA;MACA;;MAEA;MACAC,OAAA,CAAAC,GAAA;MACA;MACA,IAAAC,sBAAA,KAAAhE,KAAA,WAAAM,KAAA;QACAD,OAAA,CAAAC,KAAA,gBAAAA,KAAA;QACA;UAAAf,IAAA;UAAAG,GAAA;QAAA;MACA;MACA;MACA,IAAAsE,sBAAA,KAAAhE,KAAA,WAAAM,KAAA;QACAD,OAAA,CAAAC,KAAA,gBAAAA,KAAA;QACA;UAAAf,IAAA;UAAAG,GAAA;QAAA;MACA,GACA,EAAAG,IAAA,WAAAoE,IAAA;QAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAtL,OAAA,EAAAoL,IAAA;UAAAG,YAAA,GAAAF,KAAA;UAAAG,UAAA,GAAAH,KAAA;QACA;QACA,IAAAE,YAAA,CAAA7E,IAAA;UACA+D,MAAA,CAAA3J,QAAA,CAAAC,eAAA,GAAAwK,YAAA,CAAA1E,GAAA;QAEA;UACAW,OAAA,CAAAC,KAAA,gBAAA8D,YAAA;UACAd,MAAA,CAAA/C,QAAA,CAAAD,KAAA;QACA;;QAEA;QACA,IAAA+D,UAAA,CAAA9E,IAAA;UACA+D,MAAA,CAAA3J,QAAA,CAAAE,aAAA,GAAAwK,UAAA,CAAA3E,GAAA;QAEA;UACAW,OAAA,CAAAC,KAAA,gBAAA+D,UAAA;UACAf,MAAA,CAAA/C,QAAA,CAAAD,KAAA;QACA;MACA,GAAAgE,OAAA;QACAf,eAAA,CAAA7B,KAAA;MACA;IACA;IACA;IACA6C,oBAAA,WAAAA,qBAAAC,KAAA;MACA,KAAAA,KAAA,KAAAA,KAAA,CAAAC,IAAA;;MAEA;MACA,IAAAC,UAAA,GAAAF,KAAA,CAAAC,IAAA;;MAEA;MACA;MACA,IAAAE,aAAA;MACA,IAAAC,UAAA,GAAAF,UAAA,CAAAG,KAAA,CAAAF,aAAA;MAEA,KAAAC,UAAA;;MAEA;MACA,IAAAE,KAAA,GAAAJ,UAAA,CAAAK,KAAA,YAAAC,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAR,IAAA;MAAA;MACA,IAAAS,UAAA;MAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAvM,OAAA,EAEAiM,KAAA;QAAAO,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAhG,CAAA,IAAAoG,IAAA;UAAA,IAAAN,IAAA,GAAAI,KAAA,CAAAG,KAAA;UACA,IAAAC,WAAA,GAAAR,IAAA,CAAAR,IAAA;UACA,KAAAgB,WAAA;UAEA,IAAAC,WAAA,GAAAD,WAAA,CAAAZ,KAAA,CAAAF,aAAA;UACA,KAAAe,WAAA;;UAEA;UACA,IAAAC,aAAA;UACA,IAAAC,eAAA,GAAAD,aAAA,CAAAE,IAAA,CAAAJ,WAAA;;UAEA;UACA,IAAAK,iBAAA,YAAAD,IAAA,CAAAJ,WAAA;UAEA,IAAAG,eAAA,IAAAE,iBAAA;YACA;YACA;YACAZ,UAAA,IAAAa,IAAA,CAAAC,GAAA,IAAAN,WAAA,CAAAjE,MAAA;UACA;YACA;YACAyD,UAAA,IAAAQ,WAAA,CAAAjE,MAAA;UACA;QACA;MAAA,SAAAwE,GAAA;QAAAd,SAAA,CAAA5D,CAAA,CAAA0E,GAAA;MAAA;QAAAd,SAAA,CAAAe,CAAA;MAAA;MAEA,OAAAhB,UAAA;IACA;IACA;IACAiB,oBAAA,WAAAA,qBAAA3B,KAAA;MACA,KAAAA,KAAA,SAAAA,KAAA;MACA;MACA,IAAA4B,KAAA,GAAA5B,KAAA,CAAAO,KAAA,eAAAlC,GAAA,WAAAyC,CAAA;QAAA,OAAAA,CAAA,CAAAb,IAAA;MAAA,GAAAO,MAAA,CAAApM,OAAA;MACA,IAAAwN,KAAA,CAAA3E,MAAA;QACA;QACA,IAAA4E,SAAA;QACA,IAAAC,IAAA,GAAAF,KAAA,CAAAA,KAAA,CAAA3E,MAAA;QACA,IAAA4E,SAAA,CAAAR,IAAA,CAAAS,IAAA;UACA;UACA,OAAAF,KAAA,CAAAG,KAAA,QAAAC,IAAA,cAAAF,IAAA;QACA;MACA;MACA,OAAA9B,KAAA;IACA;IACA;IACAiC,wBAAA,WAAAA,yBAAAjC,KAAA;MACA,KAAAA,KAAA,KAAAA,KAAA,CAAAC,IAAA;;MAEA;MACA,IAAAC,UAAA,GAAAF,KAAA,CAAAC,IAAA;;MAEA;MACA,IAAAiC,gBAAA,GAAAhC,UAAA,CAAAG,KAAA;MAEA,KAAA6B,gBAAA;;MAEA;MACA,OAAAA,gBAAA,IAAAjF,MAAA;IACA;IACAkF,uBAAA,WAAAA,wBAAAnB,KAAA;MAAA,IAAAoB,MAAA;MACA;MACA,KAAAnM,WAAA,QAAA8J,oBAAA,CAAAiB,KAAA;;MAEA;MACA,IAAAqB,kBAAA,QAAAJ,wBAAA,CAAAjB,KAAA;MACA,KAAA9K,qBAAA,GAAAmM,kBAAA,gBAAApM,WAAA;MAEA,KAAA+K,KAAA;QACA,KAAA7L,QAAA,CAAAI,SAAA;UACAC,QAAA,EAAAC,SAAA;UACAC,KAAA;UACAC,UAAA;UACAC,SAAA;UACAC,MAAA;UACAC,OAAA;UACAC,KAAA;UACAC,SAAA,OAAAW,UAAA;QACA;QACA;MACA;;MAEA;MACA,SAAAT,qBAAA;QAEA;MACA;MACA;MACA,IAAAoM,MAAA,OAAAH,0CAAA,EAAAnB,KAAA;MACA;MACA,KAAAsB,MAAA,KAAAA,MAAA,CAAAC,MAAA,KAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,WAAAvE,CAAA;QAAA,OAAAA,CAAA,CAAAzI,QAAA;MAAA;QACA,IAAA8K,KAAA,GAAAU,KAAA,CAAAT,KAAA,YAAAlC,GAAA,WAAAoC,IAAA;UAAA,OAAAA,IAAA,CAAAR,IAAA;QAAA,GAAAO,MAAA,CAAApM,OAAA;QACA,IAAAqO,SAAA;QAAA,IAAAC,UAAA,OAAA9B,2BAAA,CAAAvM,OAAA,EACAiM,KAAA;UAAAqC,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAA5B,CAAA,MAAA6B,MAAA,GAAAD,UAAA,CAAA/H,CAAA,IAAAoG,IAAA;YAAA,IAAAN,IAAA,GAAAkC,MAAA,CAAA3B,KAAA;YACA,IAAA4B,UAAA,OAAAT,0CAAA,EAAA1B,IAAA;YACA,IAAAmC,UAAA,IAAAA,UAAA,CAAAL,MAAA,IAAAK,UAAA,CAAAL,MAAA,CAAAtF,MAAA;cACAwF,SAAA,GAAAA,SAAA,CAAAI,MAAA,CAAAD,UAAA,CAAAL,MAAA;YACA;UACA;QAAA,SAAAd,GAAA;UAAAiB,UAAA,CAAA3F,CAAA,CAAA0E,GAAA;QAAA;UAAAiB,UAAA,CAAAhB,CAAA;QAAA;QACAY,MAAA;UAAAC,MAAA,EAAAE;QAAA;MACA;MAIA,IAAAH,MAAA;QACA;QACA,IAAAA,MAAA,CAAAC,MAAA,IAAAD,MAAA,CAAAC,MAAA,CAAAtF,MAAA;UAEA;UACA,KAAA4B,IAAA,MAAA1J,QAAA;UACA;UACAmN,MAAA,CAAAC,MAAA,CAAAO,OAAA,WAAAvE,KAAA;YACA,IAAAlB,QAAA;cACA7H,QAAA,EAAA+I,KAAA,CAAA/I,QAAA;cACAI,SAAA,EAAA2I,KAAA,CAAA3I,SAAA;cACAC,MAAA,EAAA0I,KAAA,CAAA1I,MAAA;cACAC,OAAA,EAAAyI,KAAA,CAAAzI,OAAA;cACAC,KAAA,EAAAwI,KAAA,CAAAxI,KAAA;cACAJ,UAAA,EAAA4I,KAAA,CAAA5I,UAAA;cACAD,KAAA,EAAA6I,KAAA,CAAA7I,KAAA,WAAAnB,MAAA,CAAAgK,KAAA,CAAA7I,KAAA;cAAA;cACAM,SAAA,EAAAoM,MAAA,CAAAzL,UAAA,gBAAA4H,KAAA,CAAAvI,SAAA;YACA;YAEAoM,MAAA,CAAAjN,QAAA,CAAAI,SAAA,CAAAgG,IAAA,CAAA8B,QAAA;UACA;QACA;UAEA;UACA,IAAAA,QAAA;YACA7H,QAAA,EAAA8M,MAAA,CAAA9M,QAAA;YACAI,SAAA,EAAA0M,MAAA,CAAA1M,SAAA;YACAC,MAAA,EAAAyM,MAAA,CAAAzM,MAAA;YACAC,OAAA,EAAAwM,MAAA,CAAAxM,OAAA;YACAC,KAAA,EAAAuM,MAAA,CAAAvM,KAAA;YACAJ,UAAA,EAAA2M,MAAA,CAAA3M,UAAA;YACAD,KAAA,EAAA4M,MAAA,CAAA5M,KAAA,WAAAnB,MAAA,CAAA+N,MAAA,CAAA5M,KAAA;YAAA;YACAM,SAAA,OAAAW,UAAA,gBAAA2L,MAAA,CAAAtM,SAAA;UACA;UAEA,KAAA6I,IAAA,MAAA1J,QAAA,gBAAAkI,QAAA;QACA;;QAEA;QACA,IAAA0F,WAAA,QAAA5N,QAAA,CAAAI,SAAA,CAAA8I,GAAA,WAAAE,KAAA;UAAA,WAAAjB,cAAA,CAAAjJ,OAAA,MAAAiJ,cAAA,CAAAjJ,OAAA,MACAkK,KAAA;YACA3I,SAAA,EAAA2I,KAAA,CAAA3I,SAAA;YACAC,MAAA,EAAA0I,KAAA,CAAA1I,MAAA;YACAC,OAAA,EAAAyI,KAAA,CAAAzI,OAAA;YACAC,KAAA,EAAAwI,KAAA,CAAAxI,KAAA;YACAJ,UAAA,EAAA4I,KAAA,CAAA5I,UAAA;YACAD,KAAA,EAAA6I,KAAA,CAAA7I,KAAA;YACAM,SAAA,EAAAuI,KAAA,CAAAvI,SAAA;UAAA;QAAA,CACA;QAEA,KAAA6I,IAAA,MAAAtI,SAAA,eAAAwM,WAAA;;QAEA;QACA,KAAAxF,SAAA;UAEA6E,MAAA,CAAA5E,YAAA;UACA;UACA4E,MAAA,CAAAjN,QAAA,CAAAI,SAAA,CAAAuN,OAAA,WAAAvE,KAAA,EAAAC,KAAA;YACA,IAAAE,YAAA,OAAApB,cAAA,CAAAjJ,OAAA,MAAAkK,KAAA;YAEA6D,MAAA,CAAAvD,IAAA,CAAAuD,MAAA,CAAAjN,QAAA,CAAAI,SAAA,EAAAiJ,KAAA,EAAAE,YAAA;UACA;UAEA0D,MAAA,CAAAvE,YAAA;QACA;MACA;IACA;IACAmF,yBAAA,WAAAA,0BAAAC,OAAA;MACA;QACA,KAAAA,OAAA;;QAEA;QACA,KAAAA,OAAA,CAAAC,UAAA;UACA,OAAAD,OAAA;QACA;QAEA,IAAAvO,IAAA,UAAAuO,OAAA,gBAAApG,IAAA,CAAAC,KAAA,CAAAmG,OAAA,IAAAA,OAAA;QACA,KAAAvO,IAAA,CAAAyO,OAAA,KAAA1G,KAAA,CAAAC,OAAA,CAAAhI,IAAA,CAAAyO,OAAA,UAAAF,OAAA;QAEA,OAAAvO,IAAA,CAAAyO,OAAA,CAAA9E,GAAA,WAAA+E,GAAA;UACA;UACA,IAAAA,GAAA,CAAAC,KAAA,KAAA5N,SAAA;YACA,sBAAAoN,MAAA,CAAAO,GAAA,CAAAC,KAAA;UACA;UACA;UACA,IAAAD,GAAA,CAAAE,KAAA,KAAA7N,SAAA,IAAA2N,GAAA,CAAAG,KAAA,KAAA9N,SAAA;YACA,gBAAAoN,MAAA,CAAAO,GAAA,CAAAE,KAAA,YAAAT,MAAA,CAAAO,GAAA,CAAAG,KAAA;UACA;UACA;UACA,IAAAC,MAAA,GAAA/O,MAAA,CAAA+O,MAAA,CAAAJ,GAAA;UACA,OAAAI,MAAA,CAAAxB,IAAA;QACA,GAAAA,IAAA;MACA,SAAAlG,KAAA;QACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA,cAAAmH,OAAA;QACA,OAAAA,OAAA;MACA;IACA;IACA;IACAQ,oBAAA,WAAAA,qBAAAlF,KAAA;MACA,KAAAA,KAAA,CAAA5I,UAAA;;MAEA;MACA,IAAA4I,KAAA,CAAA/I,QAAA,UAAA+I,KAAA,CAAA/I,QAAA,UAAA+I,KAAA,CAAA/I,QAAA,UAAA+I,KAAA,CAAA/I,QAAA;QACA;UACA;UACA,IAAA+I,KAAA,CAAA5I,UAAA,CAAAuN,UAAA,SAAA3E,KAAA,CAAA5I,UAAA,CAAAgJ,QAAA;YACA,YAAAqE,yBAAA,CAAAzE,KAAA,CAAA5I,UAAA;UACA;UACA;UACA,OAAA4I,KAAA,CAAA5I,UAAA;QACA,SAAAmG,KAAA;UACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA;UACA,OAAAyC,KAAA,CAAA5I,UAAA;QACA;MACA;;MAEA;MACA,OAAA4I,KAAA,CAAA5I,UAAA;IACA;IACA;IACA+N,iBAAA,WAAAA,kBAAA1C,KAAA,EAAAxC,KAAA;MACA,IAAAD,KAAA,QAAApJ,QAAA,CAAAI,SAAA,CAAAiJ,KAAA;;MAEA;MACA,IAAAD,KAAA,CAAA/I,QAAA,UAAA+I,KAAA,CAAA/I,QAAA,UAAA+I,KAAA,CAAA/I,QAAA,UAAA+I,KAAA,CAAA/I,QAAA;QACA;QACA;QACA,KAAAqJ,IAAA,MAAA1J,QAAA,CAAAI,SAAA,EAAAiJ,KAAA,MAAAlB,cAAA,CAAAjJ,OAAA,MAAAiJ,cAAA,CAAAjJ,OAAA,MAAAkK,KAAA;UAAA5I,UAAA,EAAAqL;QAAA;MACA;QACA;QACA,KAAAnC,IAAA,MAAA1J,QAAA,CAAAI,SAAA,EAAAiJ,KAAA,MAAAlB,cAAA,CAAAjJ,OAAA,MAAAiJ,cAAA,CAAAjJ,OAAA,MAAAkK,KAAA;UAAA5I,UAAA,EAAAqL;QAAA;MACA;MAEA,KAAAxD,YAAA;IACA;IACAvE,YAAA,WAAAA,aAAA;MAAA,IAAA0K,MAAA;MACA;QACA;QACA,KAAAxL,cAAA,QAAA3D,GAAA,CAAAsE,MAAA;QACA,KAAAV,oBAAA,QAAA5D,GAAA,CAAAwD,YAAA;;QAEA;QACA,KAAA7C,QAAA,CAAAC,eAAA,QAAAZ,GAAA,CAAAoP,WAAA;QACA,KAAAzO,QAAA,CAAAE,aAAA,QAAAb,GAAA,CAAAoP,WAAA;;QAEA;QACA,IAAAC,mBAAA,QAAAb,yBAAA,MAAAxO,GAAA,CAAAmB,UAAA;;QAEA;QACA,IAAA4I,KAAA;UACA/I,QAAA,OAAAhB,GAAA,CAAAgB,QAAA;UACAE,KAAA,OAAAlB,GAAA,CAAAkB,KAAA,WAAAnB,MAAA,MAAAC,GAAA,CAAAkB,KAAA;UAAA;UACAC,UAAA,EAAAkO,mBAAA;UAAA;UACAjO,SAAA,OAAApB,GAAA,CAAAoB,SAAA;UACAC,MAAA,OAAArB,GAAA,CAAAqB,MAAA;UACAC,OAAA,OAAAtB,GAAA,CAAAsB,OAAA;UACAC,KAAA,OAAAvB,GAAA,CAAAuB,KAAA;UACAC,SAAA,OAAAxB,GAAA,CAAAwB;QACA;;QAEA;QACA,KAAAb,QAAA,CAAAI,SAAA,IAAAgJ,KAAA;;QAEA;QACA,SAAA/J,GAAA,CAAAc,MAAA;UACA,KAAAH,QAAA,CAAAG,MAAA,QAAAd,GAAA,CAAAc,MAAA;QACA;;QAEA;QACA,SAAAd,GAAA,CAAAwD,YAAA;UAEA,KAAAA,YAAA,QAAAxD,GAAA,CAAAwD,YAAA;QACA;;QAEA;QACA,KAAAuF,SAAA;UACAoG,MAAA,CAAAnG,YAAA;UACA;UACAmG,MAAA,CAAA9F,YAAA;QACA;MACA,SAAA/B,KAAA;QACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA;MACA;IACA;IACAgI,uBAAA,WAAAA,wBAAAnO,UAAA,EAAAH,QAAA;MACA,KAAAG,UAAA;QAAAwN,OAAA;MAAA;;MAEA;MACA,IAAA3N,QAAA,UAAAA,QAAA;QACA,YAAAuO,mBAAA,CAAApO,UAAA,EAAAH,QAAA;MACA;;MAEA;MACA,IAAAA,QAAA,UAAAA,QAAA;QACA,YAAAwO,kBAAA,CAAArO,UAAA,EAAAH,QAAA;MACA;;MAEA;MACA,IAAAyO,YAAA,GAAAtO,UAAA,CAAA4K,KAAA,MAAAC,MAAA,WAAA7F,CAAA;QAAA,OAAAA,CAAA,CAAAsF,IAAA;MAAA;;MAEA;MACA,IAAAiE,SAAA;QACA;QACA,YAAAC,EAAAf,GAAA;UAAA;YAAA3H,CAAA,EAAA2I,QAAA,CAAAhB,GAAA;UAAA;QAAA;QACA,YAAAe,EAAAf,GAAA;UAAA;YAAA3H,CAAA,EAAA2I,QAAA,CAAAhB,GAAA;UAAA;QAAA;QACA,aAAAe,EAAAf,GAAA;UAAA;YAAA3H,CAAA,EAAA2I,QAAA,CAAAhB,GAAA;UAAA;QAAA;QACA,aAAAe,EAAAf,GAAA;UAAA;YAAA3H,CAAA,EAAA2H;UAAA;QAAA;QAAA;QACA,aAAAe,EAAAf,GAAA;UAAA;YAAA3H,CAAA,EAAA2H;UAAA;QAAA;QAAA;QACA,aAAAe,EAAAf,GAAA;UAAA;YAAA3H,CAAA,EAAA2H;UAAA;QAAA;QAAA;QACA;QACA,YAAAe,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;UAAA;QACA;QACA,YAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;UAAA;QACA;QACA;QACA,aAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;UAAA;QACA;QAEA;QACA,YAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;YAAAE,CAAA,EAAAF,MAAA;UAAA;QACA;QACA,YAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;YAAAE,CAAA,EAAAF,MAAA;UAAA;QACA;QACA,YAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;YAAAE,CAAA,EAAAF,MAAA;UAAA;QACA;QACA,cAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;YAAAE,CAAA,EAAAF,MAAA;UAAA;QACA;QAEA;QACA,YAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;YAAAE,CAAA,EAAAF,MAAA;YAAAG,CAAA,EAAAH,MAAA;UAAA;QACA;QACA,YAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;YAAAE,CAAA,EAAAF,MAAA;YAAAG,CAAA,EAAAH,MAAA;UAAA;QACA;QAEA;QACA,aAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;YAAAE,CAAA,EAAAF,MAAA;YAAAG,CAAA,EAAAH,MAAA;YAAAtH,CAAA,EAAAsH,MAAA;UAAA;QACA;QACA,aAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;YAAAE,CAAA,EAAAF,MAAA;YAAAG,CAAA,EAAAH,MAAA;YAAAtH,CAAA,EAAAsH,MAAA;UAAA;QACA;QAEA;QACA,aAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;YAAAE,CAAA,EAAAF,MAAA;YAAAG,CAAA,EAAAH,MAAA;YAAAtH,CAAA,EAAAsH,MAAA;YAAA3C,CAAA,EAAA2C,MAAA;UAAA;QACA;QACA,aAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;YAAAE,CAAA,EAAAF,MAAA;YAAAG,CAAA,EAAAH,MAAA;YAAAtH,CAAA,EAAAsH,MAAA;YAAA3C,CAAA,EAAA2C,MAAA;UAAA;QACA;QAEA;QACA,aAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;YAAAE,CAAA,EAAAF,MAAA;YAAAG,CAAA,EAAAH,MAAA;YAAAtH,CAAA,EAAAsH,MAAA;YAAA3C,CAAA,EAAA2C,MAAA;YAAApG,CAAA,EAAAoG,MAAA;UAAA;QACA;QACA,aAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;YAAAE,CAAA,EAAAF,MAAA;YAAAG,CAAA,EAAAH,MAAA;YAAAtH,CAAA,EAAAsH,MAAA;YAAA3C,CAAA,EAAA2C,MAAA;YAAApG,CAAA,EAAAoG,MAAA;UAAA;QACA;QAEA;QACA,aAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;YAAAE,CAAA,EAAAF,MAAA;YAAAG,CAAA,EAAAH,MAAA;YAAAtH,CAAA,EAAAsH,MAAA;YAAA3C,CAAA,EAAA2C,MAAA;YAAApG,CAAA,EAAAoG,MAAA;YAAAI,CAAA,EAAAJ,MAAA;UAAA;QACA;QACA,aAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;YAAAE,CAAA,EAAAF,MAAA;YAAAG,CAAA,EAAAH,MAAA;YAAAtH,CAAA,EAAAsH,MAAA;YAAA3C,CAAA,EAAA2C,MAAA;YAAApG,CAAA,EAAAoG,MAAA;YAAAI,CAAA,EAAAJ,MAAA;UAAA;QACA;QAEA;QACA,aAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;YAAAE,CAAA,EAAAF,MAAA;YAAAG,CAAA,EAAAH,MAAA;YAAAtH,CAAA,EAAAsH,MAAA;YAAA3C,CAAA,EAAA2C,MAAA;YAAApG,CAAA,EAAAoG,MAAA;YAAAI,CAAA,EAAAJ,MAAA;YAAAK,CAAA,EAAAL,MAAA;UAAA;QACA;QACA,aAAAF,EAAAf,GAAA;UACA,IAAAiB,MAAA,GAAAjB,GAAA,CAAA7C,KAAA,KAAAlC,GAAA,WAAA1D,CAAA;YAAA,OAAAyJ,QAAA,CAAAzJ,CAAA;UAAA;UACA;YAAAc,CAAA,EAAA4I,MAAA;YAAAC,CAAA,EAAAD,MAAA;YAAAE,CAAA,EAAAF,MAAA;YAAAG,CAAA,EAAAH,MAAA;YAAAtH,CAAA,EAAAsH,MAAA;YAAA3C,CAAA,EAAA2C,MAAA;YAAApG,CAAA,EAAAoG,MAAA;YAAAI,CAAA,EAAAJ,MAAA;YAAAK,CAAA,EAAAL,MAAA;UAAA;QACA;QAEA;QACA,aAAAF,EAAA;UAAA;QAAA;QACA,aAAAA,EAAA;UAAA;QAAA;QAEA;QACA,aAAAA,EAAA;UAAA;QAAA;QACA,aAAAA,EAAA;UAAA;QAAA;QACA,aAAAA,EAAA;UAAA;QAAA;QACA,aAAAA,EAAA;UAAA;QAAA;QACA,aAAAA,EAAA;UAAA;QAAA;QACA,aAAAA,EAAA;UAAA;QAAA;QACA,aAAAA,EAAA;UAAA;QAAA;QAEA;QACA,aAAAA,EAAA;UAAA;QAAA;QACA,aAAAA,EAAA;UAAA;QAAA;QACA,aAAAA,EAAA;UAAA;QAAA;QACA,aAAAA,EAAA;UAAA;QAAA;QACA,aAAAA,EAAA;UAAA;QAAA;QACA,aAAAA,EAAA;UAAA;QAAA;QACA,aAAAA,EAAA;UAAA;QAAA;QAEA;QACA,aAAAA,EAAA;UAAA;QAAA;QAEA;QACA,aAAAA,EAAA;UAAA;QAAA;QACA;QACA,aAAAA,EAAA;UAAA;QAAA;MAEA;MAEA,IAAAQ,SAAA,GAAAT,SAAA,CAAA1O,QAAA;MACA,KAAAmP,SAAA;QAAAxB,OAAA;MAAA;MAEA;QACAA,OAAA,EAAAc,YAAA,CAAA5F,GAAA,WAAA+E,GAAA;UAAA,OAAAuB,SAAA,CAAAvB,GAAA,CAAAnD,IAAA;QAAA;MACA;IACA;IACA2E,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,eAAA,QAAAC,aAAA;MACA,KAAAD,eAAA,CAAAE,KAAA;QACA,KAAAC,MAAA,CAAAH,eAAA,CAAAzO,OAAA;UACA8E,iBAAA;UACAhH,IAAA;UACA+Q,wBAAA;QACA;QACA;MACA;MAEA,KAAAnL,KAAA,SAAAoL,QAAA,WAAAH,KAAA;QACA,IAAAA,KAAA;UACA,IAAAjG,eAAA,GAAA8F,MAAA,CAAA7F,QAAA;YACAC,IAAA;YACAC,IAAA;YACAC,OAAA;YACAC,UAAA;UACA;;UAEA;UACA,IAAAgG,UAAA,GAAAP,MAAA,CAAA1P,QAAA,CAAAI,SAAA,CAAA8I,GAAA,WAAAE,KAAA,EAAAC,KAAA;YACA;YACA,IAAA2E,OAAA,GAAA0B,MAAA,CAAAf,uBAAA,CAAAvF,KAAA,CAAA5I,UAAA,EAAA4I,KAAA,CAAA/I,QAAA;YACA;YACA,IAAA6P,SAAA,GAAAzO,cAAA,CAAAC,OAAA;YAEA,IAAAyO,MAAA;cACAtP,SAAA,EAAAuI,KAAA,CAAAvI,SAAA;cACA4N,WAAA,EAAArF,KAAA,CAAAvI,SAAA,SAAA6O,MAAA,CAAA1P,QAAA,CAAAC,eAAA,GAAAyP,MAAA,CAAA1P,QAAA,CAAAE,aAAA;cACAG,QAAA,EAAA+I,KAAA,CAAA/I,QAAA;cACA;cACAsD,MAAA,EAAAC,MAAA,CAAA8L,MAAA,CAAAnO,cAAA;cACA;cACA2O,SAAA,EAAAA,SAAA,GAAAtM,MAAA,CAAAsM,SAAA;cACA1P,UAAA,EAAAkH,IAAA,CAAA0I,SAAA,CAAApC,OAAA;cACAzN,KAAA,EAAA6I,KAAA,CAAA7I,KAAA;cACAE,SAAA,EAAA2I,KAAA,CAAA3I,SAAA;cACAC,MAAA,EAAA0I,KAAA,CAAA1I,MAAA;cACAC,OAAA,EAAAyI,KAAA,CAAAzI,OAAA;cACAC,KAAA,EAAAwI,KAAA,CAAAxI,KAAA;cACAlB,WAAA,EAAAgQ,MAAA,CAAA3G,cAAA,CAAAK,KAAA;cACAzJ,SAAA,EAAA+P,MAAA,CAAA1G,YAAA,CAAAI,KAAA;cACAjJ,MAAA,EAAAuP,MAAA,CAAA1P,QAAA,CAAAG,MAAA;cACAkQ,OAAA,EAAAX,MAAA,CAAAY,UAAA,KAAAC,IAAA;cACA1N,YAAA,EAAA6M,MAAA,CAAA7M,YAAA;YACA;YACA;YACA,IAAA6M,MAAA,CAAArQ,GAAA,IAAAgK,KAAA;cACA8G,MAAA,CAAA9L,KAAA,GAAAqL,MAAA,CAAArQ,GAAA,CAAAgF,KAAA;YACA;YACA;;YAEA,OAAA8L,MAAA;UACA;;UAEA;;UAGA;UACA,IAAAlJ,GAAA,GAAAyI,MAAA,CAAArQ,GAAA;UACA,IAAA6H,MAAA,GAAAwI,MAAA,CAAArQ,GAAA;UACA,IAAAE,IAAA,GAAAmQ,MAAA,CAAArQ,GAAA,GAAA4Q,UAAA,MAAAA,UAAA;UAEA,IAAAjJ,gBAAA;YACAC,GAAA,EAAAA,GAAA;YACAC,MAAA,EAAAA,MAAA;YACA3H,IAAA,EAAAA;UACA,GAAA2G,IAAA,WAAAd,QAAA;YACA,IAAAA,QAAA,CAAAQ,IAAA;cACA8J,MAAA,CAAAc,MAAA,CAAAC,UAAA;cACAf,MAAA,CAAA1L,KAAA;cACA;cACA0L,MAAA,CAAA1P,QAAA,CAAAG,MAAA;cACAuP,MAAA,CAAA1P,QAAA,CAAAI,SAAA;gBACAC,QAAA,EAAAC,SAAA;gBACAC,KAAA;gBACAC,UAAA;gBACAC,SAAA;gBACAC,MAAA;gBACAC,OAAA;gBACAC,KAAA;gBACAC,SAAA;cACA;cACA6O,MAAA,CAAArH,YAAA;cACA;cACA,IAAAqH,MAAA,CAAArQ,GAAA;gBACAqQ,MAAA,CAAA3H,KAAA;cACA;YACA;cACA;cACA,IAAA3C,QAAA,CAAA7F,IAAA,IAAA+H,KAAA,CAAAC,OAAA,CAAAnC,QAAA,CAAA7F,IAAA,CAAAmR,aAAA;gBACA;gBACA,IAAAC,YAAA,GAAAjB,MAAA,CAAA1P,QAAA,CAAAI,SAAA,CAAAiL,MAAA,WAAA2D,CAAA,EAAA3F,KAAA;kBAAA,OACAjE,QAAA,CAAA7F,IAAA,CAAAmR,aAAA,CAAAlH,QAAA,CAAAH,KAAA;gBAAA,CACA;gBACAqG,MAAA,CAAAhG,IAAA,CAAAgG,MAAA,CAAA1P,QAAA,eAAA2Q,YAAA;gBACAjB,MAAA,CAAAc,MAAA,CAAAI,QAAA,IAAAlD,MAAA,CAAAtI,QAAA,CAAA7F,IAAA,CAAAmR,aAAA,CAAA5I,MAAA;cACA;gBACA;gBACA,IAAA1C,QAAA,CAAAyL,aAAA;kBACA;gBAAA,CAEA;kBACA;kBACAnB,MAAA,CAAAc,MAAA,CAAAI,QAAA,CAAAxL,QAAA,CAAAW,GAAA;gBACA;cACA;YACA;UACA,GAAAM,KAAA,WAAAM,KAAA;YACAD,OAAA,CAAAC,KAAA,YAAAA,KAAA;;YAEA;YACA;YACA+I,MAAA,CAAAc,MAAA,CAAAI,QAAA;UACA,GAAAjG,OAAA;YACAf,eAAA,CAAA7B,KAAA;YACA2H,MAAA,CAAArH,YAAA;UACA;QACA;MACA;IACA;IACAW,YAAA,WAAAA,aAAAI,KAAA;MACA;MACA,IAAAA,KAAA,CAAA/I,QAAA,UAAA+I,KAAA,CAAA/I,QAAA;QACA;MACA;;MAEA;MACA,IAAA+I,KAAA,CAAA/I,QAAA,UAAA+I,KAAA,CAAA/I,QAAA;QACA;MACA;MAEA,SAAAyQ,eAAA,CAAA1H,KAAA,CAAA/I,QAAA;QACA,6DAAAmJ,QAAA,CAAAJ,KAAA,CAAA/I,QAAA;UACA,OAAA+I,KAAA,CAAAxI,KAAA;QACA;QACA,aAAA4I,QAAA,CAAAJ,KAAA,CAAA/I,QAAA;UACA,IAAA+I,KAAA,CAAA3I,SAAA,IAAA2I,KAAA,CAAA1I,MAAA;YACA;UACA;UACA;QACA;QACA,aAAA8I,QAAA,CAAAJ,KAAA,CAAA/I,QAAA;UAAA;UACA;QACA;QACA;MACA;MACA,IAAA+I,KAAA,CAAA5I,UAAA;QACA,OAAA4I,KAAA,CAAA5I,UAAA,CAAA4K,KAAA,MAAAC,MAAA,WAAAM,CAAA;UAAA,OAAAA,CAAA,CAAAb,IAAA;QAAA,GAAAhD,MAAA;MACA;MACA;IACA;IACAiB,cAAA,WAAAA,eAAAK,KAAA;MACA,IAAA2H,IAAA,QAAA/H,YAAA,CAAAI,KAAA;MACA,IAAA7I,KAAA,GAAAqD,MAAA,CAAAwF,KAAA,CAAA7I,KAAA;MACA,OAAAwQ,IAAA,GAAAxQ,KAAA;IACA;IACAuQ,eAAA,WAAAA,gBAAAzQ,QAAA;MACA,gFAAAmJ,QAAA,CAAAnJ,QAAA;IACA;IACA;IACAuO,mBAAA,WAAAA,oBAAApO,UAAA,EAAAH,QAAA;MACA;MACA,IAAAoM,KAAA,GAAAjM,UAAA,CAAA4K,KAAA;MACA,IAAAqB,KAAA,CAAA3E,MAAA;QAAAkG,OAAA;MAAA;MAEA,IAAAG,KAAA,GAAA1B,KAAA,IAAAuE,OAAA;MACA,IAAA5C,KAAA,GAAA3B,KAAA,IAAAuE,OAAA,oBAAA5F,KAAA,YAAAC,MAAA,WAAA7F,CAAA;QAAA,OAAAA,CAAA,IAAAA,CAAA,KAAA2I,KAAA;MAAA;MAEA;QACAH,OAAA;UACAG,KAAA,EAAAA,KAAA;UACAC,KAAA,EAAAA,KAAA,CAAAvB,IAAA;QACA;MACA;IACA;IACA;IACAgC,kBAAA,WAAAA,mBAAArO,UAAA,EAAAH,QAAA;MACAqG,OAAA,CAAAuK,GAAA,2BAAAzQ,UAAA,eAAAH,QAAA;;MAEA;MACA,IAAAG,UAAA,CAAAuN,UAAA;QACA;UACA,IAAAmD,MAAA,GAAAxJ,IAAA,CAAAC,KAAA,CAAAnH,UAAA;UACAkG,OAAA,CAAAuK,GAAA,cAAAC,MAAA;UACA,OAAAA,MAAA;QACA,SAAAvK,KAAA;UACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;QACA;MACA;;MAEA;MACA,IAAAwK,UAAA;;MAEA;MACA,IAAAjG,KAAA,GAAA1K,UAAA,CAAA0K,KAAA;MACA,IAAAA,KAAA;QACAiG,UAAA,GAAAjG,KAAA;MACA;QACA;QACAA,KAAA,GAAA1K,UAAA,CAAA0K,KAAA;QACA,IAAAA,KAAA;UACAiG,UAAA,GAAAjG,KAAA;QACA;UACA;UACA,IAAA7K,QAAA,UAAAA,QAAA;YACA8Q,UAAA,IAAA9Q,QAAA,OAAA+Q,QAAA;UACA;QACA;MACA;MAEA1K,OAAA,CAAAuK,GAAA,aAAAE,UAAA;MAEA,IAAAA,UAAA;QACAzK,OAAA,CAAA2K,IAAA;QACA;UAAArD,OAAA;QAAA;MACA;MAEA,IAAAb,MAAA;QACAa,OAAA;UACAE,KAAA,EAAAiD;QACA;MACA;MAEAzK,OAAA,CAAAuK,GAAA,2BAAA9D,MAAA;MACA,OAAAA,MAAA;IACA;IACAmE,gBAAA,WAAAA,iBAAAlI,KAAA,EAAAmI,KAAA,EAAA1F,KAAA;MAEA,IAAAxC,KAAA,QAAArJ,QAAA,CAAAI,SAAA,CAAAkJ,SAAA,WAAAR,CAAA;QAAA,OAAAA,CAAA,KAAAM,KAAA;MAAA;MACA,IAAAC,KAAA;QACA,IAAAE,YAAA,OAAApB,cAAA,CAAAjJ,OAAA,MAAAiJ,cAAA,CAAAjJ,OAAA,MACA,KAAAc,QAAA,CAAAI,SAAA,CAAAiJ,KAAA,YAAAmI,gBAAA,CAAAtS,OAAA,MACAqS,KAAA,EAAA1F,KAAA,EACA;QACA,KAAAnC,IAAA,MAAA1J,QAAA,CAAAI,SAAA,EAAAiJ,KAAA,EAAAE,YAAA;QACA,KAAAb,YAAA;MACA;IACA;IAGA;IACA4H,UAAA,WAAAA,WAAAmB,IAAA;MACA,IAAAC,GAAA,YAAAA,IAAAzD,GAAA;QAAA,OAAAA,GAAA,YAAAP,MAAA,CAAAO,GAAA,IAAAA,GAAA;MAAA;MACA,IAAA0D,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAH,GAAA,CAAAD,IAAA,CAAAK,QAAA;MACA,IAAAC,GAAA,GAAAL,GAAA,CAAAD,IAAA,CAAAO,OAAA;MACA,IAAAC,KAAA,GAAAP,GAAA,CAAAD,IAAA,CAAAS,QAAA;MACA,IAAAC,OAAA,GAAAT,GAAA,CAAAD,IAAA,CAAAW,UAAA;MACA,IAAAC,OAAA,GAAAX,GAAA,CAAAD,IAAA,CAAAa,UAAA;MACA,UAAA5E,MAAA,CAAAiE,IAAA,OAAAjE,MAAA,CAAAmE,KAAA,OAAAnE,MAAA,CAAAqE,GAAA,OAAArE,MAAA,CAAAuE,KAAA,OAAAvE,MAAA,CAAAyE,OAAA,OAAAzE,MAAA,CAAA2E,OAAA;IACA;IACAvL,cAAA,WAAAA,eAAA;MACA,IAAAnD,MAAA,GAAAlC,cAAA,CAAAC,OAAA;MAEA,IAAAiC,MAAA;QACA;QACA,KAAApC,cAAA,GAAAqC,MAAA,CAAAD,MAAA;QACA,KAAA4O,YAAA,CAAA5O,MAAA;MACA;QACA;QACA,KAAA6O,mBAAA;MACA;IACA;IAEAD,YAAA,WAAAA,aAAA5O,MAAA;MAAA,IAAA8O,MAAA;MACA,IAAAzL,gBAAA;QACAC,GAAA,oBAAAyG,MAAA,CAAA/J,MAAA;QACAuD,MAAA;MACA,GAAAhB,IAAA,WAAAwM,GAAA;QACA,IAAAA,GAAA,CAAA9M,IAAA,YAAA8M,GAAA,CAAAnT,IAAA;UACAkT,MAAA,CAAApR,WAAA,GAAAqR,GAAA,CAAAnT,IAAA;QACA;UACAkT,MAAA,CAAApR,WAAA;YAAA3C,IAAA;UAAA;QACA;MACA,GAAA2H,KAAA,WAAAM,KAAA;QACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;QACA8L,MAAA,CAAApR,WAAA;UAAA3C,IAAA;QAAA;MACA;IACA;IAEA8T,mBAAA,WAAAA,oBAAA;MAAA,IAAAG,MAAA;MACA;MACA,IAAA3L,gBAAA;QACAC,GAAA;QACAC,MAAA;QACAC,MAAA;UAAAC,OAAA;UAAAC,QAAA;QAAA;MACA,GAAAnB,IAAA,WAAAwM,GAAA;QACA,IAAAA,GAAA,CAAA9M,IAAA,YAAA8M,GAAA,CAAAlL,IAAA,IAAAkL,GAAA,CAAAlL,IAAA,CAAAM,MAAA;UACA;UACA,IAAA8K,SAAA,GAAAF,GAAA,CAAAlL,IAAA;UACAmL,MAAA,CAAApR,cAAA,GAAAqR,SAAA,CAAAjP,MAAA;UACAgP,MAAA,CAAAtR,WAAA,GAAAuR,SAAA;UACA;UACAnR,cAAA,CAAAoR,OAAA,YAAAD,SAAA,CAAAjP,MAAA;QAEA;UACA+C,OAAA,CAAA2K,IAAA;UACAsB,MAAA,CAAAtR,WAAA;YAAA3C,IAAA;UAAA;UACAiU,MAAA,CAAApR,cAAA;QACA;MACA,GAAA8E,KAAA,WAAAM,KAAA;QACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;QACAgM,MAAA,CAAAtR,WAAA;UAAA3C,IAAA;QAAA;QACAiU,MAAA,CAAApR,cAAA;MACA;IACA;IACAkC,WAAA,WAAAA,YAAA;MAAA,IAAAqP,OAAA;MACA,IAAA9L,gBAAA;QACAC,GAAA;QACAC,MAAA;QACAC,MAAA;UAAAC,OAAA;UAAAC,QAAA;QAAA;MACA,GAAAnB,IAAA,WAAAwM,GAAA;QACA,IAAAA,GAAA,CAAA9M,IAAA,YAAA8M,GAAA,CAAAlL,IAAA;UACAsL,OAAA,CAAAxR,QAAA,GAAAoR,GAAA,CAAAlL,IAAA;QACA;UACAsL,OAAA,CAAAxR,QAAA;QACA;MACA;IACA;IACAyR,UAAA,WAAAA,WAAA;MACA,UAAAxR,cAAA;MACAE,cAAA,CAAAoR,OAAA,iBAAAtR,cAAA;MACA,KAAAuF,cAAA;MACA,KAAAF,QAAA,CAAAoM,OAAA;IACA;IACAjP,kBAAA,WAAAA,mBAAAR,GAAA;MACA9B,cAAA,CAAAoR,OAAA,eAAAtP,GAAA;MACA,IAAAA,GAAA;QACA;QACA,SAAAvD,QAAA,SAAAA,QAAA,CAAAI,SAAA;UACA,KAAAJ,QAAA,CAAAI,SAAA,CAAAuN,OAAA,WAAAvE,KAAA;YACAA,KAAA,CAAAvI,SAAA;YACA;YACA,IAAAuI,KAAA,CAAA7I,KAAA,mBAAA6I,KAAA,CAAA7I,KAAA;cACA6I,KAAA,CAAA7I,KAAA,GAAAnB,MAAA,CAAAgK,KAAA,CAAA7I,KAAA;YACA;UACA;QACA;MACA;QACA;QACA,SAAAP,QAAA,SAAAA,QAAA,CAAAI,SAAA;UACA,KAAAJ,QAAA,CAAAI,SAAA,CAAAuN,OAAA,WAAAvE,KAAA;YACA;YACA,IAAAA,KAAA,CAAA7I,KAAA,mBAAA6I,KAAA,CAAA7I,KAAA;cACA6I,KAAA,CAAA7I,KAAA,GAAAnB,MAAA,CAAAgK,KAAA,CAAA7I,KAAA;YACA;UACA;QACA;MACA;MACA;MACA,KAAAmI,YAAA;IACA;IACA;IACAuK,YAAA,WAAAA,aAAAC,KAAA;MACA,KAAAA,KAAA;QACA,KAAApT,mBAAA,QAAAD,eAAA;MACA;QACA,KAAAC,mBAAA,QAAAD,eAAA,CAAAwL,MAAA,WAAA8H,IAAA;UACA,OAAAA,IAAA,CAAAC,UAAA,IAAAD,IAAA,CAAAC,UAAA,CAAAC,OAAA,CAAAH,KAAA,YACAA,KAAA,YAAAC,IAAA,CAAAC,UAAA,IAAAD,IAAA,CAAAC,UAAA,CAAAC,OAAA;QACA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAA3R,mBAAA;MACA,KAAAC,WAAA;MACA,KAAAC,YAAA;MACA,KAAAC,SAAA;IACA;IACA,cACAyR,cAAA,WAAAA,eAAA;MACA,KAAArQ,iBAAA;IACA;IACA,WACA0M,aAAA,WAAAA,cAAA;MACA,SAAAL,CAAA,MAAAA,CAAA,QAAAvP,QAAA,CAAAI,SAAA,CAAA0H,MAAA,EAAAyH,CAAA;QACA,IAAAnG,KAAA,QAAApJ,QAAA,CAAAI,SAAA,CAAAmP,CAAA;QACA,IAAAhP,KAAA,GAAA6I,KAAA,CAAA7I,KAAA;QACA,IAAAiT,WAAA,GAAAjE,CAAA;;QAEA;QACA,IAAAkE,QAAA,GAAAlT,KAAA,WAAAnB,MAAA,CAAAmB,KAAA;;QAEA;QACA,KAAAkT,QAAA,IAAAA,QAAA,CAAA3I,IAAA;UACA;YACA+E,KAAA;YACA3O,OAAA,+EAAAwM,MAAA,CAAA8F,WAAA;UACA;QACA;;QAEA;QACA,IAAAE,QAAA,GAAA9P,MAAA,CAAA6P,QAAA,CAAA3I,IAAA;QACA,IAAA6I,KAAA,CAAAD,QAAA,KAAAA,QAAA;UACA;YACA7D,KAAA;YACA3O,OAAA,+EAAAwM,MAAA,CAAA8F,WAAA,mIAAA9F,MAAA,CAAA+F,QAAA;UACA;QACA;MACA;MAEA;QACA5D,KAAA;QACA3O,OAAA;MACA;IACA;IACA,aACAwC,yBAAA,WAAAA,0BAAA;MAAA,IAAAkQ,OAAA;MAAA,WAAA5O,kBAAA,CAAA9F,OAAA,mBAAA+F,aAAA,CAAA/F,OAAA,IAAAgG,CAAA,UAAA2O,SAAA;QAAA,IAAAzO,QAAA,EAAA0O,GAAA;QAAA,WAAA7O,aAAA,CAAA/F,OAAA,IAAAoG,CAAA,WAAAyO,SAAA;UAAA,kBAAAA,SAAA,CAAAvO,CAAA;YAAA;cAAAuO,SAAA,CAAAtO,CAAA;cAAAsO,SAAA,CAAAvO,CAAA;cAAA,OAGA,IAAAwO,6BAAA;YAAA;cAAA5O,QAAA,GAAA2O,SAAA,CAAApO,CAAA;cAGA,IAAAP,QAAA,IAAAA,QAAA,CAAAQ,IAAA,YAAAR,QAAA,CAAA7F,IAAA,KAAAe,SAAA;gBAEAsT,OAAA,CAAAzQ,uBAAA,GAAAiC,QAAA,CAAA7F,IAAA;cACA;gBAEAqU,OAAA,CAAAzQ,uBAAA;cACA;cAAA4Q,SAAA,CAAAvO,CAAA;cAAA;YAAA;cAAAuO,SAAA,CAAAtO,CAAA;cAAAqO,GAAA,GAAAC,SAAA,CAAApO,CAAA;cAIAe,OAAA,CAAAC,KAAA,cAAAmN,GAAA;cACAF,OAAA,CAAAzQ,uBAAA;YAAA;cAAA,OAAA4Q,SAAA,CAAAzN,CAAA;UAAA;QAAA,GAAAuN,QAAA;MAAA;IAEA;IACAI,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAAC,GAAA,SAAAvS,WAAA,QAAAoP,OAAA;MACA;MACAmD,GAAA,GAAAA,GAAA,CAAAnD,OAAA;MACA;MACA,IAAA7F,KAAA,GAAAgJ,GAAA,CAAA/I,KAAA,OAAAlC,GAAA,WAAAoC,IAAA;QAAA,OAAA4I,OAAA,CAAAE,oBAAA,CAAA9I,IAAA;MAAA;MACAH,KAAA,QAAAkJ,uBAAA,CAAAlJ,KAAA;MACA,KAAAtJ,YAAA,GAAAsJ,KAAA,CAAAE,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA;MAAA,GAAAuB,IAAA;IACA;IACAyH,kBAAA,WAAAA,mBAAA;MACA,KAAAtU,QAAA,CAAAG,MAAA,QAAA0B,YAAA;MACA,KAAAF,mBAAA;MACA,KAAAqL,uBAAA,MAAAnL,YAAA;IACA;IACA0S,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAAL,GAAA,SAAApS,gBAAA,QAAAiP,OAAA;MACA;MACAmD,GAAA,GAAAA,GAAA,CAAAnD,OAAA;MACA;MACA,IAAA7F,KAAA,GAAAgJ,GAAA,CAAA/I,KAAA,OAAAlC,GAAA,WAAAoC,IAAA;QAAA,OAAAkJ,OAAA,CAAAJ,oBAAA,CAAA9I,IAAA;MAAA;MACAH,KAAA,QAAAkJ,uBAAA,CAAAlJ,KAAA;MACA,KAAAnJ,iBAAA,GAAAmJ,KAAA,CAAAE,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA;MAAA,GAAAuB,IAAA;IACA;IACA4H,uBAAA,WAAAA,wBAAA;MACA,KAAAzU,QAAA,CAAAG,MAAA,QAAA6B,iBAAA;MACA,KAAAL,mBAAA;MACA,KAAAqL,uBAAA,MAAAhL,iBAAA;IACA;IAEA0S,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAAxJ,KAAA,SAAAlJ,gBAAA,QAAAmJ,KAAA,OAAAlC,GAAA,WAAAoC,IAAA;QAAA,OAAAqJ,OAAA,CAAAP,oBAAA,CAAA9I,IAAA;MAAA;MACA,IAAAsJ,eAAA;MACAzJ,KAAA,GAAAA,KAAA,CAAAjC,GAAA,WAAAoC,IAAA;QACA,IAAAuJ,SAAA;QACA,IAAA3J,KAAA;QACA,QAAAA,KAAA,GAAA0J,eAAA,CAAAE,IAAA,CAAAxJ,IAAA;UACAuJ,SAAA,GAAA3J,KAAA;QACA;QACA,IAAA2J,SAAA;UACA,IAAA5G,GAAA,GAAA0G,OAAA,CAAAI,eAAA,CAAAF,SAAA;UACA,IAAAG,IAAA,GAAAH,SAAA;UACA,IAAAI,KAAA,GAAAJ,SAAA,CAAAxL,KAAA;UACA,IAAA6L,GAAA,GAAAD,KAAA,GAAAJ,SAAA,IAAA/M,MAAA;UACA,IAAAqN,MAAA,GAAA7J,IAAA,CAAAsB,KAAA,IAAAqI,KAAA;UACA,qBAAA/I,IAAA,CAAAiJ,MAAA;YACAA,MAAA;UACA;UACA,OAAAA,MAAA,GAAAlH,GAAA,GAAA+G,IAAA,GAAA1J,IAAA,CAAAsB,KAAA,CAAAsI,GAAA;QACA;QACA,OAAA5J,IAAA;MACA;MACA,KAAApJ,iBAAA,GAAAiJ,KAAA,CAAAE,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA;MAAA,GAAAuB,IAAA;IACA;IACAuI,uBAAA,WAAAA,wBAAA;MACA,KAAApV,QAAA,CAAAG,MAAA,QAAA+B,iBAAA;MACA,KAAAP,mBAAA;MACA,KAAAqL,uBAAA,MAAA9K,iBAAA;IACA;IACAmT,wBAAA,WAAAA,yBAAA;MACA;MACA,IAAAlB,GAAA,QAAA9R,kBAAA;;MAEA;MACA,IAAA8I,KAAA,GAAAgJ,GAAA,CAAA/I,KAAA,OAAAlC,GAAA,WAAAoC,IAAA;QAAA,OAAAA,IAAA,CAAAR,IAAA;MAAA,GAAAO,MAAA,CAAApM,OAAA;MACAkM,KAAA,QAAAkJ,uBAAA,CAAAlJ,KAAA;MACAgJ,GAAA,GAAAhJ,KAAA,CAAA0B,IAAA;;MAEA;MACAsH,GAAA,QAAAC,oBAAA,CAAAD,GAAA;;MAEA;MACA;MACAA,GAAA,GAAAA,GAAA,CAAAnD,OAAA;MACAmD,GAAA,GAAAA,GAAA,CAAAnD,OAAA;MACAmD,GAAA,GAAAA,GAAA,CAAAnD,OAAA;MACAmD,GAAA,GAAAA,GAAA,CAAAnD,OAAA;;MAEA;MACAmD,GAAA,GAAAA,GAAA,CAAAnD,OAAA;;MAEA;MACAmD,GAAA,GAAAA,GAAA,CAAAnD,OAAA;MAEA,KAAA1O,mBAAA,GAAA6R,GAAA,CAAA/I,KAAA,OAAAlC,GAAA,WAAAoC,IAAA;QAAA,OAAAA,IAAA,CAAAR,IAAA;MAAA,GAAAO,MAAA,CAAApM,OAAA,EAAA4N,IAAA;IACA;IACAyI,yBAAA,WAAAA,0BAAA;MACA,KAAAtV,QAAA,CAAAG,MAAA,QAAAmC,mBAAA;MACA,KAAAX,mBAAA;MACA,KAAAqL,uBAAA,MAAA1K,mBAAA;IACA;IACAiT,sBAAA,WAAAA,uBAAA;MACA;MACA,IAAA1K,KAAA,QAAAtI,gBAAA;;MAEA;MACAsI,KAAA,QAAAuJ,oBAAA,CAAAvJ,KAAA;;MAEA;MACA;MACA,IAAAG,aAAA;MACA,IAAAgD,OAAA,GAAAnD,KAAA,CAAAK,KAAA,CAAAF,aAAA;;MAEA;MACA,IAAAwK,YAAA;MACA,IAAAC,gBAAA;MAEAzH,OAAA,CAAAL,OAAA,WAAAM,GAAA,EAAA5E,KAAA;QACA,IAAAvB,MAAA,GAAAmG,GAAA,CAAAnG,MAAA;QACA,KAAA0N,YAAA,CAAA1N,MAAA;UACA0N,YAAA,CAAA1N,MAAA;UACA2N,gBAAA,CAAA3N,MAAA,IAAAuB,KAAA;QACA;QACAmM,YAAA,CAAA1N,MAAA,EAAA1B,IAAA,CAAA6H,GAAA;MACA;;MAEA;MACA,IAAAyH,aAAA,GAAApW,MAAA,CAAAqW,IAAA,CAAAH,YAAA,EAAAI,IAAA,WAAAtP,CAAA,EAAA6I,CAAA;QAAA,OAAAsG,gBAAA,CAAAnP,CAAA,IAAAmP,gBAAA,CAAAtG,CAAA;MAAA;MACA,IAAA0G,WAAA;MAEAH,aAAA,CAAA/H,OAAA,WAAA7F,MAAA;QACA,IAAAgO,IAAA,GAAAN,YAAA,CAAA1N,MAAA;QACA,IAAAgO,IAAA,CAAAhO,MAAA;UACA;UACA,IAAAwD,IAAA,GAAAwK,IAAA,CAAAjJ,IAAA;UACAgJ,WAAA,CAAAzP,IAAA,CAAAkF,IAAA;QACA;MACA;MAEA,KAAA9I,iBAAA,GAAAqT,WAAA,CAAAhJ,IAAA;IACA;IACAkJ,uBAAA,WAAAA,wBAAA;MACA,KAAA/V,QAAA,CAAAG,MAAA,QAAAqC,iBAAA;MACA,KAAAb,mBAAA;MACA,KAAAqL,uBAAA,MAAAxK,iBAAA;IACA;IACAwT,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,OAAA;MACA;MACA;MACA,IAAApL,KAAA,QAAApI,iBAAA;MACA,IAAA0I,KAAA,GAAAN,KAAA,CAAAO,KAAA,OAAAC,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAR,IAAA;MAAA;;MAEA;MACA,IAAAsC,MAAA;MACA,IAAA8I,UAAA;;MAEA/K,KAAA,CAAAwC,OAAA,WAAArC,IAAA;QACA,IAAAQ,WAAA,GAAAR,IAAA,CAAAR,IAAA;QACA,KAAAgB,WAAA;;QAEA;QACA,IAAAW,KAAA,GAAAX,WAAA,CAAAV,KAAA,QAAAC,MAAA,WAAA8K,IAAA;UAAA,OAAAA,IAAA,CAAArL,IAAA;QAAA;;QAEA;QACA,IAAA2B,KAAA,CAAA3E,MAAA;UACAmO,OAAA,CAAAG,uBAAA,CAAA3J,KAAA,KAAAW,MAAA,EAAA8I,UAAA;QACA;UACA;UACAzJ,KAAA,CAAAkB,OAAA,WAAAwI,IAAA;YACAF,OAAA,CAAAG,uBAAA,CAAAD,IAAA,EAAA/I,MAAA,EAAA8I,UAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAL,WAAA;MACAK,UAAA,CAAAvI,OAAA,WAAA0I,QAAA;QACA,IAAAjN,KAAA,GAAAgE,MAAA,CAAAiJ,QAAA;QACA,IAAAjN,KAAA,IAAAA,KAAA,CAAA4E,OAAA,CAAAlG,MAAA;UACA;UACA,IAAAwD,IAAA,GAAAlC,KAAA,CAAA4E,OAAA,CAAAnB,IAAA,cAAAzD,KAAA,CAAAkN,MAAA;UACAT,WAAA,CAAAzP,IAAA,CAAAkF,IAAA;QACA;MACA;MAEA,KAAA5I,kBAAA,GAAAmT,WAAA,CAAAhJ,IAAA;IACA;IAEA;IACAuJ,uBAAA,WAAAA,wBAAAjD,IAAA,EAAA/F,MAAA,EAAA8I,UAAA;MACA,IAAAK,WAAA,GAAApD,IAAA,CAAArI,IAAA;MACA,KAAAyL,WAAA;;MAEA;MACA,IAAAC,aAAA,GAAAD,WAAA,CAAAE,WAAA;MACA,IAAAD,aAAA;;MAEA,IAAAE,MAAA,GAAAH,WAAA,CAAAI,SAAA,IAAAH,aAAA,EAAA1L,IAAA;MACA,IAAAwL,MAAA,GAAAC,WAAA,CAAAI,SAAA,CAAAH,aAAA,MAAA1L,IAAA;MAEA,KAAA4L,MAAA,KAAAJ,MAAA;;MAEA;MACA,IAAAM,YAAA,GAAAF,MAAA,CAAA5O,MAAA;MACA,IAAAuO,QAAA,MAAA3I,MAAA,CAAA4I,MAAA,OAAA5I,MAAA,CAAAkJ,YAAA;;MAEA,KAAAxJ,MAAA,CAAAiJ,QAAA;QACAjJ,MAAA,CAAAiJ,QAAA;UACAC,MAAA,EAAAA,MAAA;UACAxO,MAAA,EAAA8O,YAAA;UACA5I,OAAA;QACA;QACAkI,UAAA,CAAA9P,IAAA,CAAAiQ,QAAA;MACA;MACAjJ,MAAA,CAAAiJ,QAAA,EAAArI,OAAA,CAAA5H,IAAA,CAAAsQ,MAAA;IACA;IACAG,wBAAA,WAAAA,yBAAA;MACA,KAAA7W,QAAA,CAAAG,MAAA,QAAAuC,kBAAA;MACA,KAAAf,mBAAA;MACA,KAAAqL,uBAAA,MAAAtK,kBAAA;IACA;IACAoU,uBAAA,WAAAA,wBAAA;MACA;MACA,IAAAjM,KAAA,QAAAlI,iBAAA;;MAEA;MACA;MACA;MACA;MACA;MACA,IAAAoU,MAAA,GAAAlM,KAAA,CAAAmG,OAAA;;MAEA;MACA,KAAApO,kBAAA,GAAAmU,MAAA,CAAA3L,KAAA,OACAlC,GAAA,WAAAoC,IAAA;QAAA,OAAAA,IAAA,CAAAR,IAAA;MAAA,GACAO,MAAA,WAAAC,IAAA,EAAAjC,KAAA,EAAA2N,GAAA;QACA;QACA,OAAA1L,IAAA,IAAAjC,KAAA,QAAA2N,GAAA,CAAA3N,KAAA;MACA,GACAwD,IAAA,OACAmE,OAAA;IACA;IACAiG,wBAAA,WAAAA,yBAAA;MACA,KAAAjX,QAAA,CAAAG,MAAA,QAAAyC,kBAAA;MACA,KAAAjB,mBAAA;MACA,KAAAqL,uBAAA,MAAApK,kBAAA;IACA;IACAsU,2BAAA,WAAAA,4BAAA;MAAA,IAAAC,OAAA;MACA;MACA,IAAAhD,GAAA,SAAAhS,qBAAA,QAAA6O,OAAA;MACA;MACA,IAAA7F,KAAA,GAAAgJ,GAAA,CAAA/I,KAAA,OAAAlC,GAAA,WAAAoC,IAAA;QAAA,OAAA6L,OAAA,CAAA/C,oBAAA,CAAA9I,IAAA;MAAA;MACAH,KAAA,QAAAkJ,uBAAA,CAAAlJ,KAAA;MACA,KAAA/I,sBAAA,GAAA+I,KAAA,CAAAE,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA;MAAA,GAAAuB,IAAA;IACA;IACAuK,4BAAA,WAAAA,6BAAA;MACA,KAAApX,QAAA,CAAAG,MAAA,QAAAiC,sBAAA;MACA,KAAAT,mBAAA;MACA,KAAAqL,uBAAA,MAAA5K,sBAAA;IACA;IACA;IACAgS,oBAAA,WAAAA,qBAAAiD,GAAA;MAAA,IAAAC,OAAA;MACA;MACA;MACA,IAAAC,oBAAA;MAEA,OAAAF,GAAA,CAAArG,OAAA,CAAAuG,oBAAA,YAAArM,KAAA;QACA;QACA,IAAAA,KAAA,CAAApD,MAAA,oBAAAoE,IAAA,CAAAhB,KAAA;UACA,IAAAhC,GAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;YAAA;UAAA;UACA,OAAAA,GAAA,CAAAgC,KAAA,KAAAA,KAAA;QACA;QACA;QACA,OAAAoM,OAAA,CAAAvC,eAAA,CAAA7J,KAAA;MACA;IACA;IACA6J,eAAA,WAAAA,gBAAAyC,OAAA;MACA,KAAAA,OAAA;;MAEA;MACA,YAAAtL,IAAA,CAAAsL,OAAA;QACA,OAAAvI,QAAA,CAAAuI,OAAA;MACA;;MAEA;MACA,IAAAH,GAAA,GAAAG,OAAA,CAAAxG,OAAA;;MAEA;MACA,IAAAyG,MAAA;QACA;QAAA;QAAA;QAAA;QAAA;QAAA;QACA;QAAA;QAAA;QAAA;QAAA;MACA;;MAEA;MACA,IAAAC,OAAA;QAAA;QAAA;QAAA;QAAA;MAAA;MAEA,IAAAvK,MAAA;MACA,IAAAwK,IAAA;MACA,IAAAC,MAAA;MAEA,SAAArI,CAAA,MAAAA,CAAA,GAAA8H,GAAA,CAAAvP,MAAA,EAAAyH,CAAA;QACA,IAAAsI,IAAA,GAAAR,GAAA,CAAA9H,CAAA;QAEA,IAAAkI,MAAA,CAAAK,cAAA,CAAAD,IAAA;UACA;UACAF,IAAA,GAAAF,MAAA,CAAAI,IAAA;UACAD,MAAA;QACA,WAAAF,OAAA,CAAAI,cAAA,CAAAD,IAAA;UACA;UACA,IAAA7C,IAAA,GAAA0C,OAAA,CAAAG,IAAA;UAEA,IAAAA,IAAA,aAAAD,MAAA;YACA;YACAD,IAAA;UACA;UAEA,IAAA3C,IAAA;YACA;YACA7H,MAAA,IAAAA,MAAA,GAAAwK,IAAA,IAAA3C,IAAA;YACA2C,IAAA;UACA,WAAA3C,IAAA;YACA;YACA7H,MAAA,IAAAwK,IAAA,GAAA3C,IAAA;YACA2C,IAAA;UACA;YACA;YACAxK,MAAA,IAAAwK,IAAA,GAAA3C,IAAA;YACA2C,IAAA;UACA;UACAC,MAAA;QACA;MACA;;MAEA;MACAzK,MAAA,IAAAwK,IAAA;MAEA,OAAAxK,MAAA;IACA;IACAkH,uBAAA,WAAAA,wBAAAlJ,KAAA;MAAA,IAAA4M,OAAA;MACA,WAAA5M,KAAA,eAAAA,KAAA,GAAAA,KAAA,CAAAC,KAAA;MACA;MACA,IAAAwJ,eAAA;MACA,OAAAzJ,KAAA,CAAAjC,GAAA,WAAAoC,IAAA;QACA;QACA,IAAAuJ,SAAA;QACA,IAAA3J,KAAA;QACA;QACA0J,eAAA,CAAAoD,SAAA;QACA,QAAA9M,KAAA,GAAA0J,eAAA,CAAAE,IAAA,CAAAxJ,IAAA;UACA;UACA,wBAAAY,IAAA,CAAAhB,KAAA;YACA2J,SAAA,GAAA3J,KAAA;UACA;QACA;QACA,IAAA2J,SAAA;UACA,IAAA5G,GAAA,GAAA8J,OAAA,CAAAhD,eAAA,CAAAF,SAAA;UACA,IAAAG,IAAA,GAAAH,SAAA;UACA;UACA,IAAAI,KAAA,GAAAJ,SAAA,CAAAxL,KAAA;UACA,IAAA6L,GAAA,GAAAD,KAAA,GAAAJ,SAAA,IAAA/M,MAAA;UACA,IAAAqN,MAAA,GAAA7J,IAAA,CAAAsB,KAAA,IAAAqI,KAAA;;UAEA;UACA,IAAAE,MAAA,oBAAAjJ,IAAA,CAAAiJ,MAAA;YACAA,MAAA;UACA;UAEA,OAAAA,MAAA,GAAAlH,GAAA,GAAA+G,IAAA,GAAA1J,IAAA,CAAAsB,KAAA,CAAAsI,GAAA;QACA;QACA,OAAA5J,IAAA;MACA;IACA;IACA;IACAzH,gBAAA,WAAAA,iBAAA;MAAA,IAAAoU,OAAA;MACA;MACA,SAAA5Y,GAAA,SAAAA,GAAA,CAAAwD,YAAA;QAEA,KAAAA,YAAA,QAAAxD,GAAA,CAAAwD,YAAA;QACA,KAAAI,oBAAA,QAAA5D,GAAA,CAAAwD,YAAA;QACA;QACA,IAAAqV,0BAAA,IAAAhS,IAAA,WAAAmG,GAAA;UACA4L,OAAA,CAAAnV,eAAA,GAAAuJ,GAAA,GAAAzI,MAAA,CAAAyI,GAAA;QACA,GAAAhG,KAAA;UACA4R,OAAA,CAAAnV,eAAA;QACA;QACA;MACA;;MAEA;;MAEA,IAAAoV,0BAAA,IAAAhS,IAAA,WAAAmG,GAAA;QACA,IAAAA,GAAA;UACA4L,OAAA,CAAAnV,eAAA;UACAmV,OAAA,CAAApV,YAAA;QACA;UACAoV,OAAA,CAAAnV,eAAA,GAAAc,MAAA,CAAAyI,GAAA;UACA4L,OAAA,CAAApV,YAAA,GAAAe,MAAA,CAAAyI,GAAA;QACA;MAEA,GAAAhG,KAAA;QACA4R,OAAA,CAAAnV,eAAA;QACAmV,OAAA,CAAApV,YAAA;MACA;IACA;IACA;IACAsV,oBAAA,WAAAA,qBAAA;MACA,IAAAC,IAAA,GAAAxU,MAAA,MAAAd,eAAA;MACA,SAAAD,YAAA,GAAAuV,IAAA;QACA,KAAAvV,YAAA,GAAAuV,IAAA;MACA;MACA;IACA;IACA;IACAC,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,OAAA;MACA,KAAAvV,eAAA;MACA,IAAAwV,4BAAA,IAAArS,IAAA,WAAAd,QAAA;QACA,IAAAA,QAAA,CAAAQ,IAAA;UACA0S,OAAA,CAAAzV,YAAA,GAAAuC,QAAA,CAAAvC,YAAA;UACAyV,OAAA,CAAAxV,eAAA,GAAAsC,QAAA,CAAAvC,YAAA;UACAyV,OAAA,CAAA1R,QAAA,CAAAoM,OAAA,0CAAAtF,MAAA,CAAAtI,QAAA,CAAAvC,YAAA;QACA;UACAyV,OAAA,CAAA1R,QAAA,CAAAD,KAAA,eAAAvB,QAAA,CAAAW,GAAA;QACA;MACA,GAAAM,KAAA,WAAAM,KAAA;QACAD,OAAA,CAAAC,KAAA,aAAAA,KAAA;QACA2R,OAAA,CAAA1R,QAAA,CAAAD,KAAA;MACA,GAAAgE,OAAA;QACA2N,OAAA,CAAAvV,eAAA;MACA;IACA;IACA;IACAuB,kBAAA,WAAAA,mBAAAH,SAAA,EAAAC,SAAA;MAEA,IAAAD,SAAA,UAAAnB,cAAA;QACA;QACA,KAAAH,YAAA,QAAAI,oBAAA;QACA,KAAA2D,QAAA,CAAA4R,IAAA,gDAAA9K,MAAA,MAAAzK,oBAAA;MACA;QACA;QACA,KAAAwV,wBAAA;MACA;IACA;IACA;IACAA,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,OAAA;MACA,IAAAH,4BAAA,IAAArS,IAAA,WAAAd,QAAA;QACA,IAAAA,QAAA,CAAAQ,IAAA;UACA8S,OAAA,CAAA7V,YAAA,GAAAuC,QAAA,CAAAvC,YAAA;UACA6V,OAAA,CAAA5V,eAAA,GAAAsC,QAAA,CAAAvC,YAAA;UACA6V,OAAA,CAAA9R,QAAA,CAAA4R,IAAA,oFAAA9K,MAAA,CAAAtI,QAAA,CAAAvC,YAAA;QACA;UACA6V,OAAA,CAAA9R,QAAA,CAAAD,KAAA,iBAAAvB,QAAA,CAAAW,GAAA;QACA;MACA,GAAAM,KAAA,WAAAM,KAAA;QACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA;QACA+R,OAAA,CAAA9R,QAAA,CAAAD,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}