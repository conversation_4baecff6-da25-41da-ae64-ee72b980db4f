// 用途：本文件用于"两码对子、两码组合"相关玩法的正则表达式与处理逻辑
// 玩法包括：两码对子、两码组合
import { BASE_PATTERNS, cleanNumberString, parseMoney, parseLotteryType, METHOD_ID, LOTTERY_TYPE } from './basePatterns';

// 两码组合和两码对子玩法的正则匹配
export const TWO_CODE_PATTERNS = {
  // 两码组合
  LIANGMA_ZUHE: {
    // 支持：12-10、12组合10、12——10、12~10、12@10、12=10、12 吊 10、12赶10、12%10、12 10 等
    pattern: /^(\d{2})(?:组合|组)[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]*(\d+)?$/,
    handler: (match, line) => {
      if (match[1].length !== 2) return null;
      const num = match[1];
      const money = match[2];
      // 只允许不同数字的两码组合
      if (num[0] === num[1]) {
        return null;
      }
      return [{
        methodId: METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合
        betNumbers: num,
        money: money
      }];
    }
  },

  // 两码对子
  LIANGMA_DUIZI: {
    // 匹配：12对子10 - 必须包含"对子"关键字
    pattern: /^(\d{2})[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]*对子[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]*(\d+)?(元|米|块)?$/,
    handler: (match, line) => {
      if (match[1].length !== 2) return null;
      const num = match[1];
      const money = match[2];
      // 只允许对子（两个数字相同）
      if (num[0] !== num[1]) {
        return null;
      }
      return [{
        methodId: METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子
        betNumbers: num,
        money: money
      }];
    }
  },

  // 两码对子
  LIANGMA_DUIZI_ALL: {
    // 匹配：12对子10 - 必须包含"对子"关键字
    pattern: /^(\d{2})[\-—~～@#￥%…&!、\\/*,，一－.＝=·\s]*对子[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]*(\d+)?(元|米|块)?$/,
    handler: (match, line) => {
      if (match[1].length !== 2) return null;
      const num = match[1];
      const money = match[2];
      const unit = match[3];
      // 只允许对子（两个数字相同）
      if (num[0] !== num[1]) {
        return null;
      }
      const result = {
        methodId: METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子
        betNumbers: num,
        money: money
      };
      if (unit) result.unit = unit;
      return [result];
    }
  },

  // 两码组三（支持单组和多组）
  LIANGMA_ZUSAN: {
    // 匹配格式1：12组三50 (单组)
    // 匹配格式2：12 23组三50 (多组)
    pattern: /^(.*?)组三[\s\-—~～@#￥%…&!、\\/*,，一－.＝=·]*(\d+)(元|块|米)?[\s\-—~～@#￥%…&!、\\/*,，一－.＝=·]*$/,
    handler: (match, line) => {
      // 提取号码部分和金额
      const numbersStr = match[1].trim();
      const money = match[2];
      const unit = match[3];

      // 使用正则提取所有两位数字
      const numberMatches = numbersStr.match(/\d{2}/g);
      if (!numberMatches || numberMatches.length === 0) return null;

      // 构建结果（前端显示用简单格式，后端存储时会转换为JSON格式）
      const result = {
        methodId: METHOD_ID.LIANGMA_ZUSAN,  // 70 两码组三
        betNumbers: numberMatches.join(','),  // 简单格式：如 "12,23,34"
        money: money
      };

      if (unit) result.unit = unit;
      return [result];
    }
  },

  // 两码组合带对子
  LIANGMA_ZUHE_DUIZI: {
    // 匹配：25-10+5 (组合10元对子5元)
    pattern: /^(\d{2})(?:[^0-9]+\d{2})*-(\d+)\+(\d+)$/,
    handler: (match, line) => {
      const numbers = line.split(BASE_PATTERNS.SEPARATORS).filter(Boolean)
        .filter(num => num.length === 2);
      const allNumbers = line.split(BASE_PATTERNS.SEPARATORS).filter(Boolean);
      if (!allNumbers.length || allNumbers.some(n => n.length !== 2)) return null;
      const zuheMoney = match[2];
      const duiziMoney = match[3];

      return [
        {
          methodId: 3, // 两码组合
          betNumbers: numbers.join(','),
          money: zuheMoney
        },
        {
          methodId: 4, // 两码对子
          betNumbers: numbers.join(','),
          money: duiziMoney
        }
      ];
    }
  },

  // 多行两码组合（支持中文金额表达）
  MULTI_LIANGMA: {
    // 匹配：12,34,56各10、各一百防二十
    pattern: /^(\d{2}(?:[^0-9]+\d{2})*)(?:各([一二三四五六七八九十百千万\d]+))?(?:防([一二三四五六七八九十百千万\d]+))?$/,
    handler: (match, line) => {
      const { chineseToNumber } = require('./basePatterns');
      const numbers = line.split(BASE_PATTERNS.SEPARATORS).filter(Boolean)
        .filter(num => num.length === 2);
      if (!numbers.length) return null;
      const money = match[2] ? chineseToNumber(match[2]) : '';
      const groupMoney = match[3] ? chineseToNumber(match[3]) : '';
      const groups = [];
      if (money) {
        groups.push({
          methodId: METHOD_ID.LIANGMA_ZUHE,
          betNumbers: numbers.join(','),
          money: money,
        });
      }
      if (groupMoney) {
        groups.push({
          methodId: METHOD_ID.LIANGMA_DUIZI,
          betNumbers: numbers[numbers.length - 1],
          money: groupMoney,
        });
      }
      console.log('两码 handler 返回:', groups);
      return groups;
    }
  },

  // 两码组合+对子多号码+双金额，任意分隔符
  // LIANGMA_ZUHE_DUIZI_MULTI_MONEY_SUPER: {
  //   // 匹配：12 34 5+1、12/34/5+1、12-34-5+1等
  //   pattern: /^([\s\S]+)$/m,
  //   handler: (match, line) => {
  //     const { SUPER_SEPARATORS } = require('./basePatterns');
  //     const lines = line.split(/[\r\n]+/).map(l => l.trim()).filter(Boolean);
  //     // 按玩法和金额分组合并，支持所有超级分隔符
  //     const zhixuanMap = new Map();
  //     const zuxuanMap = new Map();
  //     for (const l of lines) {
  //       // 匹配：3位号码+超级分隔符+金额1+超级分隔符+金额2
  //       const m = l.match(new RegExp(`^(\\d{2})${SUPER_SEPARATORS.source}(\\d+)\+(\\d+)$`));
  //       if (m) {
  //         const num = m[1];
  //         const zhixuanMoney = m[2];
  //         const zuxuanMoney = m[3];
  //         // 合并直选
  //         const zKey = zhixuanMoney;
  //         if (!zhixuanMap.has(zKey)) zhixuanMap.set(zKey, []);
  //         zhixuanMap.get(zKey).push(num);
  //         // 合并组选
  //         const zuKey = zuxuanMoney;
  //         if (!zuxuanMap.has(zuKey)) zuxuanMap.set(zuKey, []);
  //         zuxuanMap.get(zuKey).push(num);
  //       }
  //     }
  //     const result = [];
  //     for (const [money, nums] of zhixuanMap.entries()) {
  //       result.push({
  //         methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,
  //         betNumbers: nums.join(','),
  //         money,
  //       });
  //     }
  //     for (const [money, nums] of zuxuanMap.entries()) {
  //       result.push({
  //         methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,
  //         betNumbers: nums.join(','),
  //         money,
  //       });
  //     }
  //     return result.length ? result : null;
  //   }
  // },

  // 两码组合（组字为组合，非对子）
  LIANGMA_ZUHE_ZU_SUPER: {
    // 匹配：12组30（只要有组字且不是对子）
    pattern: /^(\d{2})组(\d+)$/,
    handler: (match, line) => {
      const numbers = match[1];
      if (numbers.length !== 2) return null;
      const money = match[2];
      return [{
        methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合
        betNumbers: numbers,
        money: money,
      }];
    }
  },

  // 两码组合多号码+金额，任意分隔符
  LIANGMA_ZUHE_MULTI_SUPER: {
    // 匹配：12-34赶50、12-34吊50、12/34/50、12-34赶50元、12-34赶50块、12-34赶50米等
    pattern: /^(\d{2}(?:[\-~～@#￥%…&!、\\/*一－。,，.＝=· \t+吊赶打各]+\d{2})*)[\-~～@#￥%…&!、\\/*一－。,，.＝=· \t+吊赶打各]+(\d+)(元|块|米)?$/,
    handler: (match, line) => {
      const { SUPER_SEPARATORS } = require('./basePatterns');
      const allNumbers = match[1].split(SUPER_SEPARATORS).filter(Boolean);
      if (!allNumbers.length || allNumbers.some(n => n.length !== 2)) return null;
      const money = match[2];
      // 分组
      const zuhe = [];
      const duizi = [];
      allNumbers.forEach(num => {
        if (num[0] === num[1]) {
          duizi.push(num);
        } else {
          zuhe.push(num);
        }
      });
      const result = [];
      if (zuhe.length) {
        result.push({
          methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合
          betNumbers: zuhe.join(','),
          money: money,
        });
      }
      if (duizi.length) {
        result.push({
          methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子
          betNumbers: duizi.join(','),
          money: money,
        });
      }
      return result.length ? result : null;
    }
  },

  // 两码对子（对子关键字）
  LIANGMA_DUIZI_DUI_SUPER: {
    // 匹配：12对子30
    pattern: /^(\d{2})对子(\d+)$/,
    handler: (match, line) => {
      const numbers = match[1];
      if (numbers.length !== 2) return null;
      const money = match[2];
      return [{
        methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子
        betNumbers: numbers,
        money: money,
      }];
    }
  },

  // 新增：单号码+金额（支持所有分隔符）
  LIANGMA_SINGLE_WITH_MONEY_SUPER: {
    // 匹配：45,20 或 45，20 或 45 20 或 45、20 或 45+20 等任意分隔符
    pattern: /^(\d{2})[\-~～@#￥%…&!、\\/*,，一－。.＝=·\s+吊赶打各]+(\d{1,4})$/,
    handler: (match, line) => {
      const num = match[1];
      if (num.length !== 2) return null;
      const money = match[2];
      // 判断玩法
      if (num[0] === num[1]) {
        return [{
          methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子
          betNumbers: num,
          money: money,
        }];
      } else {
        return [{
          methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合
          betNumbers: num,
          money: money,
        }];
      }
    }
  },

  // 新增：多号码/金额（如45，28，18，52/20），兼容所有分隔符
  LIANGMA_MULTI_WITH_MONEY_SLASH_SUPER: {
    // 匹配：45，28，18，52/20 或 45 28 18 52/20
    pattern: /^(\d{2}(?:[\-~～@#￥%…&!、\\/*,，.＝=一－。·\s+吊赶打各]+\d{2})*)\/(\d{1,4})$/,
    handler: (match, line) => {
      const { SUPER_SEPARATORS } = require('./basePatterns');
      const allNumbers = match[1].split(SUPER_SEPARATORS).filter(Boolean);
      if (!allNumbers.length || allNumbers.some(n => n.length !== 2)) return null;
      const money = match[2];
      // 分组
      const zuhe = [];
      const duizi = [];
      allNumbers.forEach(num => {
        if (num[0] === num[1]) {
          duizi.push(num);
        } else {
          zuhe.push(num);
        }
      });
      const result = [];
      if (zuhe.length) {
        result.push({
          methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合
          betNumbers: zuhe.join(','),
          money: money,
        });
      }
      if (duizi.length) {
        result.push({
          methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子
          betNumbers: duizi.join(','),
          money: money,
        });
      }
      return result.length ? result : null;
    }
  },

  // 新增：多号码/双金额（如45，28，18，52/20-5），兼容所有分隔符
  LIANGMA_MULTI_WITH_DOUBLE_MONEY_SLASH_SUPER: {
    // 匹配：45，28，18，52/20-5 或 45 28 18 52/20-5
    pattern: /^(\d{2}(?:[\-~～@#￥%…&!、\\/*,，一－。.＝=·\s+吊赶打各]+\d{2})*)\/(\d+)-(\d+)$/,
    handler: (match, line) => {
      const { SUPER_SEPARATORS } = require('./basePatterns');
      const allNumbers = match[1].split(SUPER_SEPARATORS).filter(Boolean);
      if (!allNumbers.length || allNumbers.some(n => n.length !== 2)) return null;
      const numbers = allNumbers;
      const zuheMoney = match[2];
      const duiziMoney = match[3];
      return [
        {
          methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合
          betNumbers: numbers.join(','),
          money: zuheMoney,
        },
        {
          methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子
          betNumbers: numbers.join(','),
          money: duiziMoney,
        }
      ];
    }
  },

  // 新增：两码组合带对子（如45-20防5）
  LIANGMA_ZUHE_FANG: {
    // 匹配：45-20防5
    pattern: /^(\d{2})-(\d+)防(\d+)$/,
    handler: (match, line) => {
      const numbers = match[1];
      if (numbers.length !== 2) return null;
      const zuheMoney = match[2];
      const duiziMoney = match[3];
      return [
        {
          methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合
          betNumbers: numbers,
          money: zuheMoney,
        },
        {
          methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子
          betNumbers: numbers,
          money: duiziMoney,
        }
      ];
    }
  },
  // 新增：多号码+组合+对子金额（任意分隔符，金额用/分隔，如 .../10+5）
  LIANGMA_MULTI_ZUHE_DUIZI_SLASH_SUPER: {
    // 匹配：大量号码/10+5，号码间任意分隔符
    pattern: /^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、＝一－。=·吊赶打各]+)(?:\/|各|\s|吊|赶|打)(\d+)\+(\d+)$/,
    handler: (match, line) => {
      const { SUPER_SEPARATORS } = require('./basePatterns');
      const allNumbers = match[1].split(SUPER_SEPARATORS).filter(Boolean);
      if (!allNumbers.length || allNumbers.some(n => n.length !== 2)) return null;
      const numbers = allNumbers;
      const zuheMoney = match[2];
      const duiziMoney = match[3];
      return [
        {
          methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合
          betNumbers: numbers.join(','),
          money: zuheMoney,
        },
        {
          methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子
          betNumbers: numbers.join(','),
          money: duiziMoney,
        }
      ];
    }
  },

  // 新增：多组号码+各X金额，支持超级分隔符
  LIANGMA_MULTI_EACH_SUPER: {
    // 匹配：12-34-56各100、12,34,56各100等
    pattern: /^(\d{2}(?:[\-~～@#￥%…&!、\\/*,，.＝=一－。·\s+吊赶打各]+\d{2})*)各(\d+)(元|块|米)?$/,
    handler: (match, line) => {
      const { SUPER_SEPARATORS } = require('./basePatterns');
      const allNumbers = match[1].split(SUPER_SEPARATORS).filter(Boolean);
      if (!allNumbers.length || allNumbers.some(n => n.length !== 2)) return null;
      const money = match[2];
      return [{
        methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,
        betNumbers: allNumbers.join(','),
        money: money,
      }];
    }
  },

  // 新增：多个两码号码（超级分隔符分割）+/金额1/金额2，组合+对子
  LIANGMA_MULTI_SUPER_SLASH_TWO_MONEY: {
    pattern: /^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!、一－。＝=·吊赶打各]+)\/(\d+)\/(\d+)$/,
    handler: (match, line, strict = true) => {
      const { SUPER_SEPARATORS } = require('./basePatterns');
      const numbers = match[1].split(SUPER_SEPARATORS).map(n => n.trim()).filter(n => n.length === 2);
      if (!numbers.length) return [];
      const zuheMoney = match[2];
      const duiziMoney = match[3];
      // 严格金额长度判断：两码金额不能是2位数字，否则防误命中
      if (strict && (/^\d{2}$/.test(zuheMoney) || /^\d{2}$/.test(duiziMoney))) return null;
      return [
        {
          methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 2 两码组合
          betNumbers: numbers.join(','),
          money: zuheMoney,
        },
        {
          methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 3 两码对子
          betNumbers: numbers.join(','),
          money: duiziMoney,
        }
      ];
    }
  },
  // 新增：批量两码组合/金额/元块米，支持如"08  /20 元"，将金额一致的号码合并为一个下注单，要求每行必须有单位且至少两行
  LIANGMA_MULTI_WITH_MONEY_UNIT_SUPER: {
    // 全包，所有内容都进 handler
    pattern: /^([\s\S]+)$/m,
    handler: (match, line) => {
      const lines = line.split(/\n+/).map(l => l.trim()).filter(Boolean);
      const groupMap = new Map();
      let validLineCount = 0;
      for (const l of lines) {
        // 金额和单位之间允许有超级分隔符
        const m = l.match(/(\d{2})[\s,，.。\-—~～@#￥%…&!、\\/*]*\/?[\s,，.。\-—~～@#￥%…&!、\\/*]*(\d+)[\s,，.。\-—~～@#￥%…&!、\\/*]*(元|块|米)/);
        if (m) {
          validLineCount++;
          const num = m[1];
          const money = m[2];
          if (!groupMap.has(money)) groupMap.set(money, []);
          groupMap.get(money).push(num);
        }
      }
      // 必须两行及以上有效行
      if (validLineCount < 2) return null;
      if (!groupMap.size) return null;
      return Array.from(groupMap.entries()).map(([money, nums]) => ({
        methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,
        betNumbers: nums.join(','),
        money: money,
      }));
    }
  },
  // 新增：通用格式，支持"号码（任意超级分隔符）+金额（元/米/块/空）"，自动识别为两码组合
  LIANGMA_UNIVERSAL_SUPER: {
    pattern: /^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－—。、＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,，一－—。.＝=·吊赶打各]+([0-9]+)(元|米|块)?$/,
    handler: (match, line) => {
      const { SUPER_SEPARATORS, METHOD_ID } = require('./basePatterns');
      // 支持多行批量
      const lines = line.split(/\n|\r/).map(l => l.trim()).filter(Boolean);
      // 合并玩法+金额一致的号码
      const groupMap = new Map();
      lines.forEach(l => {
        const m = l.match(/^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－—。、＝=·吊赶打各]+)[\-~～@#￥%…&!、\\/*,，一－—。.＝=·吊赶打各]+([0-9]+)(元|米|块)?$/);
        if (!m) return;
        const numbers = m[1].split(SUPER_SEPARATORS).map(n => n.trim()).filter(n => n.length === 2);
        const money = m[2];
        numbers.forEach(num => {
          let methodId = null;
          if (num[0] === num[1]) {
            methodId = METHOD_ID.LIANGMA_DUIZI;
          } else {
            methodId = METHOD_ID.LIANGMA_ZUHE;
          }
          const key = methodId + '_' + money;
          if (!groupMap.has(key)) groupMap.set(key, []);
          groupMap.get(key).push(num);
        });
      });
      const result = [];
      for (const [key, nums] of groupMap.entries()) {
        const [methodId, money] = key.split('_');
        result.push({
          methodId: Number(methodId),
          betNumbers: nums.join(','),
          money: money,
        });
      }
      return result.length ? result : null;
    }
  },

  // 新增：12 34 各50+5
  LIANGMA_EACH_PLUS_SUPER: {
    // 匹配：12 34 各50+5
    pattern: /^([\d、,，.。\\/*\s\n\r\-~～@#￥%…&!一－。、＝=·吊赶打各]+)各([0-9]+)\+([0-9]+)$/,
    handler: (match, line) => {
      const { SUPER_SEPARATORS, METHOD_ID } = require('./basePatterns');
      const numbers = match[1].split(SUPER_SEPARATORS).map(n => n.trim()).filter(Boolean);
      const money1 = match[2];
      const money2 = match[3];
      // 单组号码
      if (numbers.length === 1 && numbers[0].length === 2) {
        return [
          {
            methodId: METHOD_ID.LIANGMA_ZUHE,
            betNumbers: numbers[0],
            money: money1,
          },
          {
            methodId: METHOD_ID.LIANGMA_DUIZI,
            betNumbers: numbers[0],
            money: money2,
          }
        ];
      }
      // 多组号码
      if (numbers.length >= 2 && numbers.every(n => n.length === 2)) {
        return [
          {
            methodId: METHOD_ID.LIANGMA_ZUHE,
            betNumbers: numbers.join(','),
            money: money1,
          },
          {
            methodId: METHOD_ID.LIANGMA_DUIZI,
            betNumbers: numbers.join(','),
            money: money2,
          }
        ];
      }
      return null;
    }
  },

  // 新增：纯数字+空格+金额整体金额格式
  LIANGMA_UNIVERSAL_SUPER_NEW: {
    pattern: /^([0-9]{2,})\s+(\d+)(元|块|米)?$/,
    handler: (match, line) => {
      const betNumbers = match[1];
      const money = match[2];
      // 只允许号码长度为2
      if (!betNumbers || betNumbers.length !== 2) return null;
      return [{
        methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,
        betNumbers,
        money,
      }];
    }
  },

  // 新增：支持"12345打90"格式，两码时为两码组合。
  LIANGMA_DA_MONEY: {
    pattern: /^([0-9]{2})打(\d+)(元|块|米)?$/,
    handler: (match, line) => {
      return [{
        methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,
        betNumbers: match[1],
        money: match[2],
      }];
    }
  },

  // 新增：多号码/金额/加号金额（如 45 28 18 52/1+1）
  LIANGMA_MULTI_WITH_MONEY_SLASH_PLUS: {
    // 匹配：45 28 18 52/1+1，兼容/后有无空格
    pattern: /^(\d{2}(?:[\s,，\-]+?\d{2})*)\/\s*(\d+)\+(\d+)$/,
    handler: (match, line) => {
      const numbers = match[1].split(/[\s,，\-]+/).filter(Boolean);
      const zuheMoney = match[2];
      const duiziMoney = match[3];
      return [
        {
          methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合
          betNumbers: numbers.join(','),
          money: zuheMoney,
        },
        {
          methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子
          betNumbers: numbers.join(','),
          money: duiziMoney,
        }
      ];
    }
  },

  // 新增：多行号码+汉字金额+加+汉字金额，金额一致才合并，否则分组
  LIANGMA_MULTI_LINE_MONEY_PLUS_CN: {
    // 适配如：23十加五\n45十加五
    pattern: /^((?:\d{2}[，,、]?[一二三四五六七八九十百千万\d]+加[一二三四五六七八九十百千万\d]+\s*\n?)+)$/m,
    handler: (match, line) => {
      const { METHOD_ID, chineseToNumber } = require('./basePatterns');

      const lines = line.split(/\n/).map(l => l.trim()).filter(Boolean);
      const groupMap = new Map();
      const unmatchedLines = [];
      for (const l of lines) {
        const m = l.match(/^(\d{2})[，,、]?([一二三四五六七八九十百千万\d]+)加([一二三四五六七八九十百千万\d]+)$/);
        if (m) {
          const num = m[1];
          const zuhe = chineseToNumber(m[2]);
          const duizi = chineseToNumber(m[3]);
          const moneyKey = `${zuhe}+${duizi}`;
          if (!groupMap.has(moneyKey)) groupMap.set(moneyKey, []);
          groupMap.get(moneyKey).push(num);
        } else {
          unmatchedLines.push(l);
        }
      }
      const result = [];
      for (const [moneyKey, numbers] of groupMap.entries()) {
        const [zuheMoney, duiziMoney] = moneyKey.split('+');
        result.push({
          methodId: METHOD_ID.LIANGMA_ZUHE,
          betNumbers: numbers.join(','),
          money: zuheMoney,
        });
        result.push({
          methodId: METHOD_ID.LIANGMA_DUIZI,
          betNumbers: numbers.join(','),
          money: duiziMoney,
        });
      }

      return result.length ? result : null;
    }
  },

  // 新增：对33/5，解析为两码对子，号码33，金额5元
  DUIZI_SLASH_MONEY: {
    pattern: /^对(\d{2})\/(\d+)$/,
    handler: (match, line) => {
      return [{
        methodId: METHOD_ID.LIANGMA_DUIZI,
        betNumbers: match[1],
        money: match[2],
      }];
    }
  },

  // 新增：69一29一个10元，两码组合10元
  ERMA_YI_YI_ONE_MONEY: {
    // 匹配：69一29一个10元
    pattern: /^(\d{2})一(\d{2})一个(\d+)(元|块|米)?$/,
    handler: (match, line) => {
      const num1 = match[1];
      const num2 = match[2];
      const money = match[3];
      return [{
        methodId: require('./basePatterns').METHOD_ID.ERMA_COMBO, // 两码组合
        betNumbers: num1 + ',' + num2,
        money,
      }];
    }
  },
  //  // 修正：排...各X元，批量组选（去掉"组的"两个字）
  //  LIANGMA_PAI_EACH_MONEY_ZUXUAN: {
  //   // 匹配：排07，28，08，...各5元 或 排27，02，38，...组的各5元
  //   pattern: /^([\d，,、\s]+)(组的|组)?各(\d+)(元|块|米)?[，,./\*\-\=。·、\s]*$/,
  //   handler: (match, line) => {
  //     const numbers = match[1].split(/[，,、\s]+/).map(n => n.trim()).filter(n => n.length === 2);
  //     const money = match[3];
  //     if (!numbers.length) return null;
  //     return [{
  //       methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUXUAN, //  2码组选
  //       betNumbers: numbers.join(','),
  //       money,
  //     }];
  //   }
  // },
  //  // 修正：排...各X元，批量组选（去掉"组的"两个字）
  //  LIANGMA_PAI_EACH_MONEY_ZHIXUAN: {
  //   // 匹配：排02，08，08，...各5元 或 排07，02，03，...组的各5元
  //   pattern: /^([\d，,、\s]+)(直的|直|值的|值)?各(\d+)(元|块|米)?[，,./\*\-\=。·、\s]*$/,
  //   handler: (match, line) => {
  //     const numbers = match[1].split(/[，,、\s]+/).map(n => n.trim()).filter(n => n.length === 2);
  //     const money = match[3];
  //     if (!numbers.length) return null;
  //     return [{
  //       methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZHIXUAN, //  2码组选
  //       betNumbers: numbers.join(','),
  //       money,
  //     }];
  //   }
  // },

  LIANGMA_MULTI_SINGLE_WITH_MONEY_FEN_SUPER: {
    // 全兜底，所有内容都交给 handler
    pattern: /^([\s\S]+)$/m,
    handler: (match, line) => {
      // 1. 必须结尾有"分"字
      if (!/分\s*$/.test(line)) return null;
  
      // 2. 预处理：去掉结尾"分"字和多余空白
      line = line.replace(/分\s*$/m, '').replace(/[\r\n]+/g, ' ').trim();
  
      // 3. 用正则全局提取所有"号码-金额"对
      const reg = /(\d{2})[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]+(\d{2,3})(?=[\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]|$)/g;
      let m, groupMap = new Map();
      let matchArr = [];
      let replaced = line;
      while ((m = reg.exec(line)) !== null) {
        matchArr.push({ num: m[1], money: m[2], raw: m[0] });
        replaced = replaced.replace(m[0], '@@');
        if (!groupMap.has(m[2])) groupMap.set(m[2], []);
        groupMap.get(m[2]).push(m[1]);
      }
  
      // 4. 必须至少有一组，且第一组号码必须是两位数
      if (matchArr.length === 0) return null;
      if (!/^\d{2}$/.test(matchArr[0].num)) return null;
  
      // 5. 剩余内容只能是分隔符
      if (replaced.replace(/[@\-—~～@#￥%…&!、\\/*,，.＝=·吊赶打各\s]/g, '').length > 0) return null;
  
      // 6. 输出分组
      const METHOD_ID = require('./basePatterns').METHOD_ID;
      return Array.from(groupMap.entries()).map(([money, nums]) => ({
        methodId: METHOD_ID.LIANGMA_MULTI_SINGLE_WITH_MONEY_FEN_SUPER || 3,
        betNumbers: nums.join(','),
        money,
      }));
    }
  }



};

// 批量处理所有pattern属性，结尾加超级分隔符，类型安全判断
Object.keys(TWO_CODE_PATTERNS).forEach(key => {
  const pattern = TWO_CODE_PATTERNS[key].pattern;
  if (typeof pattern === 'string' && pattern.endsWith('$')) {
    TWO_CODE_PATTERNS[key].pattern = pattern.replace(/\$$/, '[，,./\*\-\=。·、\s]*$');
  } else if (pattern instanceof RegExp && pattern.source.endsWith('$')) {
    TWO_CODE_PATTERNS[key].pattern = new RegExp(pattern.source.replace(/\$$/, '[，,./\*\-\=。·、\s]*$'));
  }
}); 