{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\patterns\\twoCodePatterns.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\patterns\\twoCodePatterns.js", "mtime": 1756432155466}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\babel.config.js", "mtime": 1750852368688}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750942927225}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0Rlc2t0b3AvZHVvM2QvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLlRXT19DT0RFX1BBVFRFUk5TID0gdm9pZCAwOwp2YXIgX3NsaWNlZFRvQXJyYXkyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0Rlc2t0b3AvZHVvM2QvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvc2xpY2VkVG9BcnJheS5qcyIpKTsKdmFyIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQzovVXNlcnMvQWRtaW5pc3RyYXRvci9EZXNrdG9wL2R1bzNkL3J1b3lpLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIuanMiKSk7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZpbHRlci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZnJvbS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuam9pbi5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5qc29uLnN0cmluZ2lmeS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5udW1iZXIuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC5rZXlzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5kb3QtYWxsLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLnN0aWNreS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLnRlc3QuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5lbmRzLXdpdGguanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLm1hdGNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucmVwbGFjZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnNwbGl0LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcudHJpbS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuZXZlcnkuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5maWx0ZXIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5mb3ItZWFjaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLm1hcC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLnNvbWUuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IuanMiKTsKdmFyIF9iYXNlUGF0dGVybnMgPSByZXF1aXJlKCIuL2Jhc2VQYXR0ZXJucyIpOwovLyDnlKjpgJTvvJrmnKzmlofku7bnlKjkuo4i5Lik56CB5a+55a2Q44CB5Lik56CB57uE5ZCIIuebuOWFs+eOqeazleeahOato+WImeihqOi+vuW8j+S4juWkhOeQhumAu+i+kQovLyDnjqnms5XljIXmi6zvvJrkuKTnoIHlr7nlrZDjgIHkuKTnoIHnu4TlkIgKCi8vIOS4pOeggee7hOWQiOWSjOS4pOeggeWvueWtkOeOqeazleeahOato+WImeWMuemFjQp2YXIgVFdPX0NPREVfUEFUVEVSTlMgPSBleHBvcnRzLlRXT19DT0RFX1BBVFRFUk5TID0gewogIC8vIOS4pOeggee7hOWQiAogIExJQU5HTUFfWlVIRTogewogICAgLy8g5pSv5oyB77yaMTItMTDjgIExMue7hOWQiDEw44CBMTLigJTigJQxMOOAgTEyfjEw44CBMTJAMTDjgIExMj0xMOOAgTEyIOWQiiAxMOOAgTEy6LW2MTDjgIExMiUxMOOAgTEyIDEwIOetiQogICAgcGF0dGVybjogL14oXGR7Mn0pKD8657uE5ZCIfOe7hClbXC3igJR+772eQCPvv6Ul4oCmJiHjgIFcXC8qLO+8jC7vvJ09wrflkIrotbbmiZPlkIRcc10qKFxkKyk/JC8sCiAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKG1hdGNoLCBsaW5lKSB7CiAgICAgIGlmIChtYXRjaFsxXS5sZW5ndGggIT09IDIpIHJldHVybiBudWxsOwogICAgICB2YXIgbnVtID0gbWF0Y2hbMV07CiAgICAgIHZhciBtb25leSA9IG1hdGNoWzJdOwogICAgICAvLyDlj6rlhYHorrjkuI3lkIzmlbDlrZfnmoTkuKTnoIHnu4TlkIgKICAgICAgaWYgKG51bVswXSA9PT0gbnVtWzFdKSB7CiAgICAgICAgcmV0dXJuIG51bGw7CiAgICAgIH0KICAgICAgcmV0dXJuIFt7CiAgICAgICAgbWV0aG9kSWQ6IF9iYXNlUGF0dGVybnMuTUVUSE9EX0lELkxJQU5HTUFfWlVIRSwKICAgICAgICAvLyAzIOS4pOeggee7hOWQiAogICAgICAgIGJldE51bWJlcnM6IG51bSwKICAgICAgICBtb25leTogbW9uZXkKICAgICAgfV07CiAgICB9CiAgfSwKICAvLyDkuKTnoIHlr7nlrZAKICBMSUFOR01BX0RVSVpJOiB7CiAgICAvLyDljLnphY3vvJoxMuWvueWtkDEwIC0g5b+F6aG75YyF5ZCrIuWvueWtkCLlhbPplK7lrZcKICAgIHBhdHRlcm46IC9eKFxkezJ9KVtcLeKAlH7vvZ5AI++/pSXigKYmIeOAgVxcLyos77yMLu+8nT3Ct+WQiui1tuaJk+WQhFxzXSrlr7nlrZBbXC3igJR+772eQCPvv6Ul4oCmJiHjgIFcXC8qLO+8jC7vvJ09wrflkIrotbbmiZPlkIRcc10qKFxkKyk/KOWFg3znsbN85Z2XKT8kLywKICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobWF0Y2gsIGxpbmUpIHsKICAgICAgaWYgKG1hdGNoWzFdLmxlbmd0aCAhPT0gMikgcmV0dXJuIG51bGw7CiAgICAgIHZhciBudW0gPSBtYXRjaFsxXTsKICAgICAgdmFyIG1vbmV5ID0gbWF0Y2hbMl07CiAgICAgIC8vIOWPquWFgeiuuOWvueWtkO+8iOS4pOS4quaVsOWtl+ebuOWQjO+8iQogICAgICBpZiAobnVtWzBdICE9PSBudW1bMV0pIHsKICAgICAgICByZXR1cm4gbnVsbDsKICAgICAgfQogICAgICByZXR1cm4gW3sKICAgICAgICBtZXRob2RJZDogX2Jhc2VQYXR0ZXJucy5NRVRIT0RfSUQuTElBTkdNQV9EVUlaSSwKICAgICAgICAvLyA0IOS4pOeggeWvueWtkAogICAgICAgIGJldE51bWJlcnM6IG51bSwKICAgICAgICBtb25leTogbW9uZXkKICAgICAgfV07CiAgICB9CiAgfSwKICAvLyDkuKTnoIHlr7nlrZAKICBMSUFOR01BX0RVSVpJX0FMTDogewogICAgLy8g5Yy56YWN77yaMTLlr7nlrZAxMCAtIOW/hemhu+WMheWQqyLlr7nlrZAi5YWz6ZSu5a2XCiAgICBwYXR0ZXJuOiAvXihcZHsyfSlbXC3igJR+772eQCPvv6Ul4oCmJiHjgIFcXC8qLO+8jOS4gO+8jS7vvJ09wrdcc10q5a+55a2QW1wt4oCUfu+9nkAj77+lJeKApiYh44CBXFwvKizvvIwu77ydPcK35ZCK6LW25omT5ZCEXHNdKihcZCspPyjlhYN857GzfOWdlyk/JC8sCiAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKG1hdGNoLCBsaW5lKSB7CiAgICAgIGlmIChtYXRjaFsxXS5sZW5ndGggIT09IDIpIHJldHVybiBudWxsOwogICAgICB2YXIgbnVtID0gbWF0Y2hbMV07CiAgICAgIHZhciBtb25leSA9IG1hdGNoWzJdOwogICAgICB2YXIgdW5pdCA9IG1hdGNoWzNdOwogICAgICAvLyDlj6rlhYHorrjlr7nlrZDvvIjkuKTkuKrmlbDlrZfnm7jlkIzvvIkKICAgICAgaWYgKG51bVswXSAhPT0gbnVtWzFdKSB7CiAgICAgICAgcmV0dXJuIG51bGw7CiAgICAgIH0KICAgICAgdmFyIHJlc3VsdCA9IHsKICAgICAgICBtZXRob2RJZDogX2Jhc2VQYXR0ZXJucy5NRVRIT0RfSUQuTElBTkdNQV9EVUlaSSwKICAgICAgICAvLyA0IOS4pOeggeWvueWtkAogICAgICAgIGJldE51bWJlcnM6IG51bSwKICAgICAgICBtb25leTogbW9uZXkKICAgICAgfTsKICAgICAgaWYgKHVuaXQpIHJlc3VsdC51bml0ID0gdW5pdDsKICAgICAgcmV0dXJuIFtyZXN1bHRdOwogICAgfQogIH0sCiAgLy8g5Lik56CB57uE5LiJ77yI5pSv5oyB5Y2V57uE5ZKM5aSa57uE77yJCiAgTElBTkdNQV9aVVNBTjogewogICAgLy8g5Yy56YWN5qC85byPMe+8mjEy57uE5LiJNTAgKOWNlee7hCkKICAgIC8vIOWMuemFjeagvOW8jzLvvJoxMiAyM+e7hOS4iTUwICjlpJrnu4QpCiAgICBwYXR0ZXJuOiAvXiguKj8p57uE5LiJW1xzXC3igJR+772eQCPvv6Ul4oCmJiHjgIFcXC8qLO+8jOS4gO+8jS7vvJ09wrddKihcZCspKOWFg3zlnZd857GzKT9bXHNcLeKAlH7vvZ5AI++/pSXigKYmIeOAgVxcLyos77yM5LiA77yNLu+8nT3Ct10qJC8sCiAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKG1hdGNoLCBsaW5lKSB7CiAgICAgIC8vIOaPkOWPluWPt+eggemDqOWIhuWSjOmHkeminQogICAgICB2YXIgbnVtYmVyc1N0ciA9IG1hdGNoWzFdLnRyaW0oKTsKICAgICAgdmFyIG1vbmV5ID0gbWF0Y2hbMl07CiAgICAgIHZhciB1bml0ID0gbWF0Y2hbM107CgogICAgICAvLyDkvb/nlKjmraPliJnmj5Dlj5bmiYDmnInkuKTkvY3mlbDlrZcKICAgICAgdmFyIG51bWJlck1hdGNoZXMgPSBudW1iZXJzU3RyLm1hdGNoKC9cZHsyfS9nKTsKICAgICAgaWYgKCFudW1iZXJNYXRjaGVzIHx8IG51bWJlck1hdGNoZXMubGVuZ3RoID09PSAwKSByZXR1cm4gbnVsbDsKCiAgICAgIC8vIOaehOW7uiBKU09OIOagvOW8j+eahOaKleazqOWPt+eggQogICAgICB2YXIgYmV0TnVtYmVyc0pzb24gPSB7CiAgICAgICAgbnVtYmVyczogbnVtYmVyTWF0Y2hlcy5tYXAoZnVuY3Rpb24gKG51bSkgewogICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgYTogbnVtWzBdLAogICAgICAgICAgICBiOiBudW1bMV0KICAgICAgICAgIH07CiAgICAgICAgfSkKICAgICAgfTsKCiAgICAgIC8vIOaehOW7uue7k+aenAogICAgICB2YXIgcmVzdWx0ID0gewogICAgICAgIG1ldGhvZElkOiBfYmFzZVBhdHRlcm5zLk1FVEhPRF9JRC5MSUFOR01BX1pVU0FOLAogICAgICAgIC8vIDcwIOS4pOeggee7hOS4iQogICAgICAgIGJldE51bWJlcnM6IEpTT04uc3RyaW5naWZ5KGJldE51bWJlcnNKc29uKSwKICAgICAgICBtb25leTogbW9uZXkKICAgICAgfTsKICAgICAgaWYgKHVuaXQpIHJlc3VsdC51bml0ID0gdW5pdDsKICAgICAgcmV0dXJuIFtyZXN1bHRdOwogICAgfQogIH0sCiAgLy8g5Lik56CB57uE5ZCI5bim5a+55a2QCiAgTElBTkdNQV9aVUhFX0RVSVpJOiB7CiAgICAvLyDljLnphY3vvJoyNS0xMCs1ICjnu4TlkIgxMOWFg+WvueWtkDXlhYMpCiAgICBwYXR0ZXJuOiAvXihcZHsyfSkoPzpbXjAtOV0rXGR7Mn0pKi0oXGQrKVwrKFxkKykkLywKICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobWF0Y2gsIGxpbmUpIHsKICAgICAgdmFyIG51bWJlcnMgPSBsaW5lLnNwbGl0KF9iYXNlUGF0dGVybnMuQkFTRV9QQVRURVJOUy5TRVBBUkFUT1JTKS5maWx0ZXIoQm9vbGVhbikuZmlsdGVyKGZ1bmN0aW9uIChudW0pIHsKICAgICAgICByZXR1cm4gbnVtLmxlbmd0aCA9PT0gMjsKICAgICAgfSk7CiAgICAgIHZhciBhbGxOdW1iZXJzID0gbGluZS5zcGxpdChfYmFzZVBhdHRlcm5zLkJBU0VfUEFUVEVSTlMuU0VQQVJBVE9SUykuZmlsdGVyKEJvb2xlYW4pOwogICAgICBpZiAoIWFsbE51bWJlcnMubGVuZ3RoIHx8IGFsbE51bWJlcnMuc29tZShmdW5jdGlvbiAobikgewogICAgICAgIHJldHVybiBuLmxlbmd0aCAhPT0gMjsKICAgICAgfSkpIHJldHVybiBudWxsOwogICAgICB2YXIgenVoZU1vbmV5ID0gbWF0Y2hbMl07CiAgICAgIHZhciBkdWl6aU1vbmV5ID0gbWF0Y2hbM107CiAgICAgIHJldHVybiBbewogICAgICAgIG1ldGhvZElkOiAzLAogICAgICAgIC8vIOS4pOeggee7hOWQiAogICAgICAgIGJldE51bWJlcnM6IG51bWJlcnMuam9pbignLCcpLAogICAgICAgIG1vbmV5OiB6dWhlTW9uZXkKICAgICAgfSwgewogICAgICAgIG1ldGhvZElkOiA0LAogICAgICAgIC8vIOS4pOeggeWvueWtkAogICAgICAgIGJldE51bWJlcnM6IG51bWJlcnMuam9pbignLCcpLAogICAgICAgIG1vbmV5OiBkdWl6aU1vbmV5CiAgICAgIH1dOwogICAgfQogIH0sCiAgLy8g5aSa6KGM5Lik56CB57uE5ZCI77yI5pSv5oyB5Lit5paH6YeR6aKd6KGo6L6+77yJCiAgTVVMVElfTElBTkdNQTogewogICAgLy8g5Yy56YWN77yaMTIsMzQsNTblkIQxMOOAgeWQhOS4gOeZvumYsuS6jOWNgQogICAgcGF0dGVybjogL14oXGR7Mn0oPzpbXjAtOV0rXGR7Mn0pKikoPzrlkIQoW+S4gOS6jOS4ieWbm+S6lOWFreS4g+WFq+S5neWNgeeZvuWNg+S4h1xkXSspKT8oPzrpmLIoW+S4gOS6jOS4ieWbm+S6lOWFreS4g+WFq+S5neWNgeeZvuWNg+S4h1xkXSspKT8kLywKICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobWF0Y2gsIGxpbmUpIHsKICAgICAgdmFyIF9yZXF1aXJlID0gcmVxdWlyZSgnLi9iYXNlUGF0dGVybnMnKSwKICAgICAgICBjaGluZXNlVG9OdW1iZXIgPSBfcmVxdWlyZS5jaGluZXNlVG9OdW1iZXI7CiAgICAgIHZhciBudW1iZXJzID0gbGluZS5zcGxpdChfYmFzZVBhdHRlcm5zLkJBU0VfUEFUVEVSTlMuU0VQQVJBVE9SUykuZmlsdGVyKEJvb2xlYW4pLmZpbHRlcihmdW5jdGlvbiAobnVtKSB7CiAgICAgICAgcmV0dXJuIG51bS5sZW5ndGggPT09IDI7CiAgICAgIH0pOwogICAgICBpZiAoIW51bWJlcnMubGVuZ3RoKSByZXR1cm4gbnVsbDsKICAgICAgdmFyIG1vbmV5ID0gbWF0Y2hbMl0gPyBjaGluZXNlVG9OdW1iZXIobWF0Y2hbMl0pIDogJyc7CiAgICAgIHZhciBncm91cE1vbmV5ID0gbWF0Y2hbM10gPyBjaGluZXNlVG9OdW1iZXIobWF0Y2hbM10pIDogJyc7CiAgICAgIHZhciBncm91cHMgPSBbXTsKICAgICAgaWYgKG1vbmV5KSB7CiAgICAgICAgZ3JvdXBzLnB1c2goewogICAgICAgICAgbWV0aG9kSWQ6IF9iYXNlUGF0dGVybnMuTUVUSE9EX0lELkxJQU5HTUFfWlVIRSwKICAgICAgICAgIGJldE51bWJlcnM6IG51bWJlcnMuam9pbignLCcpLAogICAgICAgICAgbW9uZXk6IG1vbmV5CiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgaWYgKGdyb3VwTW9uZXkpIHsKICAgICAgICBncm91cHMucHVzaCh7CiAgICAgICAgICBtZXRob2RJZDogX2Jhc2VQYXR0ZXJucy5NRVRIT0RfSUQuTElBTkdNQV9EVUlaSSwKICAgICAgICAgIGJldE51bWJlcnM6IG51bWJlcnNbbnVtYmVycy5sZW5ndGggLSAxXSwKICAgICAgICAgIG1vbmV5OiBncm91cE1vbmV5CiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgY29uc29sZS5sb2coJ+S4pOeggSBoYW5kbGVyIOi/lOWbnjonLCBncm91cHMpOwogICAgICByZXR1cm4gZ3JvdXBzOwogICAgfQogIH0sCiAgLy8g5Lik56CB57uE5ZCIK+WvueWtkOWkmuWPt+eggSvlj4zph5Hpop3vvIzku7vmhI/liIbpmpTnrKYKICAvLyBMSUFOR01BX1pVSEVfRFVJWklfTVVMVElfTU9ORVlfU1VQRVI6IHsKICAvLyAgIC8vIOWMuemFje+8mjEyIDM0IDUrMeOAgTEyLzM0LzUrMeOAgTEyLTM0LTUrMeetiQogIC8vICAgcGF0dGVybjogL14oW1xzXFNdKykkL20sCiAgLy8gICBoYW5kbGVyOiAobWF0Y2gsIGxpbmUpID0+IHsKICAvLyAgICAgY29uc3QgeyBTVVBFUl9TRVBBUkFUT1JTIH0gPSByZXF1aXJlKCcuL2Jhc2VQYXR0ZXJucycpOwogIC8vICAgICBjb25zdCBsaW5lcyA9IGxpbmUuc3BsaXQoL1tcclxuXSsvKS5tYXAobCA9PiBsLnRyaW0oKSkuZmlsdGVyKEJvb2xlYW4pOwogIC8vICAgICAvLyDmjInnjqnms5Xlkozph5Hpop3liIbnu4TlkIjlubbvvIzmlK/mjIHmiYDmnInotoXnuqfliIbpmpTnrKYKICAvLyAgICAgY29uc3QgemhpeHVhbk1hcCA9IG5ldyBNYXAoKTsKICAvLyAgICAgY29uc3QgenV4dWFuTWFwID0gbmV3IE1hcCgpOwogIC8vICAgICBmb3IgKGNvbnN0IGwgb2YgbGluZXMpIHsKICAvLyAgICAgICAvLyDljLnphY3vvJoz5L2N5Y+356CBK+i2hee6p+WIhumalOespivph5Hpop0xK+i2hee6p+WIhumalOespivph5Hpop0yCiAgLy8gICAgICAgY29uc3QgbSA9IGwubWF0Y2gobmV3IFJlZ0V4cChgXihcXGR7Mn0pJHtTVVBFUl9TRVBBUkFUT1JTLnNvdXJjZX0oXFxkKylcKyhcXGQrKSRgKSk7CiAgLy8gICAgICAgaWYgKG0pIHsKICAvLyAgICAgICAgIGNvbnN0IG51bSA9IG1bMV07CiAgLy8gICAgICAgICBjb25zdCB6aGl4dWFuTW9uZXkgPSBtWzJdOwogIC8vICAgICAgICAgY29uc3QgenV4dWFuTW9uZXkgPSBtWzNdOwogIC8vICAgICAgICAgLy8g5ZCI5bm255u06YCJCiAgLy8gICAgICAgICBjb25zdCB6S2V5ID0gemhpeHVhbk1vbmV5OwogIC8vICAgICAgICAgaWYgKCF6aGl4dWFuTWFwLmhhcyh6S2V5KSkgemhpeHVhbk1hcC5zZXQoektleSwgW10pOwogIC8vICAgICAgICAgemhpeHVhbk1hcC5nZXQoektleSkucHVzaChudW0pOwogIC8vICAgICAgICAgLy8g5ZCI5bm257uE6YCJCiAgLy8gICAgICAgICBjb25zdCB6dUtleSA9IHp1eHVhbk1vbmV5OwogIC8vICAgICAgICAgaWYgKCF6dXh1YW5NYXAuaGFzKHp1S2V5KSkgenV4dWFuTWFwLnNldCh6dUtleSwgW10pOwogIC8vICAgICAgICAgenV4dWFuTWFwLmdldCh6dUtleSkucHVzaChudW0pOwogIC8vICAgICAgIH0KICAvLyAgICAgfQogIC8vICAgICBjb25zdCByZXN1bHQgPSBbXTsKICAvLyAgICAgZm9yIChjb25zdCBbbW9uZXksIG51bXNdIG9mIHpoaXh1YW5NYXAuZW50cmllcygpKSB7CiAgLy8gICAgICAgcmVzdWx0LnB1c2goewogIC8vICAgICAgICAgbWV0aG9kSWQ6IHJlcXVpcmUoJy4vYmFzZVBhdHRlcm5zJykuTUVUSE9EX0lELkxJQU5HTUFfWlVIRSwKICAvLyAgICAgICAgIGJldE51bWJlcnM6IG51bXMuam9pbignLCcpLAogIC8vICAgICAgICAgbW9uZXksCiAgLy8gICAgICAgfSk7CiAgLy8gICAgIH0KICAvLyAgICAgZm9yIChjb25zdCBbbW9uZXksIG51bXNdIG9mIHp1eHVhbk1hcC5lbnRyaWVzKCkpIHsKICAvLyAgICAgICByZXN1bHQucHVzaCh7CiAgLy8gICAgICAgICBtZXRob2RJZDogcmVxdWlyZSgnLi9iYXNlUGF0dGVybnMnKS5NRVRIT0RfSUQuTElBTkdNQV9EVUlaSSwKICAvLyAgICAgICAgIGJldE51bWJlcnM6IG51bXMuam9pbignLCcpLAogIC8vICAgICAgICAgbW9uZXksCiAgLy8gICAgICAgfSk7CiAgLy8gICAgIH0KICAvLyAgICAgcmV0dXJuIHJlc3VsdC5sZW5ndGggPyByZXN1bHQgOiBudWxsOwogIC8vICAgfQogIC8vIH0sCgogIC8vIOS4pOeggee7hOWQiO+8iOe7hOWtl+S4uue7hOWQiO+8jOmdnuWvueWtkO+8iQogIExJQU5HTUFfWlVIRV9aVV9TVVBFUjogewogICAgLy8g5Yy56YWN77yaMTLnu4QzMO+8iOWPquimgeaciee7hOWtl+S4lOS4jeaYr+WvueWtkO+8iQogICAgcGF0dGVybjogL14oXGR7Mn0p57uEKFxkKykkLywKICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobWF0Y2gsIGxpbmUpIHsKICAgICAgdmFyIG51bWJlcnMgPSBtYXRjaFsxXTsKICAgICAgaWYgKG51bWJlcnMubGVuZ3RoICE9PSAyKSByZXR1cm4gbnVsbDsKICAgICAgdmFyIG1vbmV5ID0gbWF0Y2hbMl07CiAgICAgIHJldHVybiBbewogICAgICAgIG1ldGhvZElkOiByZXF1aXJlKCcuL2Jhc2VQYXR0ZXJucycpLk1FVEhPRF9JRC5MSUFOR01BX1pVSEUsCiAgICAgICAgLy8gMyDkuKTnoIHnu4TlkIgKICAgICAgICBiZXROdW1iZXJzOiBudW1iZXJzLAogICAgICAgIG1vbmV5OiBtb25leQogICAgICB9XTsKICAgIH0KICB9LAogIC8vIOS4pOeggee7hOWQiOWkmuWPt+eggSvph5Hpop3vvIzku7vmhI/liIbpmpTnrKYKICBMSUFOR01BX1pVSEVfTVVMVElfU1VQRVI6IHsKICAgIC8vIOWMuemFje+8mjEyLTM06LW2NTDjgIExMi0zNOWQijUw44CBMTIvMzQvNTDjgIExMi0zNOi1tjUw5YWD44CBMTItMzTotbY1MOWdl+OAgTEyLTM06LW2NTDnsbPnrYkKICAgIHBhdHRlcm46IC9eKFxkezJ9KD86W1wtfu+9nkAj77+lJeKApiYh44CBXFwvKuS4gO+8jeOAgizvvIwu77ydPcK3IFx0K+WQiui1tuaJk+WQhF0rXGR7Mn0pKilbXC1+772eQCPvv6Ul4oCmJiHjgIFcXC8q5LiA77yN44CCLO+8jC7vvJ09wrcgXHQr5ZCK6LW25omT5ZCEXSsoXGQrKSjlhYN85Z2XfOexsyk/JC8sCiAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKG1hdGNoLCBsaW5lKSB7CiAgICAgIHZhciBfcmVxdWlyZTIgPSByZXF1aXJlKCcuL2Jhc2VQYXR0ZXJucycpLAogICAgICAgIFNVUEVSX1NFUEFSQVRPUlMgPSBfcmVxdWlyZTIuU1VQRVJfU0VQQVJBVE9SUzsKICAgICAgdmFyIGFsbE51bWJlcnMgPSBtYXRjaFsxXS5zcGxpdChTVVBFUl9TRVBBUkFUT1JTKS5maWx0ZXIoQm9vbGVhbik7CiAgICAgIGlmICghYWxsTnVtYmVycy5sZW5ndGggfHwgYWxsTnVtYmVycy5zb21lKGZ1bmN0aW9uIChuKSB7CiAgICAgICAgcmV0dXJuIG4ubGVuZ3RoICE9PSAyOwogICAgICB9KSkgcmV0dXJuIG51bGw7CiAgICAgIHZhciBtb25leSA9IG1hdGNoWzJdOwogICAgICAvLyDliIbnu4QKICAgICAgdmFyIHp1aGUgPSBbXTsKICAgICAgdmFyIGR1aXppID0gW107CiAgICAgIGFsbE51bWJlcnMuZm9yRWFjaChmdW5jdGlvbiAobnVtKSB7CiAgICAgICAgaWYgKG51bVswXSA9PT0gbnVtWzFdKSB7CiAgICAgICAgICBkdWl6aS5wdXNoKG51bSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHp1aGUucHVzaChudW0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHZhciByZXN1bHQgPSBbXTsKICAgICAgaWYgKHp1aGUubGVuZ3RoKSB7CiAgICAgICAgcmVzdWx0LnB1c2goewogICAgICAgICAgbWV0aG9kSWQ6IHJlcXVpcmUoJy4vYmFzZVBhdHRlcm5zJykuTUVUSE9EX0lELkxJQU5HTUFfWlVIRSwKICAgICAgICAgIC8vIDMg5Lik56CB57uE5ZCICiAgICAgICAgICBiZXROdW1iZXJzOiB6dWhlLmpvaW4oJywnKSwKICAgICAgICAgIG1vbmV5OiBtb25leQogICAgICAgIH0pOwogICAgICB9CiAgICAgIGlmIChkdWl6aS5sZW5ndGgpIHsKICAgICAgICByZXN1bHQucHVzaCh7CiAgICAgICAgICBtZXRob2RJZDogcmVxdWlyZSgnLi9iYXNlUGF0dGVybnMnKS5NRVRIT0RfSUQuTElBTkdNQV9EVUlaSSwKICAgICAgICAgIC8vIDQg5Lik56CB5a+55a2QCiAgICAgICAgICBiZXROdW1iZXJzOiBkdWl6aS5qb2luKCcsJyksCiAgICAgICAgICBtb25leTogbW9uZXkKICAgICAgICB9KTsKICAgICAgfQogICAgICByZXR1cm4gcmVzdWx0Lmxlbmd0aCA/IHJlc3VsdCA6IG51bGw7CiAgICB9CiAgfSwKICAvLyDkuKTnoIHlr7nlrZDvvIjlr7nlrZDlhbPplK7lrZfvvIkKICBMSUFOR01BX0RVSVpJX0RVSV9TVVBFUjogewogICAgLy8g5Yy56YWN77yaMTLlr7nlrZAzMAogICAgcGF0dGVybjogL14oXGR7Mn0p5a+55a2QKFxkKykkLywKICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobWF0Y2gsIGxpbmUpIHsKICAgICAgdmFyIG51bWJlcnMgPSBtYXRjaFsxXTsKICAgICAgaWYgKG51bWJlcnMubGVuZ3RoICE9PSAyKSByZXR1cm4gbnVsbDsKICAgICAgdmFyIG1vbmV5ID0gbWF0Y2hbMl07CiAgICAgIHJldHVybiBbewogICAgICAgIG1ldGhvZElkOiByZXF1aXJlKCcuL2Jhc2VQYXR0ZXJucycpLk1FVEhPRF9JRC5MSUFOR01BX0RVSVpJLAogICAgICAgIC8vIDQg5Lik56CB5a+55a2QCiAgICAgICAgYmV0TnVtYmVyczogbnVtYmVycywKICAgICAgICBtb25leTogbW9uZXkKICAgICAgfV07CiAgICB9CiAgfSwKICAvLyDmlrDlop7vvJrljZXlj7fnoIEr6YeR6aKd77yI5pSv5oyB5omA5pyJ5YiG6ZqU56ym77yJCiAgTElBTkdNQV9TSU5HTEVfV0lUSF9NT05FWV9TVVBFUjogewogICAgLy8g5Yy56YWN77yaNDUsMjAg5oiWIDQ177yMMjAg5oiWIDQ1IDIwIOaIliA0NeOAgTIwIOaIliA0NSsyMCDnrYnku7vmhI/liIbpmpTnrKYKICAgIHBhdHRlcm46IC9eKFxkezJ9KVtcLX7vvZ5AI++/pSXigKYmIeOAgVxcLyos77yM5LiA77yN44CCLu+8nT3Ct1xzK+WQiui1tuaJk+WQhF0rKFxkezEsNH0pJC8sCiAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKG1hdGNoLCBsaW5lKSB7CiAgICAgIHZhciBudW0gPSBtYXRjaFsxXTsKICAgICAgaWYgKG51bS5sZW5ndGggIT09IDIpIHJldHVybiBudWxsOwogICAgICB2YXIgbW9uZXkgPSBtYXRjaFsyXTsKICAgICAgLy8g5Yik5pat546p5rOVCiAgICAgIGlmIChudW1bMF0gPT09IG51bVsxXSkgewogICAgICAgIHJldHVybiBbewogICAgICAgICAgbWV0aG9kSWQ6IHJlcXVpcmUoJy4vYmFzZVBhdHRlcm5zJykuTUVUSE9EX0lELkxJQU5HTUFfRFVJWkksCiAgICAgICAgICAvLyA0IOS4pOeggeWvueWtkAogICAgICAgICAgYmV0TnVtYmVyczogbnVtLAogICAgICAgICAgbW9uZXk6IG1vbmV5CiAgICAgICAgfV07CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuIFt7CiAgICAgICAgICBtZXRob2RJZDogcmVxdWlyZSgnLi9iYXNlUGF0dGVybnMnKS5NRVRIT0RfSUQuTElBTkdNQV9aVUhFLAogICAgICAgICAgLy8gMyDkuKTnoIHnu4TlkIgKICAgICAgICAgIGJldE51bWJlcnM6IG51bSwKICAgICAgICAgIG1vbmV5OiBtb25leQogICAgICAgIH1dOwogICAgICB9CiAgICB9CiAgfSwKICAvLyDmlrDlop7vvJrlpJrlj7fnoIEv6YeR6aKd77yI5aaCNDXvvIwyOO+8jDE477yMNTIvMjDvvInvvIzlhbzlrrnmiYDmnInliIbpmpTnrKYKICBMSUFOR01BX01VTFRJX1dJVEhfTU9ORVlfU0xBU0hfU1VQRVI6IHsKICAgIC8vIOWMuemFje+8mjQ177yMMjjvvIwxOO+8jDUyLzIwIOaIliA0NSAyOCAxOCA1Mi8yMAogICAgcGF0dGVybjogL14oXGR7Mn0oPzpbXC1+772eQCPvv6Ul4oCmJiHjgIFcXC8qLO+8jC7vvJ095LiA77yN44CCwrdccyvlkIrotbbmiZPlkIRdK1xkezJ9KSopXC8oXGR7MSw0fSkkLywKICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobWF0Y2gsIGxpbmUpIHsKICAgICAgdmFyIF9yZXF1aXJlMyA9IHJlcXVpcmUoJy4vYmFzZVBhdHRlcm5zJyksCiAgICAgICAgU1VQRVJfU0VQQVJBVE9SUyA9IF9yZXF1aXJlMy5TVVBFUl9TRVBBUkFUT1JTOwogICAgICB2YXIgYWxsTnVtYmVycyA9IG1hdGNoWzFdLnNwbGl0KFNVUEVSX1NFUEFSQVRPUlMpLmZpbHRlcihCb29sZWFuKTsKICAgICAgaWYgKCFhbGxOdW1iZXJzLmxlbmd0aCB8fCBhbGxOdW1iZXJzLnNvbWUoZnVuY3Rpb24gKG4pIHsKICAgICAgICByZXR1cm4gbi5sZW5ndGggIT09IDI7CiAgICAgIH0pKSByZXR1cm4gbnVsbDsKICAgICAgdmFyIG1vbmV5ID0gbWF0Y2hbMl07CiAgICAgIC8vIOWIhue7hAogICAgICB2YXIgenVoZSA9IFtdOwogICAgICB2YXIgZHVpemkgPSBbXTsKICAgICAgYWxsTnVtYmVycy5mb3JFYWNoKGZ1bmN0aW9uIChudW0pIHsKICAgICAgICBpZiAobnVtWzBdID09PSBudW1bMV0pIHsKICAgICAgICAgIGR1aXppLnB1c2gobnVtKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgenVoZS5wdXNoKG51bSk7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgdmFyIHJlc3VsdCA9IFtdOwogICAgICBpZiAoenVoZS5sZW5ndGgpIHsKICAgICAgICByZXN1bHQucHVzaCh7CiAgICAgICAgICBtZXRob2RJZDogcmVxdWlyZSgnLi9iYXNlUGF0dGVybnMnKS5NRVRIT0RfSUQuTElBTkdNQV9aVUhFLAogICAgICAgICAgLy8gMyDkuKTnoIHnu4TlkIgKICAgICAgICAgIGJldE51bWJlcnM6IHp1aGUuam9pbignLCcpLAogICAgICAgICAgbW9uZXk6IG1vbmV5CiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgaWYgKGR1aXppLmxlbmd0aCkgewogICAgICAgIHJlc3VsdC5wdXNoKHsKICAgICAgICAgIG1ldGhvZElkOiByZXF1aXJlKCcuL2Jhc2VQYXR0ZXJucycpLk1FVEhPRF9JRC5MSUFOR01BX0RVSVpJLAogICAgICAgICAgLy8gNCDkuKTnoIHlr7nlrZAKICAgICAgICAgIGJldE51bWJlcnM6IGR1aXppLmpvaW4oJywnKSwKICAgICAgICAgIG1vbmV5OiBtb25leQogICAgICAgIH0pOwogICAgICB9CiAgICAgIHJldHVybiByZXN1bHQubGVuZ3RoID8gcmVzdWx0IDogbnVsbDsKICAgIH0KICB9LAogIC8vIOaWsOWinu+8muWkmuWPt+eggS/lj4zph5Hpop3vvIjlpoI0Ne+8jDI477yMMTjvvIw1Mi8yMC0177yJ77yM5YW85a655omA5pyJ5YiG6ZqU56ymCiAgTElBTkdNQV9NVUxUSV9XSVRIX0RPVUJMRV9NT05FWV9TTEFTSF9TVVBFUjogewogICAgLy8g5Yy56YWN77yaNDXvvIwyOO+8jDE477yMNTIvMjAtNSDmiJYgNDUgMjggMTggNTIvMjAtNQogICAgcGF0dGVybjogL14oXGR7Mn0oPzpbXC1+772eQCPvv6Ul4oCmJiHjgIFcXC8qLO+8jOS4gO+8jeOAgi7vvJ09wrdccyvlkIrotbbmiZPlkIRdK1xkezJ9KSopXC8oXGQrKS0oXGQrKSQvLAogICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihtYXRjaCwgbGluZSkgewogICAgICB2YXIgX3JlcXVpcmU0ID0gcmVxdWlyZSgnLi9iYXNlUGF0dGVybnMnKSwKICAgICAgICBTVVBFUl9TRVBBUkFUT1JTID0gX3JlcXVpcmU0LlNVUEVSX1NFUEFSQVRPUlM7CiAgICAgIHZhciBhbGxOdW1iZXJzID0gbWF0Y2hbMV0uc3BsaXQoU1VQRVJfU0VQQVJBVE9SUykuZmlsdGVyKEJvb2xlYW4pOwogICAgICBpZiAoIWFsbE51bWJlcnMubGVuZ3RoIHx8IGFsbE51bWJlcnMuc29tZShmdW5jdGlvbiAobikgewogICAgICAgIHJldHVybiBuLmxlbmd0aCAhPT0gMjsKICAgICAgfSkpIHJldHVybiBudWxsOwogICAgICB2YXIgbnVtYmVycyA9IGFsbE51bWJlcnM7CiAgICAgIHZhciB6dWhlTW9uZXkgPSBtYXRjaFsyXTsKICAgICAgdmFyIGR1aXppTW9uZXkgPSBtYXRjaFszXTsKICAgICAgcmV0dXJuIFt7CiAgICAgICAgbWV0aG9kSWQ6IHJlcXVpcmUoJy4vYmFzZVBhdHRlcm5zJykuTUVUSE9EX0lELkxJQU5HTUFfWlVIRSwKICAgICAgICAvLyAzIOS4pOeggee7hOWQiAogICAgICAgIGJldE51bWJlcnM6IG51bWJlcnMuam9pbignLCcpLAogICAgICAgIG1vbmV5OiB6dWhlTW9uZXkKICAgICAgfSwgewogICAgICAgIG1ldGhvZElkOiByZXF1aXJlKCcuL2Jhc2VQYXR0ZXJucycpLk1FVEhPRF9JRC5MSUFOR01BX0RVSVpJLAogICAgICAgIC8vIDQg5Lik56CB5a+55a2QCiAgICAgICAgYmV0TnVtYmVyczogbnVtYmVycy5qb2luKCcsJyksCiAgICAgICAgbW9uZXk6IGR1aXppTW9uZXkKICAgICAgfV07CiAgICB9CiAgfSwKICAvLyDmlrDlop7vvJrkuKTnoIHnu4TlkIjluKblr7nlrZDvvIjlpoI0NS0yMOmYsjXvvIkKICBMSUFOR01BX1pVSEVfRkFORzogewogICAgLy8g5Yy56YWN77yaNDUtMjDpmLI1CiAgICBwYXR0ZXJuOiAvXihcZHsyfSktKFxkKynpmLIoXGQrKSQvLAogICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihtYXRjaCwgbGluZSkgewogICAgICB2YXIgbnVtYmVycyA9IG1hdGNoWzFdOwogICAgICBpZiAobnVtYmVycy5sZW5ndGggIT09IDIpIHJldHVybiBudWxsOwogICAgICB2YXIgenVoZU1vbmV5ID0gbWF0Y2hbMl07CiAgICAgIHZhciBkdWl6aU1vbmV5ID0gbWF0Y2hbM107CiAgICAgIHJldHVybiBbewogICAgICAgIG1ldGhvZElkOiByZXF1aXJlKCcuL2Jhc2VQYXR0ZXJucycpLk1FVEhPRF9JRC5MSUFOR01BX1pVSEUsCiAgICAgICAgLy8gMyDkuKTnoIHnu4TlkIgKICAgICAgICBiZXROdW1iZXJzOiBudW1iZXJzLAogICAgICAgIG1vbmV5OiB6dWhlTW9uZXkKICAgICAgfSwgewogICAgICAgIG1ldGhvZElkOiByZXF1aXJlKCcuL2Jhc2VQYXR0ZXJucycpLk1FVEhPRF9JRC5MSUFOR01BX0RVSVpJLAogICAgICAgIC8vIDQg5Lik56CB5a+55a2QCiAgICAgICAgYmV0TnVtYmVyczogbnVtYmVycywKICAgICAgICBtb25leTogZHVpemlNb25leQogICAgICB9XTsKICAgIH0KICB9LAogIC8vIOaWsOWinu+8muWkmuWPt+eggSvnu4TlkIgr5a+55a2Q6YeR6aKd77yI5Lu75oSP5YiG6ZqU56ym77yM6YeR6aKd55SoL+WIhumalO+8jOWmgiAuLi4vMTArNe+8iQogIExJQU5HTUFfTVVMVElfWlVIRV9EVUlaSV9TTEFTSF9TVVBFUjogewogICAgLy8g5Yy56YWN77ya5aSn6YeP5Y+356CBLzEwKzXvvIzlj7fnoIHpl7Tku7vmhI/liIbpmpTnrKYKICAgIHBhdHRlcm46IC9eKFtcZOOAgSzvvIwu44CCXFwvKlxzXG5cclwtfu+9nkAj77+lJeKApiYh44CB77yd5LiA77yN44CCPcK35ZCK6LW25omT5ZCEXSspKD86XC985ZCEfFxzfOWQinzotbZ85omTKShcZCspXCsoXGQrKSQvLAogICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihtYXRjaCwgbGluZSkgewogICAgICB2YXIgX3JlcXVpcmU1ID0gcmVxdWlyZSgnLi9iYXNlUGF0dGVybnMnKSwKICAgICAgICBTVVBFUl9TRVBBUkFUT1JTID0gX3JlcXVpcmU1LlNVUEVSX1NFUEFSQVRPUlM7CiAgICAgIHZhciBhbGxOdW1iZXJzID0gbWF0Y2hbMV0uc3BsaXQoU1VQRVJfU0VQQVJBVE9SUykuZmlsdGVyKEJvb2xlYW4pOwogICAgICBpZiAoIWFsbE51bWJlcnMubGVuZ3RoIHx8IGFsbE51bWJlcnMuc29tZShmdW5jdGlvbiAobikgewogICAgICAgIHJldHVybiBuLmxlbmd0aCAhPT0gMjsKICAgICAgfSkpIHJldHVybiBudWxsOwogICAgICB2YXIgbnVtYmVycyA9IGFsbE51bWJlcnM7CiAgICAgIHZhciB6dWhlTW9uZXkgPSBtYXRjaFsyXTsKICAgICAgdmFyIGR1aXppTW9uZXkgPSBtYXRjaFszXTsKICAgICAgcmV0dXJuIFt7CiAgICAgICAgbWV0aG9kSWQ6IHJlcXVpcmUoJy4vYmFzZVBhdHRlcm5zJykuTUVUSE9EX0lELkxJQU5HTUFfWlVIRSwKICAgICAgICAvLyAzIOS4pOeggee7hOWQiAogICAgICAgIGJldE51bWJlcnM6IG51bWJlcnMuam9pbignLCcpLAogICAgICAgIG1vbmV5OiB6dWhlTW9uZXkKICAgICAgfSwgewogICAgICAgIG1ldGhvZElkOiByZXF1aXJlKCcuL2Jhc2VQYXR0ZXJucycpLk1FVEhPRF9JRC5MSUFOR01BX0RVSVpJLAogICAgICAgIC8vIDQg5Lik56CB5a+55a2QCiAgICAgICAgYmV0TnVtYmVyczogbnVtYmVycy5qb2luKCcsJyksCiAgICAgICAgbW9uZXk6IGR1aXppTW9uZXkKICAgICAgfV07CiAgICB9CiAgfSwKICAvLyDmlrDlop7vvJrlpJrnu4Tlj7fnoIEr5ZCEWOmHkemine+8jOaUr+aMgei2hee6p+WIhumalOespgogIExJQU5HTUFfTVVMVElfRUFDSF9TVVBFUjogewogICAgLy8g5Yy56YWN77yaMTItMzQtNTblkIQxMDDjgIExMiwzNCw1NuWQhDEwMOetiQogICAgcGF0dGVybjogL14oXGR7Mn0oPzpbXC1+772eQCPvv6Ul4oCmJiHjgIFcXC8qLO+8jC7vvJ095LiA77yN44CCwrdccyvlkIrotbbmiZPlkIRdK1xkezJ9KSop5ZCEKFxkKyko5YWDfOWdl3znsbMpPyQvLAogICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihtYXRjaCwgbGluZSkgewogICAgICB2YXIgX3JlcXVpcmU2ID0gcmVxdWlyZSgnLi9iYXNlUGF0dGVybnMnKSwKICAgICAgICBTVVBFUl9TRVBBUkFUT1JTID0gX3JlcXVpcmU2LlNVUEVSX1NFUEFSQVRPUlM7CiAgICAgIHZhciBhbGxOdW1iZXJzID0gbWF0Y2hbMV0uc3BsaXQoU1VQRVJfU0VQQVJBVE9SUykuZmlsdGVyKEJvb2xlYW4pOwogICAgICBpZiAoIWFsbE51bWJlcnMubGVuZ3RoIHx8IGFsbE51bWJlcnMuc29tZShmdW5jdGlvbiAobikgewogICAgICAgIHJldHVybiBuLmxlbmd0aCAhPT0gMjsKICAgICAgfSkpIHJldHVybiBudWxsOwogICAgICB2YXIgbW9uZXkgPSBtYXRjaFsyXTsKICAgICAgcmV0dXJuIFt7CiAgICAgICAgbWV0aG9kSWQ6IHJlcXVpcmUoJy4vYmFzZVBhdHRlcm5zJykuTUVUSE9EX0lELkxJQU5HTUFfWlVIRSwKICAgICAgICBiZXROdW1iZXJzOiBhbGxOdW1iZXJzLmpvaW4oJywnKSwKICAgICAgICBtb25leTogbW9uZXkKICAgICAgfV07CiAgICB9CiAgfSwKICAvLyDmlrDlop7vvJrlpJrkuKrkuKTnoIHlj7fnoIHvvIjotoXnuqfliIbpmpTnrKbliIblibLvvIkrL+mHkeminTEv6YeR6aKdMu+8jOe7hOWQiCvlr7nlrZAKICBMSUFOR01BX01VTFRJX1NVUEVSX1NMQVNIX1RXT19NT05FWTogewogICAgcGF0dGVybjogL14oW1xk44CBLO+8jC7jgIJcXC8qXHNcblxyXC1+772eQCPvv6Ul4oCmJiHjgIHkuIDvvI3jgILvvJ09wrflkIrotbbmiZPlkIRdKylcLyhcZCspXC8oXGQrKSQvLAogICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihtYXRjaCwgbGluZSkgewogICAgICB2YXIgc3RyaWN0ID0gYXJndW1lbnRzLmxlbmd0aCA+IDIgJiYgYXJndW1lbnRzWzJdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMl0gOiB0cnVlOwogICAgICB2YXIgX3JlcXVpcmU3ID0gcmVxdWlyZSgnLi9iYXNlUGF0dGVybnMnKSwKICAgICAgICBTVVBFUl9TRVBBUkFUT1JTID0gX3JlcXVpcmU3LlNVUEVSX1NFUEFSQVRPUlM7CiAgICAgIHZhciBudW1iZXJzID0gbWF0Y2hbMV0uc3BsaXQoU1VQRVJfU0VQQVJBVE9SUykubWFwKGZ1bmN0aW9uIChuKSB7CiAgICAgICAgcmV0dXJuIG4udHJpbSgpOwogICAgICB9KS5maWx0ZXIoZnVuY3Rpb24gKG4pIHsKICAgICAgICByZXR1cm4gbi5sZW5ndGggPT09IDI7CiAgICAgIH0pOwogICAgICBpZiAoIW51bWJlcnMubGVuZ3RoKSByZXR1cm4gW107CiAgICAgIHZhciB6dWhlTW9uZXkgPSBtYXRjaFsyXTsKICAgICAgdmFyIGR1aXppTW9uZXkgPSBtYXRjaFszXTsKICAgICAgLy8g5Lil5qC86YeR6aKd6ZW/5bqm5Yik5pat77ya5Lik56CB6YeR6aKd5LiN6IO95pivMuS9jeaVsOWtl++8jOWQpuWImemYsuivr+WRveS4rQogICAgICBpZiAoc3RyaWN0ICYmICgvXlxkezJ9JC8udGVzdCh6dWhlTW9uZXkpIHx8IC9eXGR7Mn0kLy50ZXN0KGR1aXppTW9uZXkpKSkgcmV0dXJuIG51bGw7CiAgICAgIHJldHVybiBbewogICAgICAgIG1ldGhvZElkOiByZXF1aXJlKCcuL2Jhc2VQYXR0ZXJucycpLk1FVEhPRF9JRC5MSUFOR01BX1pVSEUsCiAgICAgICAgLy8gMiDkuKTnoIHnu4TlkIgKICAgICAgICBiZXROdW1iZXJzOiBudW1iZXJzLmpvaW4oJywnKSwKICAgICAgICBtb25leTogenVoZU1vbmV5CiAgICAgIH0sIHsKICAgICAgICBtZXRob2RJZDogcmVxdWlyZSgnLi9iYXNlUGF0dGVybnMnKS5NRVRIT0RfSUQuTElBTkdNQV9EVUlaSSwKICAgICAgICAvLyAzIOS4pOeggeWvueWtkAogICAgICAgIGJldE51bWJlcnM6IG51bWJlcnMuam9pbignLCcpLAogICAgICAgIG1vbmV5OiBkdWl6aU1vbmV5CiAgICAgIH1dOwogICAgfQogIH0sCiAgLy8g5paw5aKe77ya5om56YeP5Lik56CB57uE5ZCIL+mHkeminS/lhYPlnZfnsbPvvIzmlK/mjIHlpoIiMDggIC8yMCDlhYMi77yM5bCG6YeR6aKd5LiA6Ie055qE5Y+356CB5ZCI5bm25Li65LiA5Liq5LiL5rOo5Y2V77yM6KaB5rGC5q+P6KGM5b+F6aG75pyJ5Y2V5L2N5LiU6Iez5bCR5Lik6KGMCiAgTElBTkdNQV9NVUxUSV9XSVRIX01PTkVZX1VOSVRfU1VQRVI6IHsKICAgIC8vIOWFqOWMhe+8jOaJgOacieWGheWuuemDvei/myBoYW5kbGVyCiAgICBwYXR0ZXJuOiAvXihbXHNcU10rKSQvbSwKICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobWF0Y2gsIGxpbmUpIHsKICAgICAgdmFyIGxpbmVzID0gbGluZS5zcGxpdCgvXG4rLykubWFwKGZ1bmN0aW9uIChsKSB7CiAgICAgICAgcmV0dXJuIGwudHJpbSgpOwogICAgICB9KS5maWx0ZXIoQm9vbGVhbik7CiAgICAgIHZhciBncm91cE1hcCA9IG5ldyBNYXAoKTsKICAgICAgdmFyIHZhbGlkTGluZUNvdW50ID0gMDsKICAgICAgdmFyIF9pdGVyYXRvciA9ICgwLCBfY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlcjIuZGVmYXVsdCkobGluZXMpLAogICAgICAgIF9zdGVwOwogICAgICB0cnkgewogICAgICAgIGZvciAoX2l0ZXJhdG9yLnMoKTsgIShfc3RlcCA9IF9pdGVyYXRvci5uKCkpLmRvbmU7KSB7CiAgICAgICAgICB2YXIgbCA9IF9zdGVwLnZhbHVlOwogICAgICAgICAgLy8g6YeR6aKd5ZKM5Y2V5L2N5LmL6Ze05YWB6K645pyJ6LaF57qn5YiG6ZqU56ymCiAgICAgICAgICB2YXIgbSA9IGwubWF0Y2goLyhcZHsyfSlbXHMs77yMLuOAglwt4oCUfu+9nkAj77+lJeKApiYh44CBXFwvKl0qXC8/W1xzLO+8jC7jgIJcLeKAlH7vvZ5AI++/pSXigKYmIeOAgVxcLypdKihcZCspW1xzLO+8jC7jgIJcLeKAlH7vvZ5AI++/pSXigKYmIeOAgVxcLypdKijlhYN85Z2XfOexsykvKTsKICAgICAgICAgIGlmIChtKSB7CiAgICAgICAgICAgIHZhbGlkTGluZUNvdW50Kys7CiAgICAgICAgICAgIHZhciBudW0gPSBtWzFdOwogICAgICAgICAgICB2YXIgbW9uZXkgPSBtWzJdOwogICAgICAgICAgICBpZiAoIWdyb3VwTWFwLmhhcyhtb25leSkpIGdyb3VwTWFwLnNldChtb25leSwgW10pOwogICAgICAgICAgICBncm91cE1hcC5nZXQobW9uZXkpLnB1c2gobnVtKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICAgLy8g5b+F6aG75Lik6KGM5Y+K5Lul5LiK5pyJ5pWI6KGMCiAgICAgIH0gY2F0Y2ggKGVycikgewogICAgICAgIF9pdGVyYXRvci5lKGVycik7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgX2l0ZXJhdG9yLmYoKTsKICAgICAgfQogICAgICBpZiAodmFsaWRMaW5lQ291bnQgPCAyKSByZXR1cm4gbnVsbDsKICAgICAgaWYgKCFncm91cE1hcC5zaXplKSByZXR1cm4gbnVsbDsKICAgICAgcmV0dXJuIEFycmF5LmZyb20oZ3JvdXBNYXAuZW50cmllcygpKS5tYXAoZnVuY3Rpb24gKF9yZWYpIHsKICAgICAgICB2YXIgX3JlZjIgPSAoMCwgX3NsaWNlZFRvQXJyYXkyLmRlZmF1bHQpKF9yZWYsIDIpLAogICAgICAgICAgbW9uZXkgPSBfcmVmMlswXSwKICAgICAgICAgIG51bXMgPSBfcmVmMlsxXTsKICAgICAgICByZXR1cm4gewogICAgICAgICAgbWV0aG9kSWQ6IHJlcXVpcmUoJy4vYmFzZVBhdHRlcm5zJykuTUVUSE9EX0lELkxJQU5HTUFfWlVIRSwKICAgICAgICAgIGJldE51bWJlcnM6IG51bXMuam9pbignLCcpLAogICAgICAgICAgbW9uZXk6IG1vbmV5CiAgICAgICAgfTsKICAgICAgfSk7CiAgICB9CiAgfSwKICAvLyDmlrDlop7vvJrpgJrnlKjmoLzlvI/vvIzmlK/mjIEi5Y+356CB77yI5Lu75oSP6LaF57qn5YiG6ZqU56ym77yJK+mHkemine+8iOWFgy/nsbMv5Z2XL+epuu+8iSLvvIzoh6rliqjor4bliKvkuLrkuKTnoIHnu4TlkIgKICBMSUFOR01BX1VOSVZFUlNBTF9TVVBFUjogewogICAgcGF0dGVybjogL14oW1xk44CBLO+8jC7jgIJcXC8qXHNcblxyXC1+772eQCPvv6Ul4oCmJiHkuIDvvI3igJTjgILjgIHvvJ09wrflkIrotbbmiZPlkIRdKylbXC1+772eQCPvv6Ul4oCmJiHjgIFcXC8qLO+8jOS4gO+8jeKAlOOAgi7vvJ09wrflkIrotbbmiZPlkIRdKyhbMC05XSspKOWFg3znsbN85Z2XKT8kLywKICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobWF0Y2gsIGxpbmUpIHsKICAgICAgdmFyIF9yZXF1aXJlOCA9IHJlcXVpcmUoJy4vYmFzZVBhdHRlcm5zJyksCiAgICAgICAgU1VQRVJfU0VQQVJBVE9SUyA9IF9yZXF1aXJlOC5TVVBFUl9TRVBBUkFUT1JTLAogICAgICAgIE1FVEhPRF9JRCA9IF9yZXF1aXJlOC5NRVRIT0RfSUQ7CiAgICAgIC8vIOaUr+aMgeWkmuihjOaJuemHjwogICAgICB2YXIgbGluZXMgPSBsaW5lLnNwbGl0KC9cbnxcci8pLm1hcChmdW5jdGlvbiAobCkgewogICAgICAgIHJldHVybiBsLnRyaW0oKTsKICAgICAgfSkuZmlsdGVyKEJvb2xlYW4pOwogICAgICAvLyDlkIjlubbnjqnms5Ur6YeR6aKd5LiA6Ie055qE5Y+356CBCiAgICAgIHZhciBncm91cE1hcCA9IG5ldyBNYXAoKTsKICAgICAgbGluZXMuZm9yRWFjaChmdW5jdGlvbiAobCkgewogICAgICAgIHZhciBtID0gbC5tYXRjaCgvXihbXGTjgIEs77yMLuOAglxcLypcc1xuXHJcLX7vvZ5AI++/pSXigKYmIeS4gO+8jeKAlOOAguOAge+8nT3Ct+WQiui1tuaJk+WQhF0rKVtcLX7vvZ5AI++/pSXigKYmIeOAgVxcLyos77yM5LiA77yN4oCU44CCLu+8nT3Ct+WQiui1tuaJk+WQhF0rKFswLTldKyko5YWDfOexs3zlnZcpPyQvKTsKICAgICAgICBpZiAoIW0pIHJldHVybjsKICAgICAgICB2YXIgbnVtYmVycyA9IG1bMV0uc3BsaXQoU1VQRVJfU0VQQVJBVE9SUykubWFwKGZ1bmN0aW9uIChuKSB7CiAgICAgICAgICByZXR1cm4gbi50cmltKCk7CiAgICAgICAgfSkuZmlsdGVyKGZ1bmN0aW9uIChuKSB7CiAgICAgICAgICByZXR1cm4gbi5sZW5ndGggPT09IDI7CiAgICAgICAgfSk7CiAgICAgICAgdmFyIG1vbmV5ID0gbVsyXTsKICAgICAgICBudW1iZXJzLmZvckVhY2goZnVuY3Rpb24gKG51bSkgewogICAgICAgICAgdmFyIG1ldGhvZElkID0gbnVsbDsKICAgICAgICAgIGlmIChudW1bMF0gPT09IG51bVsxXSkgewogICAgICAgICAgICBtZXRob2RJZCA9IE1FVEhPRF9JRC5MSUFOR01BX0RVSVpJOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgbWV0aG9kSWQgPSBNRVRIT0RfSUQuTElBTkdNQV9aVUhFOwogICAgICAgICAgfQogICAgICAgICAgdmFyIGtleSA9IG1ldGhvZElkICsgJ18nICsgbW9uZXk7CiAgICAgICAgICBpZiAoIWdyb3VwTWFwLmhhcyhrZXkpKSBncm91cE1hcC5zZXQoa2V5LCBbXSk7CiAgICAgICAgICBncm91cE1hcC5nZXQoa2V5KS5wdXNoKG51bSk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgICB2YXIgcmVzdWx0ID0gW107CiAgICAgIHZhciBfaXRlcmF0b3IyID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KShncm91cE1hcC5lbnRyaWVzKCkpLAogICAgICAgIF9zdGVwMjsKICAgICAgdHJ5IHsKICAgICAgICBmb3IgKF9pdGVyYXRvcjIucygpOyAhKF9zdGVwMiA9IF9pdGVyYXRvcjIubigpKS5kb25lOykgewogICAgICAgICAgdmFyIF9zdGVwMiR2YWx1ZSA9ICgwLCBfc2xpY2VkVG9BcnJheTIuZGVmYXVsdCkoX3N0ZXAyLnZhbHVlLCAyKSwKICAgICAgICAgICAga2V5ID0gX3N0ZXAyJHZhbHVlWzBdLAogICAgICAgICAgICBudW1zID0gX3N0ZXAyJHZhbHVlWzFdOwogICAgICAgICAgdmFyIF9rZXkkc3BsaXQgPSBrZXkuc3BsaXQoJ18nKSwKICAgICAgICAgICAgX2tleSRzcGxpdDIgPSAoMCwgX3NsaWNlZFRvQXJyYXkyLmRlZmF1bHQpKF9rZXkkc3BsaXQsIDIpLAogICAgICAgICAgICBtZXRob2RJZCA9IF9rZXkkc3BsaXQyWzBdLAogICAgICAgICAgICBtb25leSA9IF9rZXkkc3BsaXQyWzFdOwogICAgICAgICAgcmVzdWx0LnB1c2goewogICAgICAgICAgICBtZXRob2RJZDogTnVtYmVyKG1ldGhvZElkKSwKICAgICAgICAgICAgYmV0TnVtYmVyczogbnVtcy5qb2luKCcsJyksCiAgICAgICAgICAgIG1vbmV5OiBtb25leQogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBfaXRlcmF0b3IyLmUoZXJyKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICBfaXRlcmF0b3IyLmYoKTsKICAgICAgfQogICAgICByZXR1cm4gcmVzdWx0Lmxlbmd0aCA/IHJlc3VsdCA6IG51bGw7CiAgICB9CiAgfSwKICAvLyDmlrDlop7vvJoxMiAzNCDlkIQ1MCs1CiAgTElBTkdNQV9FQUNIX1BMVVNfU1VQRVI6IHsKICAgIC8vIOWMuemFje+8mjEyIDM0IOWQhDUwKzUKICAgIHBhdHRlcm46IC9eKFtcZOOAgSzvvIwu44CCXFwvKlxzXG5cclwtfu+9nkAj77+lJeKApiYh5LiA77yN44CC44CB77ydPcK35ZCK6LW25omT5ZCEXSsp5ZCEKFswLTldKylcKyhbMC05XSspJC8sCiAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKG1hdGNoLCBsaW5lKSB7CiAgICAgIHZhciBfcmVxdWlyZTkgPSByZXF1aXJlKCcuL2Jhc2VQYXR0ZXJucycpLAogICAgICAgIFNVUEVSX1NFUEFSQVRPUlMgPSBfcmVxdWlyZTkuU1VQRVJfU0VQQVJBVE9SUywKICAgICAgICBNRVRIT0RfSUQgPSBfcmVxdWlyZTkuTUVUSE9EX0lEOwogICAgICB2YXIgbnVtYmVycyA9IG1hdGNoWzFdLnNwbGl0KFNVUEVSX1NFUEFSQVRPUlMpLm1hcChmdW5jdGlvbiAobikgewogICAgICAgIHJldHVybiBuLnRyaW0oKTsKICAgICAgfSkuZmlsdGVyKEJvb2xlYW4pOwogICAgICB2YXIgbW9uZXkxID0gbWF0Y2hbMl07CiAgICAgIHZhciBtb25leTIgPSBtYXRjaFszXTsKICAgICAgLy8g5Y2V57uE5Y+356CBCiAgICAgIGlmIChudW1iZXJzLmxlbmd0aCA9PT0gMSAmJiBudW1iZXJzWzBdLmxlbmd0aCA9PT0gMikgewogICAgICAgIHJldHVybiBbewogICAgICAgICAgbWV0aG9kSWQ6IE1FVEhPRF9JRC5MSUFOR01BX1pVSEUsCiAgICAgICAgICBiZXROdW1iZXJzOiBudW1iZXJzWzBdLAogICAgICAgICAgbW9uZXk6IG1vbmV5MQogICAgICAgIH0sIHsKICAgICAgICAgIG1ldGhvZElkOiBNRVRIT0RfSUQuTElBTkdNQV9EVUlaSSwKICAgICAgICAgIGJldE51bWJlcnM6IG51bWJlcnNbMF0sCiAgICAgICAgICBtb25leTogbW9uZXkyCiAgICAgICAgfV07CiAgICAgIH0KICAgICAgLy8g5aSa57uE5Y+356CBCiAgICAgIGlmIChudW1iZXJzLmxlbmd0aCA+PSAyICYmIG51bWJlcnMuZXZlcnkoZnVuY3Rpb24gKG4pIHsKICAgICAgICByZXR1cm4gbi5sZW5ndGggPT09IDI7CiAgICAgIH0pKSB7CiAgICAgICAgcmV0dXJuIFt7CiAgICAgICAgICBtZXRob2RJZDogTUVUSE9EX0lELkxJQU5HTUFfWlVIRSwKICAgICAgICAgIGJldE51bWJlcnM6IG51bWJlcnMuam9pbignLCcpLAogICAgICAgICAgbW9uZXk6IG1vbmV5MQogICAgICAgIH0sIHsKICAgICAgICAgIG1ldGhvZElkOiBNRVRIT0RfSUQuTElBTkdNQV9EVUlaSSwKICAgICAgICAgIGJldE51bWJlcnM6IG51bWJlcnMuam9pbignLCcpLAogICAgICAgICAgbW9uZXk6IG1vbmV5MgogICAgICAgIH1dOwogICAgICB9CiAgICAgIHJldHVybiBudWxsOwogICAgfQogIH0sCiAgLy8g5paw5aKe77ya57qv5pWw5a2XK+epuuagvCvph5Hpop3mlbTkvZPph5Hpop3moLzlvI8KICBMSUFOR01BX1VOSVZFUlNBTF9TVVBFUl9ORVc6IHsKICAgIHBhdHRlcm46IC9eKFswLTldezIsfSlccysoXGQrKSjlhYN85Z2XfOexsyk/JC8sCiAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKG1hdGNoLCBsaW5lKSB7CiAgICAgIHZhciBiZXROdW1iZXJzID0gbWF0Y2hbMV07CiAgICAgIHZhciBtb25leSA9IG1hdGNoWzJdOwogICAgICAvLyDlj6rlhYHorrjlj7fnoIHplb/luqbkuLoyCiAgICAgIGlmICghYmV0TnVtYmVycyB8fCBiZXROdW1iZXJzLmxlbmd0aCAhPT0gMikgcmV0dXJuIG51bGw7CiAgICAgIHJldHVybiBbewogICAgICAgIG1ldGhvZElkOiByZXF1aXJlKCcuL2Jhc2VQYXR0ZXJucycpLk1FVEhPRF9JRC5MSUFOR01BX1pVSEUsCiAgICAgICAgYmV0TnVtYmVyczogYmV0TnVtYmVycywKICAgICAgICBtb25leTogbW9uZXkKICAgICAgfV07CiAgICB9CiAgfSwKICAvLyDmlrDlop7vvJrmlK/mjIEiMTIzNDXmiZM5MCLmoLzlvI/vvIzkuKTnoIHml7bkuLrkuKTnoIHnu4TlkIjjgIIKICBMSUFOR01BX0RBX01PTkVZOiB7CiAgICBwYXR0ZXJuOiAvXihbMC05XXsyfSnmiZMoXGQrKSjlhYN85Z2XfOexsyk/JC8sCiAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKG1hdGNoLCBsaW5lKSB7CiAgICAgIHJldHVybiBbewogICAgICAgIG1ldGhvZElkOiByZXF1aXJlKCcuL2Jhc2VQYXR0ZXJucycpLk1FVEhPRF9JRC5MSUFOR01BX1pVSEUsCiAgICAgICAgYmV0TnVtYmVyczogbWF0Y2hbMV0sCiAgICAgICAgbW9uZXk6IG1hdGNoWzJdCiAgICAgIH1dOwogICAgfQogIH0sCiAgLy8g5paw5aKe77ya5aSa5Y+356CBL+mHkeminS/liqDlj7fph5Hpop3vvIjlpoIgNDUgMjggMTggNTIvMSsx77yJCiAgTElBTkdNQV9NVUxUSV9XSVRIX01PTkVZX1NMQVNIX1BMVVM6IHsKICAgIC8vIOWMuemFje+8mjQ1IDI4IDE4IDUyLzErMe+8jOWFvOWuuS/lkI7mnInml6DnqbrmoLwKICAgIHBhdHRlcm46IC9eKFxkezJ9KD86W1xzLO+8jFwtXSs/XGR7Mn0pKilcL1xzKihcZCspXCsoXGQrKSQvLAogICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihtYXRjaCwgbGluZSkgewogICAgICB2YXIgbnVtYmVycyA9IG1hdGNoWzFdLnNwbGl0KC9bXHMs77yMXC1dKy8pLmZpbHRlcihCb29sZWFuKTsKICAgICAgdmFyIHp1aGVNb25leSA9IG1hdGNoWzJdOwogICAgICB2YXIgZHVpemlNb25leSA9IG1hdGNoWzNdOwogICAgICByZXR1cm4gW3sKICAgICAgICBtZXRob2RJZDogcmVxdWlyZSgnLi9iYXNlUGF0dGVybnMnKS5NRVRIT0RfSUQuTElBTkdNQV9aVUhFLAogICAgICAgIC8vIDMg5Lik56CB57uE5ZCICiAgICAgICAgYmV0TnVtYmVyczogbnVtYmVycy5qb2luKCcsJyksCiAgICAgICAgbW9uZXk6IHp1aGVNb25leQogICAgICB9LCB7CiAgICAgICAgbWV0aG9kSWQ6IHJlcXVpcmUoJy4vYmFzZVBhdHRlcm5zJykuTUVUSE9EX0lELkxJQU5HTUFfRFVJWkksCiAgICAgICAgLy8gNCDkuKTnoIHlr7nlrZAKICAgICAgICBiZXROdW1iZXJzOiBudW1iZXJzLmpvaW4oJywnKSwKICAgICAgICBtb25leTogZHVpemlNb25leQogICAgICB9XTsKICAgIH0KICB9LAogIC8vIOaWsOWinu+8muWkmuihjOWPt+eggSvmsYnlrZfph5Hpop0r5YqgK+axieWtl+mHkemine+8jOmHkemineS4gOiHtOaJjeWQiOW5tu+8jOWQpuWImeWIhue7hAogIExJQU5HTUFfTVVMVElfTElORV9NT05FWV9QTFVTX0NOOiB7CiAgICAvLyDpgILphY3lpoLvvJoyM+WNgeWKoOS6lFxuNDXljYHliqDkupQKICAgIHBhdHRlcm46IC9eKCg/OlxkezJ9W++8jCzjgIFdP1vkuIDkuozkuInlm5vkupTlha3kuIPlhavkuZ3ljYHnmb7ljYPkuIdcZF0r5YqgW+S4gOS6jOS4ieWbm+S6lOWFreS4g+WFq+S5neWNgeeZvuWNg+S4h1xkXStccypcbj8pKykkL20sCiAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKG1hdGNoLCBsaW5lKSB7CiAgICAgIHZhciBfcmVxdWlyZTAgPSByZXF1aXJlKCcuL2Jhc2VQYXR0ZXJucycpLAogICAgICAgIE1FVEhPRF9JRCA9IF9yZXF1aXJlMC5NRVRIT0RfSUQsCiAgICAgICAgY2hpbmVzZVRvTnVtYmVyID0gX3JlcXVpcmUwLmNoaW5lc2VUb051bWJlcjsKICAgICAgdmFyIGxpbmVzID0gbGluZS5zcGxpdCgvXG4vKS5tYXAoZnVuY3Rpb24gKGwpIHsKICAgICAgICByZXR1cm4gbC50cmltKCk7CiAgICAgIH0pLmZpbHRlcihCb29sZWFuKTsKICAgICAgdmFyIGdyb3VwTWFwID0gbmV3IE1hcCgpOwogICAgICB2YXIgdW5tYXRjaGVkTGluZXMgPSBbXTsKICAgICAgdmFyIF9pdGVyYXRvcjMgPSAoMCwgX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIyLmRlZmF1bHQpKGxpbmVzKSwKICAgICAgICBfc3RlcDM7CiAgICAgIHRyeSB7CiAgICAgICAgZm9yIChfaXRlcmF0b3IzLnMoKTsgIShfc3RlcDMgPSBfaXRlcmF0b3IzLm4oKSkuZG9uZTspIHsKICAgICAgICAgIHZhciBsID0gX3N0ZXAzLnZhbHVlOwogICAgICAgICAgdmFyIG0gPSBsLm1hdGNoKC9eKFxkezJ9KVvvvIws44CBXT8oW+S4gOS6jOS4ieWbm+S6lOWFreS4g+WFq+S5neWNgeeZvuWNg+S4h1xkXSsp5YqgKFvkuIDkuozkuInlm5vkupTlha3kuIPlhavkuZ3ljYHnmb7ljYPkuIdcZF0rKSQvKTsKICAgICAgICAgIGlmIChtKSB7CiAgICAgICAgICAgIHZhciBudW0gPSBtWzFdOwogICAgICAgICAgICB2YXIgenVoZSA9IGNoaW5lc2VUb051bWJlcihtWzJdKTsKICAgICAgICAgICAgdmFyIGR1aXppID0gY2hpbmVzZVRvTnVtYmVyKG1bM10pOwogICAgICAgICAgICB2YXIgbW9uZXlLZXkgPSAiIi5jb25jYXQoenVoZSwgIisiKS5jb25jYXQoZHVpemkpOwogICAgICAgICAgICBpZiAoIWdyb3VwTWFwLmhhcyhtb25leUtleSkpIGdyb3VwTWFwLnNldChtb25leUtleSwgW10pOwogICAgICAgICAgICBncm91cE1hcC5nZXQobW9uZXlLZXkpLnB1c2gobnVtKTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHVubWF0Y2hlZExpbmVzLnB1c2gobCk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICBfaXRlcmF0b3IzLmUoZXJyKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICBfaXRlcmF0b3IzLmYoKTsKICAgICAgfQogICAgICB2YXIgcmVzdWx0ID0gW107CiAgICAgIHZhciBfaXRlcmF0b3I0ID0gKDAsIF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyMi5kZWZhdWx0KShncm91cE1hcC5lbnRyaWVzKCkpLAogICAgICAgIF9zdGVwNDsKICAgICAgdHJ5IHsKICAgICAgICBmb3IgKF9pdGVyYXRvcjQucygpOyAhKF9zdGVwNCA9IF9pdGVyYXRvcjQubigpKS5kb25lOykgewogICAgICAgICAgdmFyIF9zdGVwNCR2YWx1ZSA9ICgwLCBfc2xpY2VkVG9BcnJheTIuZGVmYXVsdCkoX3N0ZXA0LnZhbHVlLCAyKSwKICAgICAgICAgICAgX21vbmV5S2V5ID0gX3N0ZXA0JHZhbHVlWzBdLAogICAgICAgICAgICBudW1iZXJzID0gX3N0ZXA0JHZhbHVlWzFdOwogICAgICAgICAgdmFyIF9tb25leUtleSRzcGxpdCA9IF9tb25leUtleS5zcGxpdCgnKycpLAogICAgICAgICAgICBfbW9uZXlLZXkkc3BsaXQyID0gKDAsIF9zbGljZWRUb0FycmF5Mi5kZWZhdWx0KShfbW9uZXlLZXkkc3BsaXQsIDIpLAogICAgICAgICAgICB6dWhlTW9uZXkgPSBfbW9uZXlLZXkkc3BsaXQyWzBdLAogICAgICAgICAgICBkdWl6aU1vbmV5ID0gX21vbmV5S2V5JHNwbGl0MlsxXTsKICAgICAgICAgIHJlc3VsdC5wdXNoKHsKICAgICAgICAgICAgbWV0aG9kSWQ6IE1FVEhPRF9JRC5MSUFOR01BX1pVSEUsCiAgICAgICAgICAgIGJldE51bWJlcnM6IG51bWJlcnMuam9pbignLCcpLAogICAgICAgICAgICBtb25leTogenVoZU1vbmV5CiAgICAgICAgICB9KTsKICAgICAgICAgIHJlc3VsdC5wdXNoKHsKICAgICAgICAgICAgbWV0aG9kSWQ6IE1FVEhPRF9JRC5MSUFOR01BX0RVSVpJLAogICAgICAgICAgICBiZXROdW1iZXJzOiBudW1iZXJzLmpvaW4oJywnKSwKICAgICAgICAgICAgbW9uZXk6IGR1aXppTW9uZXkKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyKSB7CiAgICAgICAgX2l0ZXJhdG9yNC5lKGVycik7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgX2l0ZXJhdG9yNC5mKCk7CiAgICAgIH0KICAgICAgcmV0dXJuIHJlc3VsdC5sZW5ndGggPyByZXN1bHQgOiBudWxsOwogICAgfQogIH0sCiAgLy8g5paw5aKe77ya5a+5MzMvNe+8jOino+aekOS4uuS4pOeggeWvueWtkO+8jOWPt+eggTMz77yM6YeR6aKdNeWFgwogIERVSVpJX1NMQVNIX01PTkVZOiB7CiAgICBwYXR0ZXJuOiAvXuWvuShcZHsyfSlcLyhcZCspJC8sCiAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKG1hdGNoLCBsaW5lKSB7CiAgICAgIHJldHVybiBbewogICAgICAgIG1ldGhvZElkOiBfYmFzZVBhdHRlcm5zLk1FVEhPRF9JRC5MSUFOR01BX0RVSVpJLAogICAgICAgIGJldE51bWJlcnM6IG1hdGNoWzFdLAogICAgICAgIG1vbmV5OiBtYXRjaFsyXQogICAgICB9XTsKICAgIH0KICB9LAogIC8vIOaWsOWinu+8mjY55LiAMjnkuIDkuKoxMOWFg++8jOS4pOeggee7hOWQiDEw5YWDCiAgRVJNQV9ZSV9ZSV9PTkVfTU9ORVk6IHsKICAgIC8vIOWMuemFje+8mjY55LiAMjnkuIDkuKoxMOWFgwogICAgcGF0dGVybjogL14oXGR7Mn0p5LiAKFxkezJ9KeS4gOS4qihcZCspKOWFg3zlnZd857GzKT8kLywKICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobWF0Y2gsIGxpbmUpIHsKICAgICAgdmFyIG51bTEgPSBtYXRjaFsxXTsKICAgICAgdmFyIG51bTIgPSBtYXRjaFsyXTsKICAgICAgdmFyIG1vbmV5ID0gbWF0Y2hbM107CiAgICAgIHJldHVybiBbewogICAgICAgIG1ldGhvZElkOiByZXF1aXJlKCcuL2Jhc2VQYXR0ZXJucycpLk1FVEhPRF9JRC5FUk1BX0NPTUJPLAogICAgICAgIC8vIOS4pOeggee7hOWQiAogICAgICAgIGJldE51bWJlcnM6IG51bTEgKyAnLCcgKyBudW0yLAogICAgICAgIG1vbmV5OiBtb25leQogICAgICB9XTsKICAgIH0KICB9LAogIC8vICAvLyDkv67mraPvvJrmjpIuLi7lkIRY5YWD77yM5om56YeP57uE6YCJ77yI5Y675o6JIue7hOeahCLkuKTkuKrlrZfvvIkKICAvLyAgTElBTkdNQV9QQUlfRUFDSF9NT05FWV9aVVhVQU46IHsKICAvLyAgIC8vIOWMuemFje+8muaOkjA377yMMjjvvIwwOO+8jC4uLuWQhDXlhYMg5oiWIOaOkjI377yMMDLvvIwzOO+8jC4uLue7hOeahOWQhDXlhYMKICAvLyAgIHBhdHRlcm46IC9eKFtcZO+8jCzjgIFcc10rKSjnu4TnmoR857uEKT/lkIQoXGQrKSjlhYN85Z2XfOexsyk/W++8jCwuL1wqXC1cPeOAgsK344CBXHNdKiQvLAogIC8vICAgaGFuZGxlcjogKG1hdGNoLCBsaW5lKSA9PiB7CiAgLy8gICAgIGNvbnN0IG51bWJlcnMgPSBtYXRjaFsxXS5zcGxpdCgvW++8jCzjgIFcc10rLykubWFwKG4gPT4gbi50cmltKCkpLmZpbHRlcihuID0+IG4ubGVuZ3RoID09PSAyKTsKICAvLyAgICAgY29uc3QgbW9uZXkgPSBtYXRjaFszXTsKICAvLyAgICAgaWYgKCFudW1iZXJzLmxlbmd0aCkgcmV0dXJuIG51bGw7CiAgLy8gICAgIHJldHVybiBbewogIC8vICAgICAgIG1ldGhvZElkOiByZXF1aXJlKCcuL2Jhc2VQYXR0ZXJucycpLk1FVEhPRF9JRC5MSUFOR01BX1pVWFVBTiwgLy8gIDLnoIHnu4TpgIkKICAvLyAgICAgICBiZXROdW1iZXJzOiBudW1iZXJzLmpvaW4oJywnKSwKICAvLyAgICAgICBtb25leSwKICAvLyAgICAgfV07CiAgLy8gICB9CiAgLy8gfSwKICAvLyAgLy8g5L+u5q2j77ya5o6SLi4u5ZCEWOWFg++8jOaJuemHj+e7hOmAie+8iOWOu+aOiSLnu4TnmoQi5Lik5Liq5a2X77yJCiAgLy8gIExJQU5HTUFfUEFJX0VBQ0hfTU9ORVlfWkhJWFVBTjogewogIC8vICAgLy8g5Yy56YWN77ya5o6SMDLvvIwwOO+8jDA477yMLi4u5ZCENeWFgyDmiJYg5o6SMDfvvIwwMu+8jDAz77yMLi4u57uE55qE5ZCENeWFgwogIC8vICAgcGF0dGVybjogL14oW1xk77yMLOOAgVxzXSspKOebtOeahHznm7R85YC855qEfOWAvCk/5ZCEKFxkKyko5YWDfOWdl3znsbMpP1vvvIwsLi9cKlwtXD3jgILCt+OAgVxzXSokLywKICAvLyAgIGhhbmRsZXI6IChtYXRjaCwgbGluZSkgPT4gewogIC8vICAgICBjb25zdCBudW1iZXJzID0gbWF0Y2hbMV0uc3BsaXQoL1vvvIws44CBXHNdKy8pLm1hcChuID0+IG4udHJpbSgpKS5maWx0ZXIobiA9PiBuLmxlbmd0aCA9PT0gMik7CiAgLy8gICAgIGNvbnN0IG1vbmV5ID0gbWF0Y2hbM107CiAgLy8gICAgIGlmICghbnVtYmVycy5sZW5ndGgpIHJldHVybiBudWxsOwogIC8vICAgICByZXR1cm4gW3sKICAvLyAgICAgICBtZXRob2RJZDogcmVxdWlyZSgnLi9iYXNlUGF0dGVybnMnKS5NRVRIT0RfSUQuTElBTkdNQV9aSElYVUFOLCAvLyAgMueggee7hOmAiQogIC8vICAgICAgIGJldE51bWJlcnM6IG51bWJlcnMuam9pbignLCcpLAogIC8vICAgICAgIG1vbmV5LAogIC8vICAgICB9XTsKICAvLyAgIH0KICAvLyB9LAoKICBMSUFOR01BX01VTFRJX1NJTkdMRV9XSVRIX01PTkVZX0ZFTl9TVVBFUjogewogICAgLy8g5YWo5YWc5bqV77yM5omA5pyJ5YaF5a656YO95Lqk57uZIGhhbmRsZXIKICAgIHBhdHRlcm46IC9eKFtcc1xTXSspJC9tLAogICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihtYXRjaCwgbGluZSkgewogICAgICAvLyAxLiDlv4Xpobvnu5PlsL7mnIki5YiGIuWtlwogICAgICBpZiAoIS/liIZccyokLy50ZXN0KGxpbmUpKSByZXR1cm4gbnVsbDsKCiAgICAgIC8vIDIuIOmihOWkhOeQhu+8muWOu+aOiee7k+WwviLliIYi5a2X5ZKM5aSa5L2Z56m655m9CiAgICAgIGxpbmUgPSBsaW5lLnJlcGxhY2UoL+WIhlxzKiQvbSwgJycpLnJlcGxhY2UoL1tcclxuXSsvZywgJyAnKS50cmltKCk7CgogICAgICAvLyAzLiDnlKjmraPliJnlhajlsYDmj5Dlj5bmiYDmnIki5Y+356CBLemHkeminSLlr7kKICAgICAgdmFyIHJlZyA9IC8oXGR7Mn0pW1wt4oCUfu+9nkAj77+lJeKApiYh44CBXFwvKizvvIwu77ydPcK35ZCK6LW25omT5ZCEXHNdKyhcZHsyLDN9KSg/PVtcLeKAlH7vvZ5AI++/pSXigKYmIeOAgVxcLyos77yMLu+8nT3Ct+WQiui1tuaJk+WQhFxzXXwkKS9nOwogICAgICB2YXIgbSwKICAgICAgICBncm91cE1hcCA9IG5ldyBNYXAoKTsKICAgICAgdmFyIG1hdGNoQXJyID0gW107CiAgICAgIHZhciByZXBsYWNlZCA9IGxpbmU7CiAgICAgIHdoaWxlICgobSA9IHJlZy5leGVjKGxpbmUpKSAhPT0gbnVsbCkgewogICAgICAgIG1hdGNoQXJyLnB1c2goewogICAgICAgICAgbnVtOiBtWzFdLAogICAgICAgICAgbW9uZXk6IG1bMl0sCiAgICAgICAgICByYXc6IG1bMF0KICAgICAgICB9KTsKICAgICAgICByZXBsYWNlZCA9IHJlcGxhY2VkLnJlcGxhY2UobVswXSwgJ0BAJyk7CiAgICAgICAgaWYgKCFncm91cE1hcC5oYXMobVsyXSkpIGdyb3VwTWFwLnNldChtWzJdLCBbXSk7CiAgICAgICAgZ3JvdXBNYXAuZ2V0KG1bMl0pLnB1c2gobVsxXSk7CiAgICAgIH0KCiAgICAgIC8vIDQuIOW/hemhu+iHs+WwkeacieS4gOe7hO+8jOS4lOesrOS4gOe7hOWPt+eggeW/hemhu+aYr+S4pOS9jeaVsAogICAgICBpZiAobWF0Y2hBcnIubGVuZ3RoID09PSAwKSByZXR1cm4gbnVsbDsKICAgICAgaWYgKCEvXlxkezJ9JC8udGVzdChtYXRjaEFyclswXS5udW0pKSByZXR1cm4gbnVsbDsKCiAgICAgIC8vIDUuIOWJqeS9meWGheWuueWPquiDveaYr+WIhumalOespgogICAgICBpZiAocmVwbGFjZWQucmVwbGFjZSgvW0BcLeKAlH7vvZ5AI++/pSXigKYmIeOAgVxcLyos77yMLu+8nT3Ct+WQiui1tuaJk+WQhFxzXS9nLCAnJykubGVuZ3RoID4gMCkgcmV0dXJuIG51bGw7CgogICAgICAvLyA2LiDovpPlh7rliIbnu4QKICAgICAgdmFyIE1FVEhPRF9JRCA9IHJlcXVpcmUoJy4vYmFzZVBhdHRlcm5zJykuTUVUSE9EX0lEOwogICAgICByZXR1cm4gQXJyYXkuZnJvbShncm91cE1hcC5lbnRyaWVzKCkpLm1hcChmdW5jdGlvbiAoX3JlZjMpIHsKICAgICAgICB2YXIgX3JlZjQgPSAoMCwgX3NsaWNlZFRvQXJyYXkyLmRlZmF1bHQpKF9yZWYzLCAyKSwKICAgICAgICAgIG1vbmV5ID0gX3JlZjRbMF0sCiAgICAgICAgICBudW1zID0gX3JlZjRbMV07CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIG1ldGhvZElkOiBNRVRIT0RfSUQuTElBTkdNQV9NVUxUSV9TSU5HTEVfV0lUSF9NT05FWV9GRU5fU1VQRVIgfHwgMywKICAgICAgICAgIGJldE51bWJlcnM6IG51bXMuam9pbignLCcpLAogICAgICAgICAgbW9uZXk6IG1vbmV5CiAgICAgICAgfTsKICAgICAgfSk7CiAgICB9CiAgfQp9OwoKLy8g5om56YeP5aSE55CG5omA5pyJcGF0dGVybuWxnuaAp++8jOe7k+WwvuWKoOi2hee6p+WIhumalOespu+8jOexu+Wei+WuieWFqOWIpOaWrQpPYmplY3Qua2V5cyhUV09fQ09ERV9QQVRURVJOUykuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgdmFyIHBhdHRlcm4gPSBUV09fQ09ERV9QQVRURVJOU1trZXldLnBhdHRlcm47CiAgaWYgKHR5cGVvZiBwYXR0ZXJuID09PSAnc3RyaW5nJyAmJiBwYXR0ZXJuLmVuZHNXaXRoKCckJykpIHsKICAgIFRXT19DT0RFX1BBVFRFUk5TW2tleV0ucGF0dGVybiA9IHBhdHRlcm4ucmVwbGFjZSgvXCQkLywgJ1vvvIwsLi9cKlwtXD3jgILCt+OAgVxzXSokJyk7CiAgfSBlbHNlIGlmIChwYXR0ZXJuIGluc3RhbmNlb2YgUmVnRXhwICYmIHBhdHRlcm4uc291cmNlLmVuZHNXaXRoKCckJykpIHsKICAgIFRXT19DT0RFX1BBVFRFUk5TW2tleV0ucGF0dGVybiA9IG5ldyBSZWdFeHAocGF0dGVybi5zb3VyY2UucmVwbGFjZSgvXCQkLywgJ1vvvIwsLi9cKlwtXD3jgILCt+OAgVxzXSokJykpOwogIH0KfSk7"}, {"version": 3, "names": ["_basePatterns", "require", "TWO_CODE_PATTERNS", "exports", "LIANGMA_ZUHE", "pattern", "handler", "match", "line", "length", "num", "money", "methodId", "METHOD_ID", "betNumbers", "LIANGMA_DUIZI", "LIANGMA_DUIZI_ALL", "unit", "result", "LIANGMA_ZUSAN", "numbersStr", "trim", "numberMatches", "betNumbersJson", "numbers", "map", "a", "b", "JSON", "stringify", "LIANGMA_ZUHE_DUIZI", "split", "BASE_PATTERNS", "SEPARATORS", "filter", "Boolean", "allNumbers", "some", "n", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "MULTI_LIANGMA", "_require", "chineseToNumber", "groupMoney", "groups", "push", "console", "log", "LIANGMA_ZUHE_ZU_SUPER", "LIANGMA_ZUHE_MULTI_SUPER", "_require2", "SUPER_SEPARATORS", "zuhe", "duizi", "for<PERSON>ach", "LIANGMA_DUIZI_DUI_SUPER", "LIANGMA_SINGLE_WITH_MONEY_SUPER", "LIANGMA_MULTI_WITH_MONEY_SLASH_SUPER", "_require3", "LIANGMA_MULTI_WITH_DOUBLE_MONEY_SLASH_SUPER", "_require4", "LIANGMA_ZUHE_FANG", "LIANGMA_MULTI_ZUHE_DUIZI_SLASH_SUPER", "_require5", "LIANGMA_MULTI_EACH_SUPER", "_require6", "LIANGMA_MULTI_SUPER_SLASH_TWO_MONEY", "strict", "arguments", "undefined", "_require7", "test", "LIANGMA_MULTI_WITH_MONEY_UNIT_SUPER", "lines", "l", "groupMap", "Map", "validLineCount", "_iterator", "_createForOfIteratorHelper2", "default", "_step", "s", "done", "value", "m", "has", "set", "get", "err", "e", "f", "size", "Array", "from", "entries", "_ref", "_ref2", "_slicedToArray2", "nums", "LIANGMA_UNIVERSAL_SUPER", "_require8", "key", "_iterator2", "_step2", "_step2$value", "_key$split", "_key$split2", "Number", "LIANGMA_EACH_PLUS_SUPER", "_require9", "money1", "money2", "every", "LIANGMA_UNIVERSAL_SUPER_NEW", "LIANGMA_DA_MONEY", "LIANGMA_MULTI_WITH_MONEY_SLASH_PLUS", "LIANGMA_MULTI_LINE_MONEY_PLUS_CN", "_require0", "unmatchedLines", "_iterator3", "_step3", "moneyKey", "concat", "_iterator4", "_step4", "_step4$value", "_moneyKey$split", "_moneyKey$split2", "DUIZI_SLASH_MONEY", "ERMA_YI_YI_ONE_MONEY", "num1", "num2", "ERMA_COMBO", "LIANGMA_MULTI_SINGLE_WITH_MONEY_FEN_SUPER", "replace", "reg", "matchArr", "replaced", "exec", "raw", "_ref3", "_ref4", "Object", "keys", "endsWith", "RegExp", "source"], "sources": ["C:/Users/<USER>/Desktop/duo3d/ruoyi-ui/src/views/game/record/components/patterns/twoCodePatterns.js"], "sourcesContent": ["// 用途：本文件用于\"两码对子、两码组合\"相关玩法的正则表达式与处理逻辑\r\n// 玩法包括：两码对子、两码组合\r\nimport { BASE_PATTERNS, cleanNumberString, parseMoney, parseLotteryType, METHOD_ID, LOTTERY_TYPE } from './basePatterns';\r\n\r\n// 两码组合和两码对子玩法的正则匹配\r\nexport const TWO_CODE_PATTERNS = {\r\n  // 两码组合\r\n  LIANGMA_ZUHE: {\r\n    // 支持：12-10、12组合10、12——10、12~10、12@10、12=10、12 吊 10、12赶10、12%10、12 10 等\r\n    pattern: /^(\\d{2})(?:组合|组)[\\-—~～@#￥%…&!、\\\\/*,，.＝=·吊赶打各\\s]*(\\d+)?$/,\r\n    handler: (match, line) => {\r\n      if (match[1].length !== 2) return null;\r\n      const num = match[1];\r\n      const money = match[2];\r\n      // 只允许不同数字的两码组合\r\n      if (num[0] === num[1]) {\r\n        return null;\r\n      }\r\n      return [{\r\n        methodId: METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合\r\n        betNumbers: num,\r\n        money: money\r\n      }];\r\n    }\r\n  },\r\n\r\n  // 两码对子\r\n  LIANGMA_DUIZI: {\r\n    // 匹配：12对子10 - 必须包含\"对子\"关键字\r\n    pattern: /^(\\d{2})[\\-—~～@#￥%…&!、\\\\/*,，.＝=·吊赶打各\\s]*对子[\\-—~～@#￥%…&!、\\\\/*,，.＝=·吊赶打各\\s]*(\\d+)?(元|米|块)?$/,\r\n    handler: (match, line) => {\r\n      if (match[1].length !== 2) return null;\r\n      const num = match[1];\r\n      const money = match[2];\r\n      // 只允许对子（两个数字相同）\r\n      if (num[0] !== num[1]) {\r\n        return null;\r\n      }\r\n      return [{\r\n        methodId: METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子\r\n        betNumbers: num,\r\n        money: money\r\n      }];\r\n    }\r\n  },\r\n\r\n  // 两码对子\r\n  LIANGMA_DUIZI_ALL: {\r\n    // 匹配：12对子10 - 必须包含\"对子\"关键字\r\n    pattern: /^(\\d{2})[\\-—~～@#￥%…&!、\\\\/*,，一－.＝=·\\s]*对子[\\-—~～@#￥%…&!、\\\\/*,，.＝=·吊赶打各\\s]*(\\d+)?(元|米|块)?$/,\r\n    handler: (match, line) => {\r\n      if (match[1].length !== 2) return null;\r\n      const num = match[1];\r\n      const money = match[2];\r\n      const unit = match[3];\r\n      // 只允许对子（两个数字相同）\r\n      if (num[0] !== num[1]) {\r\n        return null;\r\n      }\r\n      const result = {\r\n        methodId: METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子\r\n        betNumbers: num,\r\n        money: money\r\n      };\r\n      if (unit) result.unit = unit;\r\n      return [result];\r\n    }\r\n  },\r\n\r\n  // 两码组三（支持单组和多组）\r\n  LIANGMA_ZUSAN: {\r\n    // 匹配格式1：12组三50 (单组)\r\n    // 匹配格式2：12 23组三50 (多组)\r\n    pattern: /^(.*?)组三[\\s\\-—~～@#￥%…&!、\\\\/*,，一－.＝=·]*(\\d+)(元|块|米)?[\\s\\-—~～@#￥%…&!、\\\\/*,，一－.＝=·]*$/,\r\n    handler: (match, line) => {\r\n      // 提取号码部分和金额\r\n      const numbersStr = match[1].trim();\r\n      const money = match[2];\r\n      const unit = match[3];\r\n\r\n      // 使用正则提取所有两位数字\r\n      const numberMatches = numbersStr.match(/\\d{2}/g);\r\n      if (!numberMatches || numberMatches.length === 0) return null;\r\n\r\n      // 构建 JSON 格式的投注号码\r\n      const betNumbersJson = {\r\n        numbers: numberMatches.map(num => ({\r\n          a: num[0],\r\n          b: num[1]\r\n        }))\r\n      };\r\n\r\n      // 构建结果\r\n      const result = {\r\n        methodId: METHOD_ID.LIANGMA_ZUSAN,  // 70 两码组三\r\n        betNumbers: JSON.stringify(betNumbersJson),\r\n        money: money\r\n      };\r\n\r\n      if (unit) result.unit = unit;\r\n      return [result];\r\n    }\r\n  },\r\n\r\n  // 两码组合带对子\r\n  LIANGMA_ZUHE_DUIZI: {\r\n    // 匹配：25-10+5 (组合10元对子5元)\r\n    pattern: /^(\\d{2})(?:[^0-9]+\\d{2})*-(\\d+)\\+(\\d+)$/,\r\n    handler: (match, line) => {\r\n      const numbers = line.split(BASE_PATTERNS.SEPARATORS).filter(Boolean)\r\n        .filter(num => num.length === 2);\r\n      const allNumbers = line.split(BASE_PATTERNS.SEPARATORS).filter(Boolean);\r\n      if (!allNumbers.length || allNumbers.some(n => n.length !== 2)) return null;\r\n      const zuheMoney = match[2];\r\n      const duiziMoney = match[3];\r\n\r\n      return [\r\n        {\r\n          methodId: 3, // 两码组合\r\n          betNumbers: numbers.join(','),\r\n          money: zuheMoney\r\n        },\r\n        {\r\n          methodId: 4, // 两码对子\r\n          betNumbers: numbers.join(','),\r\n          money: duiziMoney\r\n        }\r\n      ];\r\n    }\r\n  },\r\n\r\n  // 多行两码组合（支持中文金额表达）\r\n  MULTI_LIANGMA: {\r\n    // 匹配：12,34,56各10、各一百防二十\r\n    pattern: /^(\\d{2}(?:[^0-9]+\\d{2})*)(?:各([一二三四五六七八九十百千万\\d]+))?(?:防([一二三四五六七八九十百千万\\d]+))?$/,\r\n    handler: (match, line) => {\r\n      const { chineseToNumber } = require('./basePatterns');\r\n      const numbers = line.split(BASE_PATTERNS.SEPARATORS).filter(Boolean)\r\n        .filter(num => num.length === 2);\r\n      if (!numbers.length) return null;\r\n      const money = match[2] ? chineseToNumber(match[2]) : '';\r\n      const groupMoney = match[3] ? chineseToNumber(match[3]) : '';\r\n      const groups = [];\r\n      if (money) {\r\n        groups.push({\r\n          methodId: METHOD_ID.LIANGMA_ZUHE,\r\n          betNumbers: numbers.join(','),\r\n          money: money,\r\n        });\r\n      }\r\n      if (groupMoney) {\r\n        groups.push({\r\n          methodId: METHOD_ID.LIANGMA_DUIZI,\r\n          betNumbers: numbers[numbers.length - 1],\r\n          money: groupMoney,\r\n        });\r\n      }\r\n      console.log('两码 handler 返回:', groups);\r\n      return groups;\r\n    }\r\n  },\r\n\r\n  // 两码组合+对子多号码+双金额，任意分隔符\r\n  // LIANGMA_ZUHE_DUIZI_MULTI_MONEY_SUPER: {\r\n  //   // 匹配：12 34 5+1、12/34/5+1、12-34-5+1等\r\n  //   pattern: /^([\\s\\S]+)$/m,\r\n  //   handler: (match, line) => {\r\n  //     const { SUPER_SEPARATORS } = require('./basePatterns');\r\n  //     const lines = line.split(/[\\r\\n]+/).map(l => l.trim()).filter(Boolean);\r\n  //     // 按玩法和金额分组合并，支持所有超级分隔符\r\n  //     const zhixuanMap = new Map();\r\n  //     const zuxuanMap = new Map();\r\n  //     for (const l of lines) {\r\n  //       // 匹配：3位号码+超级分隔符+金额1+超级分隔符+金额2\r\n  //       const m = l.match(new RegExp(`^(\\\\d{2})${SUPER_SEPARATORS.source}(\\\\d+)\\+(\\\\d+)$`));\r\n  //       if (m) {\r\n  //         const num = m[1];\r\n  //         const zhixuanMoney = m[2];\r\n  //         const zuxuanMoney = m[3];\r\n  //         // 合并直选\r\n  //         const zKey = zhixuanMoney;\r\n  //         if (!zhixuanMap.has(zKey)) zhixuanMap.set(zKey, []);\r\n  //         zhixuanMap.get(zKey).push(num);\r\n  //         // 合并组选\r\n  //         const zuKey = zuxuanMoney;\r\n  //         if (!zuxuanMap.has(zuKey)) zuxuanMap.set(zuKey, []);\r\n  //         zuxuanMap.get(zuKey).push(num);\r\n  //       }\r\n  //     }\r\n  //     const result = [];\r\n  //     for (const [money, nums] of zhixuanMap.entries()) {\r\n  //       result.push({\r\n  //         methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,\r\n  //         betNumbers: nums.join(','),\r\n  //         money,\r\n  //       });\r\n  //     }\r\n  //     for (const [money, nums] of zuxuanMap.entries()) {\r\n  //       result.push({\r\n  //         methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,\r\n  //         betNumbers: nums.join(','),\r\n  //         money,\r\n  //       });\r\n  //     }\r\n  //     return result.length ? result : null;\r\n  //   }\r\n  // },\r\n\r\n  // 两码组合（组字为组合，非对子）\r\n  LIANGMA_ZUHE_ZU_SUPER: {\r\n    // 匹配：12组30（只要有组字且不是对子）\r\n    pattern: /^(\\d{2})组(\\d+)$/,\r\n    handler: (match, line) => {\r\n      const numbers = match[1];\r\n      if (numbers.length !== 2) return null;\r\n      const money = match[2];\r\n      return [{\r\n        methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合\r\n        betNumbers: numbers,\r\n        money: money,\r\n      }];\r\n    }\r\n  },\r\n\r\n  // 两码组合多号码+金额，任意分隔符\r\n  LIANGMA_ZUHE_MULTI_SUPER: {\r\n    // 匹配：12-34赶50、12-34吊50、12/34/50、12-34赶50元、12-34赶50块、12-34赶50米等\r\n    pattern: /^(\\d{2}(?:[\\-~～@#￥%…&!、\\\\/*一－。,，.＝=· \\t+吊赶打各]+\\d{2})*)[\\-~～@#￥%…&!、\\\\/*一－。,，.＝=· \\t+吊赶打各]+(\\d+)(元|块|米)?$/,\r\n    handler: (match, line) => {\r\n      const { SUPER_SEPARATORS } = require('./basePatterns');\r\n      const allNumbers = match[1].split(SUPER_SEPARATORS).filter(Boolean);\r\n      if (!allNumbers.length || allNumbers.some(n => n.length !== 2)) return null;\r\n      const money = match[2];\r\n      // 分组\r\n      const zuhe = [];\r\n      const duizi = [];\r\n      allNumbers.forEach(num => {\r\n        if (num[0] === num[1]) {\r\n          duizi.push(num);\r\n        } else {\r\n          zuhe.push(num);\r\n        }\r\n      });\r\n      const result = [];\r\n      if (zuhe.length) {\r\n        result.push({\r\n          methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合\r\n          betNumbers: zuhe.join(','),\r\n          money: money,\r\n        });\r\n      }\r\n      if (duizi.length) {\r\n        result.push({\r\n          methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子\r\n          betNumbers: duizi.join(','),\r\n          money: money,\r\n        });\r\n      }\r\n      return result.length ? result : null;\r\n    }\r\n  },\r\n\r\n  // 两码对子（对子关键字）\r\n  LIANGMA_DUIZI_DUI_SUPER: {\r\n    // 匹配：12对子30\r\n    pattern: /^(\\d{2})对子(\\d+)$/,\r\n    handler: (match, line) => {\r\n      const numbers = match[1];\r\n      if (numbers.length !== 2) return null;\r\n      const money = match[2];\r\n      return [{\r\n        methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子\r\n        betNumbers: numbers,\r\n        money: money,\r\n      }];\r\n    }\r\n  },\r\n\r\n  // 新增：单号码+金额（支持所有分隔符）\r\n  LIANGMA_SINGLE_WITH_MONEY_SUPER: {\r\n    // 匹配：45,20 或 45，20 或 45 20 或 45、20 或 45+20 等任意分隔符\r\n    pattern: /^(\\d{2})[\\-~～@#￥%…&!、\\\\/*,，一－。.＝=·\\s+吊赶打各]+(\\d{1,4})$/,\r\n    handler: (match, line) => {\r\n      const num = match[1];\r\n      if (num.length !== 2) return null;\r\n      const money = match[2];\r\n      // 判断玩法\r\n      if (num[0] === num[1]) {\r\n        return [{\r\n          methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子\r\n          betNumbers: num,\r\n          money: money,\r\n        }];\r\n      } else {\r\n        return [{\r\n          methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合\r\n          betNumbers: num,\r\n          money: money,\r\n        }];\r\n      }\r\n    }\r\n  },\r\n\r\n  // 新增：多号码/金额（如45，28，18，52/20），兼容所有分隔符\r\n  LIANGMA_MULTI_WITH_MONEY_SLASH_SUPER: {\r\n    // 匹配：45，28，18，52/20 或 45 28 18 52/20\r\n    pattern: /^(\\d{2}(?:[\\-~～@#￥%…&!、\\\\/*,，.＝=一－。·\\s+吊赶打各]+\\d{2})*)\\/(\\d{1,4})$/,\r\n    handler: (match, line) => {\r\n      const { SUPER_SEPARATORS } = require('./basePatterns');\r\n      const allNumbers = match[1].split(SUPER_SEPARATORS).filter(Boolean);\r\n      if (!allNumbers.length || allNumbers.some(n => n.length !== 2)) return null;\r\n      const money = match[2];\r\n      // 分组\r\n      const zuhe = [];\r\n      const duizi = [];\r\n      allNumbers.forEach(num => {\r\n        if (num[0] === num[1]) {\r\n          duizi.push(num);\r\n        } else {\r\n          zuhe.push(num);\r\n        }\r\n      });\r\n      const result = [];\r\n      if (zuhe.length) {\r\n        result.push({\r\n          methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合\r\n          betNumbers: zuhe.join(','),\r\n          money: money,\r\n        });\r\n      }\r\n      if (duizi.length) {\r\n        result.push({\r\n          methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子\r\n          betNumbers: duizi.join(','),\r\n          money: money,\r\n        });\r\n      }\r\n      return result.length ? result : null;\r\n    }\r\n  },\r\n\r\n  // 新增：多号码/双金额（如45，28，18，52/20-5），兼容所有分隔符\r\n  LIANGMA_MULTI_WITH_DOUBLE_MONEY_SLASH_SUPER: {\r\n    // 匹配：45，28，18，52/20-5 或 45 28 18 52/20-5\r\n    pattern: /^(\\d{2}(?:[\\-~～@#￥%…&!、\\\\/*,，一－。.＝=·\\s+吊赶打各]+\\d{2})*)\\/(\\d+)-(\\d+)$/,\r\n    handler: (match, line) => {\r\n      const { SUPER_SEPARATORS } = require('./basePatterns');\r\n      const allNumbers = match[1].split(SUPER_SEPARATORS).filter(Boolean);\r\n      if (!allNumbers.length || allNumbers.some(n => n.length !== 2)) return null;\r\n      const numbers = allNumbers;\r\n      const zuheMoney = match[2];\r\n      const duiziMoney = match[3];\r\n      return [\r\n        {\r\n          methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合\r\n          betNumbers: numbers.join(','),\r\n          money: zuheMoney,\r\n        },\r\n        {\r\n          methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子\r\n          betNumbers: numbers.join(','),\r\n          money: duiziMoney,\r\n        }\r\n      ];\r\n    }\r\n  },\r\n\r\n  // 新增：两码组合带对子（如45-20防5）\r\n  LIANGMA_ZUHE_FANG: {\r\n    // 匹配：45-20防5\r\n    pattern: /^(\\d{2})-(\\d+)防(\\d+)$/,\r\n    handler: (match, line) => {\r\n      const numbers = match[1];\r\n      if (numbers.length !== 2) return null;\r\n      const zuheMoney = match[2];\r\n      const duiziMoney = match[3];\r\n      return [\r\n        {\r\n          methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合\r\n          betNumbers: numbers,\r\n          money: zuheMoney,\r\n        },\r\n        {\r\n          methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子\r\n          betNumbers: numbers,\r\n          money: duiziMoney,\r\n        }\r\n      ];\r\n    }\r\n  },\r\n  // 新增：多号码+组合+对子金额（任意分隔符，金额用/分隔，如 .../10+5）\r\n  LIANGMA_MULTI_ZUHE_DUIZI_SLASH_SUPER: {\r\n    // 匹配：大量号码/10+5，号码间任意分隔符\r\n    pattern: /^([\\d、,，.。\\\\/*\\s\\n\\r\\-~～@#￥%…&!、＝一－。=·吊赶打各]+)(?:\\/|各|\\s|吊|赶|打)(\\d+)\\+(\\d+)$/,\r\n    handler: (match, line) => {\r\n      const { SUPER_SEPARATORS } = require('./basePatterns');\r\n      const allNumbers = match[1].split(SUPER_SEPARATORS).filter(Boolean);\r\n      if (!allNumbers.length || allNumbers.some(n => n.length !== 2)) return null;\r\n      const numbers = allNumbers;\r\n      const zuheMoney = match[2];\r\n      const duiziMoney = match[3];\r\n      return [\r\n        {\r\n          methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合\r\n          betNumbers: numbers.join(','),\r\n          money: zuheMoney,\r\n        },\r\n        {\r\n          methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子\r\n          betNumbers: numbers.join(','),\r\n          money: duiziMoney,\r\n        }\r\n      ];\r\n    }\r\n  },\r\n\r\n  // 新增：多组号码+各X金额，支持超级分隔符\r\n  LIANGMA_MULTI_EACH_SUPER: {\r\n    // 匹配：12-34-56各100、12,34,56各100等\r\n    pattern: /^(\\d{2}(?:[\\-~～@#￥%…&!、\\\\/*,，.＝=一－。·\\s+吊赶打各]+\\d{2})*)各(\\d+)(元|块|米)?$/,\r\n    handler: (match, line) => {\r\n      const { SUPER_SEPARATORS } = require('./basePatterns');\r\n      const allNumbers = match[1].split(SUPER_SEPARATORS).filter(Boolean);\r\n      if (!allNumbers.length || allNumbers.some(n => n.length !== 2)) return null;\r\n      const money = match[2];\r\n      return [{\r\n        methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,\r\n        betNumbers: allNumbers.join(','),\r\n        money: money,\r\n      }];\r\n    }\r\n  },\r\n\r\n  // 新增：多个两码号码（超级分隔符分割）+/金额1/金额2，组合+对子\r\n  LIANGMA_MULTI_SUPER_SLASH_TWO_MONEY: {\r\n    pattern: /^([\\d、,，.。\\\\/*\\s\\n\\r\\-~～@#￥%…&!、一－。＝=·吊赶打各]+)\\/(\\d+)\\/(\\d+)$/,\r\n    handler: (match, line, strict = true) => {\r\n      const { SUPER_SEPARATORS } = require('./basePatterns');\r\n      const numbers = match[1].split(SUPER_SEPARATORS).map(n => n.trim()).filter(n => n.length === 2);\r\n      if (!numbers.length) return [];\r\n      const zuheMoney = match[2];\r\n      const duiziMoney = match[3];\r\n      // 严格金额长度判断：两码金额不能是2位数字，否则防误命中\r\n      if (strict && (/^\\d{2}$/.test(zuheMoney) || /^\\d{2}$/.test(duiziMoney))) return null;\r\n      return [\r\n        {\r\n          methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 2 两码组合\r\n          betNumbers: numbers.join(','),\r\n          money: zuheMoney,\r\n        },\r\n        {\r\n          methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 3 两码对子\r\n          betNumbers: numbers.join(','),\r\n          money: duiziMoney,\r\n        }\r\n      ];\r\n    }\r\n  },\r\n  // 新增：批量两码组合/金额/元块米，支持如\"08  /20 元\"，将金额一致的号码合并为一个下注单，要求每行必须有单位且至少两行\r\n  LIANGMA_MULTI_WITH_MONEY_UNIT_SUPER: {\r\n    // 全包，所有内容都进 handler\r\n    pattern: /^([\\s\\S]+)$/m,\r\n    handler: (match, line) => {\r\n      const lines = line.split(/\\n+/).map(l => l.trim()).filter(Boolean);\r\n      const groupMap = new Map();\r\n      let validLineCount = 0;\r\n      for (const l of lines) {\r\n        // 金额和单位之间允许有超级分隔符\r\n        const m = l.match(/(\\d{2})[\\s,，.。\\-—~～@#￥%…&!、\\\\/*]*\\/?[\\s,，.。\\-—~～@#￥%…&!、\\\\/*]*(\\d+)[\\s,，.。\\-—~～@#￥%…&!、\\\\/*]*(元|块|米)/);\r\n        if (m) {\r\n          validLineCount++;\r\n          const num = m[1];\r\n          const money = m[2];\r\n          if (!groupMap.has(money)) groupMap.set(money, []);\r\n          groupMap.get(money).push(num);\r\n        }\r\n      }\r\n      // 必须两行及以上有效行\r\n      if (validLineCount < 2) return null;\r\n      if (!groupMap.size) return null;\r\n      return Array.from(groupMap.entries()).map(([money, nums]) => ({\r\n        methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,\r\n        betNumbers: nums.join(','),\r\n        money: money,\r\n      }));\r\n    }\r\n  },\r\n  // 新增：通用格式，支持\"号码（任意超级分隔符）+金额（元/米/块/空）\"，自动识别为两码组合\r\n  LIANGMA_UNIVERSAL_SUPER: {\r\n    pattern: /^([\\d、,，.。\\\\/*\\s\\n\\r\\-~～@#￥%…&!一－—。、＝=·吊赶打各]+)[\\-~～@#￥%…&!、\\\\/*,，一－—。.＝=·吊赶打各]+([0-9]+)(元|米|块)?$/,\r\n    handler: (match, line) => {\r\n      const { SUPER_SEPARATORS, METHOD_ID } = require('./basePatterns');\r\n      // 支持多行批量\r\n      const lines = line.split(/\\n|\\r/).map(l => l.trim()).filter(Boolean);\r\n      // 合并玩法+金额一致的号码\r\n      const groupMap = new Map();\r\n      lines.forEach(l => {\r\n        const m = l.match(/^([\\d、,，.。\\\\/*\\s\\n\\r\\-~～@#￥%…&!一－—。、＝=·吊赶打各]+)[\\-~～@#￥%…&!、\\\\/*,，一－—。.＝=·吊赶打各]+([0-9]+)(元|米|块)?$/);\r\n        if (!m) return;\r\n        const numbers = m[1].split(SUPER_SEPARATORS).map(n => n.trim()).filter(n => n.length === 2);\r\n        const money = m[2];\r\n        numbers.forEach(num => {\r\n          let methodId = null;\r\n          if (num[0] === num[1]) {\r\n            methodId = METHOD_ID.LIANGMA_DUIZI;\r\n          } else {\r\n            methodId = METHOD_ID.LIANGMA_ZUHE;\r\n          }\r\n          const key = methodId + '_' + money;\r\n          if (!groupMap.has(key)) groupMap.set(key, []);\r\n          groupMap.get(key).push(num);\r\n        });\r\n      });\r\n      const result = [];\r\n      for (const [key, nums] of groupMap.entries()) {\r\n        const [methodId, money] = key.split('_');\r\n        result.push({\r\n          methodId: Number(methodId),\r\n          betNumbers: nums.join(','),\r\n          money: money,\r\n        });\r\n      }\r\n      return result.length ? result : null;\r\n    }\r\n  },\r\n\r\n  // 新增：12 34 各50+5\r\n  LIANGMA_EACH_PLUS_SUPER: {\r\n    // 匹配：12 34 各50+5\r\n    pattern: /^([\\d、,，.。\\\\/*\\s\\n\\r\\-~～@#￥%…&!一－。、＝=·吊赶打各]+)各([0-9]+)\\+([0-9]+)$/,\r\n    handler: (match, line) => {\r\n      const { SUPER_SEPARATORS, METHOD_ID } = require('./basePatterns');\r\n      const numbers = match[1].split(SUPER_SEPARATORS).map(n => n.trim()).filter(Boolean);\r\n      const money1 = match[2];\r\n      const money2 = match[3];\r\n      // 单组号码\r\n      if (numbers.length === 1 && numbers[0].length === 2) {\r\n        return [\r\n          {\r\n            methodId: METHOD_ID.LIANGMA_ZUHE,\r\n            betNumbers: numbers[0],\r\n            money: money1,\r\n          },\r\n          {\r\n            methodId: METHOD_ID.LIANGMA_DUIZI,\r\n            betNumbers: numbers[0],\r\n            money: money2,\r\n          }\r\n        ];\r\n      }\r\n      // 多组号码\r\n      if (numbers.length >= 2 && numbers.every(n => n.length === 2)) {\r\n        return [\r\n          {\r\n            methodId: METHOD_ID.LIANGMA_ZUHE,\r\n            betNumbers: numbers.join(','),\r\n            money: money1,\r\n          },\r\n          {\r\n            methodId: METHOD_ID.LIANGMA_DUIZI,\r\n            betNumbers: numbers.join(','),\r\n            money: money2,\r\n          }\r\n        ];\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n\r\n  // 新增：纯数字+空格+金额整体金额格式\r\n  LIANGMA_UNIVERSAL_SUPER_NEW: {\r\n    pattern: /^([0-9]{2,})\\s+(\\d+)(元|块|米)?$/,\r\n    handler: (match, line) => {\r\n      const betNumbers = match[1];\r\n      const money = match[2];\r\n      // 只允许号码长度为2\r\n      if (!betNumbers || betNumbers.length !== 2) return null;\r\n      return [{\r\n        methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,\r\n        betNumbers,\r\n        money,\r\n      }];\r\n    }\r\n  },\r\n\r\n  // 新增：支持\"12345打90\"格式，两码时为两码组合。\r\n  LIANGMA_DA_MONEY: {\r\n    pattern: /^([0-9]{2})打(\\d+)(元|块|米)?$/,\r\n    handler: (match, line) => {\r\n      return [{\r\n        methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,\r\n        betNumbers: match[1],\r\n        money: match[2],\r\n      }];\r\n    }\r\n  },\r\n\r\n  // 新增：多号码/金额/加号金额（如 45 28 18 52/1+1）\r\n  LIANGMA_MULTI_WITH_MONEY_SLASH_PLUS: {\r\n    // 匹配：45 28 18 52/1+1，兼容/后有无空格\r\n    pattern: /^(\\d{2}(?:[\\s,，\\-]+?\\d{2})*)\\/\\s*(\\d+)\\+(\\d+)$/,\r\n    handler: (match, line) => {\r\n      const numbers = match[1].split(/[\\s,，\\-]+/).filter(Boolean);\r\n      const zuheMoney = match[2];\r\n      const duiziMoney = match[3];\r\n      return [\r\n        {\r\n          methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUHE,  // 3 两码组合\r\n          betNumbers: numbers.join(','),\r\n          money: zuheMoney,\r\n        },\r\n        {\r\n          methodId: require('./basePatterns').METHOD_ID.LIANGMA_DUIZI,  // 4 两码对子\r\n          betNumbers: numbers.join(','),\r\n          money: duiziMoney,\r\n        }\r\n      ];\r\n    }\r\n  },\r\n\r\n  // 新增：多行号码+汉字金额+加+汉字金额，金额一致才合并，否则分组\r\n  LIANGMA_MULTI_LINE_MONEY_PLUS_CN: {\r\n    // 适配如：23十加五\\n45十加五\r\n    pattern: /^((?:\\d{2}[，,、]?[一二三四五六七八九十百千万\\d]+加[一二三四五六七八九十百千万\\d]+\\s*\\n?)+)$/m,\r\n    handler: (match, line) => {\r\n      const { METHOD_ID, chineseToNumber } = require('./basePatterns');\r\n\r\n      const lines = line.split(/\\n/).map(l => l.trim()).filter(Boolean);\r\n      const groupMap = new Map();\r\n      const unmatchedLines = [];\r\n      for (const l of lines) {\r\n        const m = l.match(/^(\\d{2})[，,、]?([一二三四五六七八九十百千万\\d]+)加([一二三四五六七八九十百千万\\d]+)$/);\r\n        if (m) {\r\n          const num = m[1];\r\n          const zuhe = chineseToNumber(m[2]);\r\n          const duizi = chineseToNumber(m[3]);\r\n          const moneyKey = `${zuhe}+${duizi}`;\r\n          if (!groupMap.has(moneyKey)) groupMap.set(moneyKey, []);\r\n          groupMap.get(moneyKey).push(num);\r\n        } else {\r\n          unmatchedLines.push(l);\r\n        }\r\n      }\r\n      const result = [];\r\n      for (const [moneyKey, numbers] of groupMap.entries()) {\r\n        const [zuheMoney, duiziMoney] = moneyKey.split('+');\r\n        result.push({\r\n          methodId: METHOD_ID.LIANGMA_ZUHE,\r\n          betNumbers: numbers.join(','),\r\n          money: zuheMoney,\r\n        });\r\n        result.push({\r\n          methodId: METHOD_ID.LIANGMA_DUIZI,\r\n          betNumbers: numbers.join(','),\r\n          money: duiziMoney,\r\n        });\r\n      }\r\n\r\n      return result.length ? result : null;\r\n    }\r\n  },\r\n\r\n  // 新增：对33/5，解析为两码对子，号码33，金额5元\r\n  DUIZI_SLASH_MONEY: {\r\n    pattern: /^对(\\d{2})\\/(\\d+)$/,\r\n    handler: (match, line) => {\r\n      return [{\r\n        methodId: METHOD_ID.LIANGMA_DUIZI,\r\n        betNumbers: match[1],\r\n        money: match[2],\r\n      }];\r\n    }\r\n  },\r\n\r\n  // 新增：69一29一个10元，两码组合10元\r\n  ERMA_YI_YI_ONE_MONEY: {\r\n    // 匹配：69一29一个10元\r\n    pattern: /^(\\d{2})一(\\d{2})一个(\\d+)(元|块|米)?$/,\r\n    handler: (match, line) => {\r\n      const num1 = match[1];\r\n      const num2 = match[2];\r\n      const money = match[3];\r\n      return [{\r\n        methodId: require('./basePatterns').METHOD_ID.ERMA_COMBO, // 两码组合\r\n        betNumbers: num1 + ',' + num2,\r\n        money,\r\n      }];\r\n    }\r\n  },\r\n  //  // 修正：排...各X元，批量组选（去掉\"组的\"两个字）\r\n  //  LIANGMA_PAI_EACH_MONEY_ZUXUAN: {\r\n  //   // 匹配：排07，28，08，...各5元 或 排27，02，38，...组的各5元\r\n  //   pattern: /^([\\d，,、\\s]+)(组的|组)?各(\\d+)(元|块|米)?[，,./\\*\\-\\=。·、\\s]*$/,\r\n  //   handler: (match, line) => {\r\n  //     const numbers = match[1].split(/[，,、\\s]+/).map(n => n.trim()).filter(n => n.length === 2);\r\n  //     const money = match[3];\r\n  //     if (!numbers.length) return null;\r\n  //     return [{\r\n  //       methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZUXUAN, //  2码组选\r\n  //       betNumbers: numbers.join(','),\r\n  //       money,\r\n  //     }];\r\n  //   }\r\n  // },\r\n  //  // 修正：排...各X元，批量组选（去掉\"组的\"两个字）\r\n  //  LIANGMA_PAI_EACH_MONEY_ZHIXUAN: {\r\n  //   // 匹配：排02，08，08，...各5元 或 排07，02，03，...组的各5元\r\n  //   pattern: /^([\\d，,、\\s]+)(直的|直|值的|值)?各(\\d+)(元|块|米)?[，,./\\*\\-\\=。·、\\s]*$/,\r\n  //   handler: (match, line) => {\r\n  //     const numbers = match[1].split(/[，,、\\s]+/).map(n => n.trim()).filter(n => n.length === 2);\r\n  //     const money = match[3];\r\n  //     if (!numbers.length) return null;\r\n  //     return [{\r\n  //       methodId: require('./basePatterns').METHOD_ID.LIANGMA_ZHIXUAN, //  2码组选\r\n  //       betNumbers: numbers.join(','),\r\n  //       money,\r\n  //     }];\r\n  //   }\r\n  // },\r\n\r\n  LIANGMA_MULTI_SINGLE_WITH_MONEY_FEN_SUPER: {\r\n    // 全兜底，所有内容都交给 handler\r\n    pattern: /^([\\s\\S]+)$/m,\r\n    handler: (match, line) => {\r\n      // 1. 必须结尾有\"分\"字\r\n      if (!/分\\s*$/.test(line)) return null;\r\n  \r\n      // 2. 预处理：去掉结尾\"分\"字和多余空白\r\n      line = line.replace(/分\\s*$/m, '').replace(/[\\r\\n]+/g, ' ').trim();\r\n  \r\n      // 3. 用正则全局提取所有\"号码-金额\"对\r\n      const reg = /(\\d{2})[\\-—~～@#￥%…&!、\\\\/*,，.＝=·吊赶打各\\s]+(\\d{2,3})(?=[\\-—~～@#￥%…&!、\\\\/*,，.＝=·吊赶打各\\s]|$)/g;\r\n      let m, groupMap = new Map();\r\n      let matchArr = [];\r\n      let replaced = line;\r\n      while ((m = reg.exec(line)) !== null) {\r\n        matchArr.push({ num: m[1], money: m[2], raw: m[0] });\r\n        replaced = replaced.replace(m[0], '@@');\r\n        if (!groupMap.has(m[2])) groupMap.set(m[2], []);\r\n        groupMap.get(m[2]).push(m[1]);\r\n      }\r\n  \r\n      // 4. 必须至少有一组，且第一组号码必须是两位数\r\n      if (matchArr.length === 0) return null;\r\n      if (!/^\\d{2}$/.test(matchArr[0].num)) return null;\r\n  \r\n      // 5. 剩余内容只能是分隔符\r\n      if (replaced.replace(/[@\\-—~～@#￥%…&!、\\\\/*,，.＝=·吊赶打各\\s]/g, '').length > 0) return null;\r\n  \r\n      // 6. 输出分组\r\n      const METHOD_ID = require('./basePatterns').METHOD_ID;\r\n      return Array.from(groupMap.entries()).map(([money, nums]) => ({\r\n        methodId: METHOD_ID.LIANGMA_MULTI_SINGLE_WITH_MONEY_FEN_SUPER || 3,\r\n        betNumbers: nums.join(','),\r\n        money,\r\n      }));\r\n    }\r\n  }\r\n\r\n\r\n\r\n};\r\n\r\n// 批量处理所有pattern属性，结尾加超级分隔符，类型安全判断\r\nObject.keys(TWO_CODE_PATTERNS).forEach(key => {\r\n  const pattern = TWO_CODE_PATTERNS[key].pattern;\r\n  if (typeof pattern === 'string' && pattern.endsWith('$')) {\r\n    TWO_CODE_PATTERNS[key].pattern = pattern.replace(/\\$$/, '[，,./\\*\\-\\=。·、\\s]*$');\r\n  } else if (pattern instanceof RegExp && pattern.source.endsWith('$')) {\r\n    TWO_CODE_PATTERNS[key].pattern = new RegExp(pattern.source.replace(/\\$$/, '[，,./\\*\\-\\=。·、\\s]*$'));\r\n  }\r\n}); "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAA,aAAA,GAAAC,OAAA;AAFA;AACA;;AAGA;AACO,IAAMC,iBAAiB,GAAAC,OAAA,CAAAD,iBAAA,GAAG;EAC/B;EACAE,YAAY,EAAE;IACZ;IACAC,OAAO,EAAE,yDAAyD;IAClEC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAID,KAAK,CAAC,CAAC,CAAC,CAACE,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MACtC,IAAMC,GAAG,GAAGH,KAAK,CAAC,CAAC,CAAC;MACpB,IAAMI,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC;MACtB;MACA,IAAIG,GAAG,CAAC,CAAC,CAAC,KAAKA,GAAG,CAAC,CAAC,CAAC,EAAE;QACrB,OAAO,IAAI;MACb;MACA,OAAO,CAAC;QACNE,QAAQ,EAAEC,uBAAS,CAACT,YAAY;QAAG;QACnCU,UAAU,EAAEJ,GAAG;QACfC,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EAED;EACAI,aAAa,EAAE;IACb;IACAV,OAAO,EAAE,2FAA2F;IACpGC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAID,KAAK,CAAC,CAAC,CAAC,CAACE,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MACtC,IAAMC,GAAG,GAAGH,KAAK,CAAC,CAAC,CAAC;MACpB,IAAMI,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC;MACtB;MACA,IAAIG,GAAG,CAAC,CAAC,CAAC,KAAKA,GAAG,CAAC,CAAC,CAAC,EAAE;QACrB,OAAO,IAAI;MACb;MACA,OAAO,CAAC;QACNE,QAAQ,EAAEC,uBAAS,CAACE,aAAa;QAAG;QACpCD,UAAU,EAAEJ,GAAG;QACfC,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EAED;EACAK,iBAAiB,EAAE;IACjB;IACAX,OAAO,EAAE,yFAAyF;IAClGC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAID,KAAK,CAAC,CAAC,CAAC,CAACE,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MACtC,IAAMC,GAAG,GAAGH,KAAK,CAAC,CAAC,CAAC;MACpB,IAAMI,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC;MACtB,IAAMU,IAAI,GAAGV,KAAK,CAAC,CAAC,CAAC;MACrB;MACA,IAAIG,GAAG,CAAC,CAAC,CAAC,KAAKA,GAAG,CAAC,CAAC,CAAC,EAAE;QACrB,OAAO,IAAI;MACb;MACA,IAAMQ,MAAM,GAAG;QACbN,QAAQ,EAAEC,uBAAS,CAACE,aAAa;QAAG;QACpCD,UAAU,EAAEJ,GAAG;QACfC,KAAK,EAAEA;MACT,CAAC;MACD,IAAIM,IAAI,EAAEC,MAAM,CAACD,IAAI,GAAGA,IAAI;MAC5B,OAAO,CAACC,MAAM,CAAC;IACjB;EACF,CAAC;EAED;EACAC,aAAa,EAAE;IACb;IACA;IACAd,OAAO,EAAE,oFAAoF;IAC7FC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB;MACA,IAAMY,UAAU,GAAGb,KAAK,CAAC,CAAC,CAAC,CAACc,IAAI,CAAC,CAAC;MAClC,IAAMV,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC;MACtB,IAAMU,IAAI,GAAGV,KAAK,CAAC,CAAC,CAAC;;MAErB;MACA,IAAMe,aAAa,GAAGF,UAAU,CAACb,KAAK,CAAC,QAAQ,CAAC;MAChD,IAAI,CAACe,aAAa,IAAIA,aAAa,CAACb,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;;MAE7D;MACA,IAAMc,cAAc,GAAG;QACrBC,OAAO,EAAEF,aAAa,CAACG,GAAG,CAAC,UAAAf,GAAG;UAAA,OAAK;YACjCgB,CAAC,EAAEhB,GAAG,CAAC,CAAC,CAAC;YACTiB,CAAC,EAAEjB,GAAG,CAAC,CAAC;UACV,CAAC;QAAA,CAAC;MACJ,CAAC;;MAED;MACA,IAAMQ,MAAM,GAAG;QACbN,QAAQ,EAAEC,uBAAS,CAACM,aAAa;QAAG;QACpCL,UAAU,EAAEc,IAAI,CAACC,SAAS,CAACN,cAAc,CAAC;QAC1CZ,KAAK,EAAEA;MACT,CAAC;MAED,IAAIM,IAAI,EAAEC,MAAM,CAACD,IAAI,GAAGA,IAAI;MAC5B,OAAO,CAACC,MAAM,CAAC;IACjB;EACF,CAAC;EAED;EACAY,kBAAkB,EAAE;IAClB;IACAzB,OAAO,EAAE,yCAAyC;IAClDC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAMgB,OAAO,GAAGhB,IAAI,CAACuB,KAAK,CAACC,2BAAa,CAACC,UAAU,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CACjED,MAAM,CAAC,UAAAxB,GAAG;QAAA,OAAIA,GAAG,CAACD,MAAM,KAAK,CAAC;MAAA,EAAC;MAClC,IAAM2B,UAAU,GAAG5B,IAAI,CAACuB,KAAK,CAACC,2BAAa,CAACC,UAAU,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;MACvE,IAAI,CAACC,UAAU,CAAC3B,MAAM,IAAI2B,UAAU,CAACC,IAAI,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC7B,MAAM,KAAK,CAAC;MAAA,EAAC,EAAE,OAAO,IAAI;MAC3E,IAAM8B,SAAS,GAAGhC,KAAK,CAAC,CAAC,CAAC;MAC1B,IAAMiC,UAAU,GAAGjC,KAAK,CAAC,CAAC,CAAC;MAE3B,OAAO,CACL;QACEK,QAAQ,EAAE,CAAC;QAAE;QACbE,UAAU,EAAEU,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC;QAC7B9B,KAAK,EAAE4B;MACT,CAAC,EACD;QACE3B,QAAQ,EAAE,CAAC;QAAE;QACbE,UAAU,EAAEU,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC;QAC7B9B,KAAK,EAAE6B;MACT,CAAC,CACF;IACH;EACF,CAAC;EAED;EACAE,aAAa,EAAE;IACb;IACArC,OAAO,EAAE,gFAAgF;IACzFC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAAmC,QAAA,GAA4B1C,OAAO,CAAC,gBAAgB,CAAC;QAA7C2C,eAAe,GAAAD,QAAA,CAAfC,eAAe;MACvB,IAAMpB,OAAO,GAAGhB,IAAI,CAACuB,KAAK,CAACC,2BAAa,CAACC,UAAU,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CACjED,MAAM,CAAC,UAAAxB,GAAG;QAAA,OAAIA,GAAG,CAACD,MAAM,KAAK,CAAC;MAAA,EAAC;MAClC,IAAI,CAACe,OAAO,CAACf,MAAM,EAAE,OAAO,IAAI;MAChC,IAAME,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC,GAAGqC,eAAe,CAACrC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;MACvD,IAAMsC,UAAU,GAAGtC,KAAK,CAAC,CAAC,CAAC,GAAGqC,eAAe,CAACrC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;MAC5D,IAAMuC,MAAM,GAAG,EAAE;MACjB,IAAInC,KAAK,EAAE;QACTmC,MAAM,CAACC,IAAI,CAAC;UACVnC,QAAQ,EAAEC,uBAAS,CAACT,YAAY;UAChCU,UAAU,EAAEU,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC;UAC7B9B,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ;MACA,IAAIkC,UAAU,EAAE;QACdC,MAAM,CAACC,IAAI,CAAC;UACVnC,QAAQ,EAAEC,uBAAS,CAACE,aAAa;UACjCD,UAAU,EAAEU,OAAO,CAACA,OAAO,CAACf,MAAM,GAAG,CAAC,CAAC;UACvCE,KAAK,EAAEkC;QACT,CAAC,CAAC;MACJ;MACAG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEH,MAAM,CAAC;MACrC,OAAOA,MAAM;IACf;EACF,CAAC;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACAI,qBAAqB,EAAE;IACrB;IACA7C,OAAO,EAAE,iBAAiB;IAC1BC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAMgB,OAAO,GAAGjB,KAAK,CAAC,CAAC,CAAC;MACxB,IAAIiB,OAAO,CAACf,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MACrC,IAAME,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC;MACtB,OAAO,CAAC;QACNK,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACT,YAAY;QAAG;QAC7DU,UAAU,EAAEU,OAAO;QACnBb,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EAED;EACAwC,wBAAwB,EAAE;IACxB;IACA9C,OAAO,EAAE,0GAA0G;IACnHC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAA4C,SAAA,GAA6BnD,OAAO,CAAC,gBAAgB,CAAC;QAA9CoD,gBAAgB,GAAAD,SAAA,CAAhBC,gBAAgB;MACxB,IAAMjB,UAAU,GAAG7B,KAAK,CAAC,CAAC,CAAC,CAACwB,KAAK,CAACsB,gBAAgB,CAAC,CAACnB,MAAM,CAACC,OAAO,CAAC;MACnE,IAAI,CAACC,UAAU,CAAC3B,MAAM,IAAI2B,UAAU,CAACC,IAAI,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC7B,MAAM,KAAK,CAAC;MAAA,EAAC,EAAE,OAAO,IAAI;MAC3E,IAAME,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC;MACtB;MACA,IAAM+C,IAAI,GAAG,EAAE;MACf,IAAMC,KAAK,GAAG,EAAE;MAChBnB,UAAU,CAACoB,OAAO,CAAC,UAAA9C,GAAG,EAAI;QACxB,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAKA,GAAG,CAAC,CAAC,CAAC,EAAE;UACrB6C,KAAK,CAACR,IAAI,CAACrC,GAAG,CAAC;QACjB,CAAC,MAAM;UACL4C,IAAI,CAACP,IAAI,CAACrC,GAAG,CAAC;QAChB;MACF,CAAC,CAAC;MACF,IAAMQ,MAAM,GAAG,EAAE;MACjB,IAAIoC,IAAI,CAAC7C,MAAM,EAAE;QACfS,MAAM,CAAC6B,IAAI,CAAC;UACVnC,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACT,YAAY;UAAG;UAC7DU,UAAU,EAAEwC,IAAI,CAACb,IAAI,CAAC,GAAG,CAAC;UAC1B9B,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ;MACA,IAAI4C,KAAK,CAAC9C,MAAM,EAAE;QAChBS,MAAM,CAAC6B,IAAI,CAAC;UACVnC,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACE,aAAa;UAAG;UAC9DD,UAAU,EAAEyC,KAAK,CAACd,IAAI,CAAC,GAAG,CAAC;UAC3B9B,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ;MACA,OAAOO,MAAM,CAACT,MAAM,GAAGS,MAAM,GAAG,IAAI;IACtC;EACF,CAAC;EAED;EACAuC,uBAAuB,EAAE;IACvB;IACApD,OAAO,EAAE,kBAAkB;IAC3BC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAMgB,OAAO,GAAGjB,KAAK,CAAC,CAAC,CAAC;MACxB,IAAIiB,OAAO,CAACf,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MACrC,IAAME,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC;MACtB,OAAO,CAAC;QACNK,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACE,aAAa;QAAG;QAC9DD,UAAU,EAAEU,OAAO;QACnBb,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EAED;EACA+C,+BAA+B,EAAE;IAC/B;IACArD,OAAO,EAAE,uDAAuD;IAChEC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAME,GAAG,GAAGH,KAAK,CAAC,CAAC,CAAC;MACpB,IAAIG,GAAG,CAACD,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MACjC,IAAME,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC;MACtB;MACA,IAAIG,GAAG,CAAC,CAAC,CAAC,KAAKA,GAAG,CAAC,CAAC,CAAC,EAAE;QACrB,OAAO,CAAC;UACNE,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACE,aAAa;UAAG;UAC9DD,UAAU,EAAEJ,GAAG;UACfC,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,OAAO,CAAC;UACNC,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACT,YAAY;UAAG;UAC7DU,UAAU,EAAEJ,GAAG;UACfC,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED;EACAgD,oCAAoC,EAAE;IACpC;IACAtD,OAAO,EAAE,mEAAmE;IAC5EC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAAoD,SAAA,GAA6B3D,OAAO,CAAC,gBAAgB,CAAC;QAA9CoD,gBAAgB,GAAAO,SAAA,CAAhBP,gBAAgB;MACxB,IAAMjB,UAAU,GAAG7B,KAAK,CAAC,CAAC,CAAC,CAACwB,KAAK,CAACsB,gBAAgB,CAAC,CAACnB,MAAM,CAACC,OAAO,CAAC;MACnE,IAAI,CAACC,UAAU,CAAC3B,MAAM,IAAI2B,UAAU,CAACC,IAAI,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC7B,MAAM,KAAK,CAAC;MAAA,EAAC,EAAE,OAAO,IAAI;MAC3E,IAAME,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC;MACtB;MACA,IAAM+C,IAAI,GAAG,EAAE;MACf,IAAMC,KAAK,GAAG,EAAE;MAChBnB,UAAU,CAACoB,OAAO,CAAC,UAAA9C,GAAG,EAAI;QACxB,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAKA,GAAG,CAAC,CAAC,CAAC,EAAE;UACrB6C,KAAK,CAACR,IAAI,CAACrC,GAAG,CAAC;QACjB,CAAC,MAAM;UACL4C,IAAI,CAACP,IAAI,CAACrC,GAAG,CAAC;QAChB;MACF,CAAC,CAAC;MACF,IAAMQ,MAAM,GAAG,EAAE;MACjB,IAAIoC,IAAI,CAAC7C,MAAM,EAAE;QACfS,MAAM,CAAC6B,IAAI,CAAC;UACVnC,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACT,YAAY;UAAG;UAC7DU,UAAU,EAAEwC,IAAI,CAACb,IAAI,CAAC,GAAG,CAAC;UAC1B9B,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ;MACA,IAAI4C,KAAK,CAAC9C,MAAM,EAAE;QAChBS,MAAM,CAAC6B,IAAI,CAAC;UACVnC,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACE,aAAa;UAAG;UAC9DD,UAAU,EAAEyC,KAAK,CAACd,IAAI,CAAC,GAAG,CAAC;UAC3B9B,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ;MACA,OAAOO,MAAM,CAACT,MAAM,GAAGS,MAAM,GAAG,IAAI;IACtC;EACF,CAAC;EAED;EACA2C,2CAA2C,EAAE;IAC3C;IACAxD,OAAO,EAAE,qEAAqE;IAC9EC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAAsD,SAAA,GAA6B7D,OAAO,CAAC,gBAAgB,CAAC;QAA9CoD,gBAAgB,GAAAS,SAAA,CAAhBT,gBAAgB;MACxB,IAAMjB,UAAU,GAAG7B,KAAK,CAAC,CAAC,CAAC,CAACwB,KAAK,CAACsB,gBAAgB,CAAC,CAACnB,MAAM,CAACC,OAAO,CAAC;MACnE,IAAI,CAACC,UAAU,CAAC3B,MAAM,IAAI2B,UAAU,CAACC,IAAI,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC7B,MAAM,KAAK,CAAC;MAAA,EAAC,EAAE,OAAO,IAAI;MAC3E,IAAMe,OAAO,GAAGY,UAAU;MAC1B,IAAMG,SAAS,GAAGhC,KAAK,CAAC,CAAC,CAAC;MAC1B,IAAMiC,UAAU,GAAGjC,KAAK,CAAC,CAAC,CAAC;MAC3B,OAAO,CACL;QACEK,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACT,YAAY;QAAG;QAC7DU,UAAU,EAAEU,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC;QAC7B9B,KAAK,EAAE4B;MACT,CAAC,EACD;QACE3B,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACE,aAAa;QAAG;QAC9DD,UAAU,EAAEU,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC;QAC7B9B,KAAK,EAAE6B;MACT,CAAC,CACF;IACH;EACF,CAAC;EAED;EACAuB,iBAAiB,EAAE;IACjB;IACA1D,OAAO,EAAE,uBAAuB;IAChCC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAMgB,OAAO,GAAGjB,KAAK,CAAC,CAAC,CAAC;MACxB,IAAIiB,OAAO,CAACf,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MACrC,IAAM8B,SAAS,GAAGhC,KAAK,CAAC,CAAC,CAAC;MAC1B,IAAMiC,UAAU,GAAGjC,KAAK,CAAC,CAAC,CAAC;MAC3B,OAAO,CACL;QACEK,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACT,YAAY;QAAG;QAC7DU,UAAU,EAAEU,OAAO;QACnBb,KAAK,EAAE4B;MACT,CAAC,EACD;QACE3B,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACE,aAAa;QAAG;QAC9DD,UAAU,EAAEU,OAAO;QACnBb,KAAK,EAAE6B;MACT,CAAC,CACF;IACH;EACF,CAAC;EACD;EACAwB,oCAAoC,EAAE;IACpC;IACA3D,OAAO,EAAE,6EAA6E;IACtFC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAAyD,SAAA,GAA6BhE,OAAO,CAAC,gBAAgB,CAAC;QAA9CoD,gBAAgB,GAAAY,SAAA,CAAhBZ,gBAAgB;MACxB,IAAMjB,UAAU,GAAG7B,KAAK,CAAC,CAAC,CAAC,CAACwB,KAAK,CAACsB,gBAAgB,CAAC,CAACnB,MAAM,CAACC,OAAO,CAAC;MACnE,IAAI,CAACC,UAAU,CAAC3B,MAAM,IAAI2B,UAAU,CAACC,IAAI,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC7B,MAAM,KAAK,CAAC;MAAA,EAAC,EAAE,OAAO,IAAI;MAC3E,IAAMe,OAAO,GAAGY,UAAU;MAC1B,IAAMG,SAAS,GAAGhC,KAAK,CAAC,CAAC,CAAC;MAC1B,IAAMiC,UAAU,GAAGjC,KAAK,CAAC,CAAC,CAAC;MAC3B,OAAO,CACL;QACEK,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACT,YAAY;QAAG;QAC7DU,UAAU,EAAEU,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC;QAC7B9B,KAAK,EAAE4B;MACT,CAAC,EACD;QACE3B,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACE,aAAa;QAAG;QAC9DD,UAAU,EAAEU,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC;QAC7B9B,KAAK,EAAE6B;MACT,CAAC,CACF;IACH;EACF,CAAC;EAED;EACA0B,wBAAwB,EAAE;IACxB;IACA7D,OAAO,EAAE,sEAAsE;IAC/EC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAA2D,SAAA,GAA6BlE,OAAO,CAAC,gBAAgB,CAAC;QAA9CoD,gBAAgB,GAAAc,SAAA,CAAhBd,gBAAgB;MACxB,IAAMjB,UAAU,GAAG7B,KAAK,CAAC,CAAC,CAAC,CAACwB,KAAK,CAACsB,gBAAgB,CAAC,CAACnB,MAAM,CAACC,OAAO,CAAC;MACnE,IAAI,CAACC,UAAU,CAAC3B,MAAM,IAAI2B,UAAU,CAACC,IAAI,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC7B,MAAM,KAAK,CAAC;MAAA,EAAC,EAAE,OAAO,IAAI;MAC3E,IAAME,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC;MACtB,OAAO,CAAC;QACNK,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACT,YAAY;QAC1DU,UAAU,EAAEsB,UAAU,CAACK,IAAI,CAAC,GAAG,CAAC;QAChC9B,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EAED;EACAyD,mCAAmC,EAAE;IACnC/D,OAAO,EAAE,8DAA8D;IACvEC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAoB;MAAA,IAAlB6D,MAAM,GAAAC,SAAA,CAAA7D,MAAA,QAAA6D,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;MAClC,IAAAE,SAAA,GAA6BvE,OAAO,CAAC,gBAAgB,CAAC;QAA9CoD,gBAAgB,GAAAmB,SAAA,CAAhBnB,gBAAgB;MACxB,IAAM7B,OAAO,GAAGjB,KAAK,CAAC,CAAC,CAAC,CAACwB,KAAK,CAACsB,gBAAgB,CAAC,CAAC5B,GAAG,CAAC,UAAAa,CAAC;QAAA,OAAIA,CAAC,CAACjB,IAAI,CAAC,CAAC;MAAA,EAAC,CAACa,MAAM,CAAC,UAAAI,CAAC;QAAA,OAAIA,CAAC,CAAC7B,MAAM,KAAK,CAAC;MAAA,EAAC;MAC/F,IAAI,CAACe,OAAO,CAACf,MAAM,EAAE,OAAO,EAAE;MAC9B,IAAM8B,SAAS,GAAGhC,KAAK,CAAC,CAAC,CAAC;MAC1B,IAAMiC,UAAU,GAAGjC,KAAK,CAAC,CAAC,CAAC;MAC3B;MACA,IAAI8D,MAAM,KAAK,SAAS,CAACI,IAAI,CAAClC,SAAS,CAAC,IAAI,SAAS,CAACkC,IAAI,CAACjC,UAAU,CAAC,CAAC,EAAE,OAAO,IAAI;MACpF,OAAO,CACL;QACE5B,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACT,YAAY;QAAG;QAC7DU,UAAU,EAAEU,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC;QAC7B9B,KAAK,EAAE4B;MACT,CAAC,EACD;QACE3B,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACE,aAAa;QAAG;QAC9DD,UAAU,EAAEU,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC;QAC7B9B,KAAK,EAAE6B;MACT,CAAC,CACF;IACH;EACF,CAAC;EACD;EACAkC,mCAAmC,EAAE;IACnC;IACArE,OAAO,EAAE,cAAc;IACvBC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAMmE,KAAK,GAAGnE,IAAI,CAACuB,KAAK,CAAC,KAAK,CAAC,CAACN,GAAG,CAAC,UAAAmD,CAAC;QAAA,OAAIA,CAAC,CAACvD,IAAI,CAAC,CAAC;MAAA,EAAC,CAACa,MAAM,CAACC,OAAO,CAAC;MAClE,IAAM0C,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC1B,IAAIC,cAAc,GAAG,CAAC;MAAC,IAAAC,SAAA,OAAAC,2BAAA,CAAAC,OAAA,EACPP,KAAK;QAAAQ,KAAA;MAAA;QAArB,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAA1C,CAAA,IAAA+C,IAAA,GAAuB;UAAA,IAAZT,CAAC,GAAAO,KAAA,CAAAG,KAAA;UACV;UACA,IAAMC,CAAC,GAAGX,CAAC,CAACrE,KAAK,CAAC,sGAAsG,CAAC;UACzH,IAAIgF,CAAC,EAAE;YACLR,cAAc,EAAE;YAChB,IAAMrE,GAAG,GAAG6E,CAAC,CAAC,CAAC,CAAC;YAChB,IAAM5E,KAAK,GAAG4E,CAAC,CAAC,CAAC,CAAC;YAClB,IAAI,CAACV,QAAQ,CAACW,GAAG,CAAC7E,KAAK,CAAC,EAAEkE,QAAQ,CAACY,GAAG,CAAC9E,KAAK,EAAE,EAAE,CAAC;YACjDkE,QAAQ,CAACa,GAAG,CAAC/E,KAAK,CAAC,CAACoC,IAAI,CAACrC,GAAG,CAAC;UAC/B;QACF;QACA;MAAA,SAAAiF,GAAA;QAAAX,SAAA,CAAAY,CAAA,CAAAD,GAAA;MAAA;QAAAX,SAAA,CAAAa,CAAA;MAAA;MACA,IAAId,cAAc,GAAG,CAAC,EAAE,OAAO,IAAI;MACnC,IAAI,CAACF,QAAQ,CAACiB,IAAI,EAAE,OAAO,IAAI;MAC/B,OAAOC,KAAK,CAACC,IAAI,CAACnB,QAAQ,CAACoB,OAAO,CAAC,CAAC,CAAC,CAACxE,GAAG,CAAC,UAAAyE,IAAA;QAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAlB,OAAA,EAAAgB,IAAA;UAAEvF,KAAK,GAAAwF,KAAA;UAAEE,IAAI,GAAAF,KAAA;QAAA,OAAO;UAC5DvF,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACT,YAAY;UAC1DU,UAAU,EAAEuF,IAAI,CAAC5D,IAAI,CAAC,GAAG,CAAC;UAC1B9B,KAAK,EAAEA;QACT,CAAC;MAAA,CAAC,CAAC;IACL;EACF,CAAC;EACD;EACA2F,uBAAuB,EAAE;IACvBjG,OAAO,EAAE,kGAAkG;IAC3GC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAA+F,SAAA,GAAwCtG,OAAO,CAAC,gBAAgB,CAAC;QAAzDoD,gBAAgB,GAAAkD,SAAA,CAAhBlD,gBAAgB;QAAExC,SAAS,GAAA0F,SAAA,CAAT1F,SAAS;MACnC;MACA,IAAM8D,KAAK,GAAGnE,IAAI,CAACuB,KAAK,CAAC,OAAO,CAAC,CAACN,GAAG,CAAC,UAAAmD,CAAC;QAAA,OAAIA,CAAC,CAACvD,IAAI,CAAC,CAAC;MAAA,EAAC,CAACa,MAAM,CAACC,OAAO,CAAC;MACpE;MACA,IAAM0C,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC1BH,KAAK,CAACnB,OAAO,CAAC,UAAAoB,CAAC,EAAI;QACjB,IAAMW,CAAC,GAAGX,CAAC,CAACrE,KAAK,CAAC,kGAAkG,CAAC;QACrH,IAAI,CAACgF,CAAC,EAAE;QACR,IAAM/D,OAAO,GAAG+D,CAAC,CAAC,CAAC,CAAC,CAACxD,KAAK,CAACsB,gBAAgB,CAAC,CAAC5B,GAAG,CAAC,UAAAa,CAAC;UAAA,OAAIA,CAAC,CAACjB,IAAI,CAAC,CAAC;QAAA,EAAC,CAACa,MAAM,CAAC,UAAAI,CAAC;UAAA,OAAIA,CAAC,CAAC7B,MAAM,KAAK,CAAC;QAAA,EAAC;QAC3F,IAAME,KAAK,GAAG4E,CAAC,CAAC,CAAC,CAAC;QAClB/D,OAAO,CAACgC,OAAO,CAAC,UAAA9C,GAAG,EAAI;UACrB,IAAIE,QAAQ,GAAG,IAAI;UACnB,IAAIF,GAAG,CAAC,CAAC,CAAC,KAAKA,GAAG,CAAC,CAAC,CAAC,EAAE;YACrBE,QAAQ,GAAGC,SAAS,CAACE,aAAa;UACpC,CAAC,MAAM;YACLH,QAAQ,GAAGC,SAAS,CAACT,YAAY;UACnC;UACA,IAAMoG,GAAG,GAAG5F,QAAQ,GAAG,GAAG,GAAGD,KAAK;UAClC,IAAI,CAACkE,QAAQ,CAACW,GAAG,CAACgB,GAAG,CAAC,EAAE3B,QAAQ,CAACY,GAAG,CAACe,GAAG,EAAE,EAAE,CAAC;UAC7C3B,QAAQ,CAACa,GAAG,CAACc,GAAG,CAAC,CAACzD,IAAI,CAACrC,GAAG,CAAC;QAC7B,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAMQ,MAAM,GAAG,EAAE;MAAC,IAAAuF,UAAA,OAAAxB,2BAAA,CAAAC,OAAA,EACQL,QAAQ,CAACoB,OAAO,CAAC,CAAC;QAAAS,MAAA;MAAA;QAA5C,KAAAD,UAAA,CAAArB,CAAA,MAAAsB,MAAA,GAAAD,UAAA,CAAAnE,CAAA,IAAA+C,IAAA,GAA8C;UAAA,IAAAsB,YAAA,OAAAP,eAAA,CAAAlB,OAAA,EAAAwB,MAAA,CAAApB,KAAA;YAAlCkB,GAAG,GAAAG,YAAA;YAAEN,IAAI,GAAAM,YAAA;UACnB,IAAAC,UAAA,GAA0BJ,GAAG,CAACzE,KAAK,CAAC,GAAG,CAAC;YAAA8E,WAAA,OAAAT,eAAA,CAAAlB,OAAA,EAAA0B,UAAA;YAAjChG,QAAQ,GAAAiG,WAAA;YAAElG,KAAK,GAAAkG,WAAA;UACtB3F,MAAM,CAAC6B,IAAI,CAAC;YACVnC,QAAQ,EAAEkG,MAAM,CAAClG,QAAQ,CAAC;YAC1BE,UAAU,EAAEuF,IAAI,CAAC5D,IAAI,CAAC,GAAG,CAAC;YAC1B9B,KAAK,EAAEA;UACT,CAAC,CAAC;QACJ;MAAC,SAAAgF,GAAA;QAAAc,UAAA,CAAAb,CAAA,CAAAD,GAAA;MAAA;QAAAc,UAAA,CAAAZ,CAAA;MAAA;MACD,OAAO3E,MAAM,CAACT,MAAM,GAAGS,MAAM,GAAG,IAAI;IACtC;EACF,CAAC;EAED;EACA6F,uBAAuB,EAAE;IACvB;IACA1G,OAAO,EAAE,mEAAmE;IAC5EC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAAwG,SAAA,GAAwC/G,OAAO,CAAC,gBAAgB,CAAC;QAAzDoD,gBAAgB,GAAA2D,SAAA,CAAhB3D,gBAAgB;QAAExC,SAAS,GAAAmG,SAAA,CAATnG,SAAS;MACnC,IAAMW,OAAO,GAAGjB,KAAK,CAAC,CAAC,CAAC,CAACwB,KAAK,CAACsB,gBAAgB,CAAC,CAAC5B,GAAG,CAAC,UAAAa,CAAC;QAAA,OAAIA,CAAC,CAACjB,IAAI,CAAC,CAAC;MAAA,EAAC,CAACa,MAAM,CAACC,OAAO,CAAC;MACnF,IAAM8E,MAAM,GAAG1G,KAAK,CAAC,CAAC,CAAC;MACvB,IAAM2G,MAAM,GAAG3G,KAAK,CAAC,CAAC,CAAC;MACvB;MACA,IAAIiB,OAAO,CAACf,MAAM,KAAK,CAAC,IAAIe,OAAO,CAAC,CAAC,CAAC,CAACf,MAAM,KAAK,CAAC,EAAE;QACnD,OAAO,CACL;UACEG,QAAQ,EAAEC,SAAS,CAACT,YAAY;UAChCU,UAAU,EAAEU,OAAO,CAAC,CAAC,CAAC;UACtBb,KAAK,EAAEsG;QACT,CAAC,EACD;UACErG,QAAQ,EAAEC,SAAS,CAACE,aAAa;UACjCD,UAAU,EAAEU,OAAO,CAAC,CAAC,CAAC;UACtBb,KAAK,EAAEuG;QACT,CAAC,CACF;MACH;MACA;MACA,IAAI1F,OAAO,CAACf,MAAM,IAAI,CAAC,IAAIe,OAAO,CAAC2F,KAAK,CAAC,UAAA7E,CAAC;QAAA,OAAIA,CAAC,CAAC7B,MAAM,KAAK,CAAC;MAAA,EAAC,EAAE;QAC7D,OAAO,CACL;UACEG,QAAQ,EAAEC,SAAS,CAACT,YAAY;UAChCU,UAAU,EAAEU,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC;UAC7B9B,KAAK,EAAEsG;QACT,CAAC,EACD;UACErG,QAAQ,EAAEC,SAAS,CAACE,aAAa;UACjCD,UAAU,EAAEU,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC;UAC7B9B,KAAK,EAAEuG;QACT,CAAC,CACF;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC;EAED;EACAE,2BAA2B,EAAE;IAC3B/G,OAAO,EAAE,+BAA+B;IACxCC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAMM,UAAU,GAAGP,KAAK,CAAC,CAAC,CAAC;MAC3B,IAAMI,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC;MACtB;MACA,IAAI,CAACO,UAAU,IAAIA,UAAU,CAACL,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MACvD,OAAO,CAAC;QACNG,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACT,YAAY;QAC1DU,UAAU,EAAVA,UAAU;QACVH,KAAK,EAALA;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EAED;EACA0G,gBAAgB,EAAE;IAChBhH,OAAO,EAAE,4BAA4B;IACrCC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,OAAO,CAAC;QACNI,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACT,YAAY;QAC1DU,UAAU,EAAEP,KAAK,CAAC,CAAC,CAAC;QACpBI,KAAK,EAAEJ,KAAK,CAAC,CAAC;MAChB,CAAC,CAAC;IACJ;EACF,CAAC;EAED;EACA+G,mCAAmC,EAAE;IACnC;IACAjH,OAAO,EAAE,gDAAgD;IACzDC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAMgB,OAAO,GAAGjB,KAAK,CAAC,CAAC,CAAC,CAACwB,KAAK,CAAC,WAAW,CAAC,CAACG,MAAM,CAACC,OAAO,CAAC;MAC3D,IAAMI,SAAS,GAAGhC,KAAK,CAAC,CAAC,CAAC;MAC1B,IAAMiC,UAAU,GAAGjC,KAAK,CAAC,CAAC,CAAC;MAC3B,OAAO,CACL;QACEK,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACT,YAAY;QAAG;QAC7DU,UAAU,EAAEU,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC;QAC7B9B,KAAK,EAAE4B;MACT,CAAC,EACD;QACE3B,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAACE,aAAa;QAAG;QAC9DD,UAAU,EAAEU,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC;QAC7B9B,KAAK,EAAE6B;MACT,CAAC,CACF;IACH;EACF,CAAC;EAED;EACA+E,gCAAgC,EAAE;IAChC;IACAlH,OAAO,EAAE,kEAAkE;IAC3EC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAAgH,SAAA,GAAuCvH,OAAO,CAAC,gBAAgB,CAAC;QAAxDY,SAAS,GAAA2G,SAAA,CAAT3G,SAAS;QAAE+B,eAAe,GAAA4E,SAAA,CAAf5E,eAAe;MAElC,IAAM+B,KAAK,GAAGnE,IAAI,CAACuB,KAAK,CAAC,IAAI,CAAC,CAACN,GAAG,CAAC,UAAAmD,CAAC;QAAA,OAAIA,CAAC,CAACvD,IAAI,CAAC,CAAC;MAAA,EAAC,CAACa,MAAM,CAACC,OAAO,CAAC;MACjE,IAAM0C,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC1B,IAAM2C,cAAc,GAAG,EAAE;MAAC,IAAAC,UAAA,OAAAzC,2BAAA,CAAAC,OAAA,EACVP,KAAK;QAAAgD,MAAA;MAAA;QAArB,KAAAD,UAAA,CAAAtC,CAAA,MAAAuC,MAAA,GAAAD,UAAA,CAAApF,CAAA,IAAA+C,IAAA,GAAuB;UAAA,IAAZT,CAAC,GAAA+C,MAAA,CAAArC,KAAA;UACV,IAAMC,CAAC,GAAGX,CAAC,CAACrE,KAAK,CAAC,0DAA0D,CAAC;UAC7E,IAAIgF,CAAC,EAAE;YACL,IAAM7E,GAAG,GAAG6E,CAAC,CAAC,CAAC,CAAC;YAChB,IAAMjC,IAAI,GAAGV,eAAe,CAAC2C,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,IAAMhC,KAAK,GAAGX,eAAe,CAAC2C,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,IAAMqC,QAAQ,MAAAC,MAAA,CAAMvE,IAAI,OAAAuE,MAAA,CAAItE,KAAK,CAAE;YACnC,IAAI,CAACsB,QAAQ,CAACW,GAAG,CAACoC,QAAQ,CAAC,EAAE/C,QAAQ,CAACY,GAAG,CAACmC,QAAQ,EAAE,EAAE,CAAC;YACvD/C,QAAQ,CAACa,GAAG,CAACkC,QAAQ,CAAC,CAAC7E,IAAI,CAACrC,GAAG,CAAC;UAClC,CAAC,MAAM;YACL+G,cAAc,CAAC1E,IAAI,CAAC6B,CAAC,CAAC;UACxB;QACF;MAAC,SAAAe,GAAA;QAAA+B,UAAA,CAAA9B,CAAA,CAAAD,GAAA;MAAA;QAAA+B,UAAA,CAAA7B,CAAA;MAAA;MACD,IAAM3E,MAAM,GAAG,EAAE;MAAC,IAAA4G,UAAA,OAAA7C,2BAAA,CAAAC,OAAA,EACgBL,QAAQ,CAACoB,OAAO,CAAC,CAAC;QAAA8B,MAAA;MAAA;QAApD,KAAAD,UAAA,CAAA1C,CAAA,MAAA2C,MAAA,GAAAD,UAAA,CAAAxF,CAAA,IAAA+C,IAAA,GAAsD;UAAA,IAAA2C,YAAA,OAAA5B,eAAA,CAAAlB,OAAA,EAAA6C,MAAA,CAAAzC,KAAA;YAA1CsC,SAAQ,GAAAI,YAAA;YAAExG,OAAO,GAAAwG,YAAA;UAC3B,IAAAC,eAAA,GAAgCL,SAAQ,CAAC7F,KAAK,CAAC,GAAG,CAAC;YAAAmG,gBAAA,OAAA9B,eAAA,CAAAlB,OAAA,EAAA+C,eAAA;YAA5C1F,SAAS,GAAA2F,gBAAA;YAAE1F,UAAU,GAAA0F,gBAAA;UAC5BhH,MAAM,CAAC6B,IAAI,CAAC;YACVnC,QAAQ,EAAEC,SAAS,CAACT,YAAY;YAChCU,UAAU,EAAEU,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC;YAC7B9B,KAAK,EAAE4B;UACT,CAAC,CAAC;UACFrB,MAAM,CAAC6B,IAAI,CAAC;YACVnC,QAAQ,EAAEC,SAAS,CAACE,aAAa;YACjCD,UAAU,EAAEU,OAAO,CAACiB,IAAI,CAAC,GAAG,CAAC;YAC7B9B,KAAK,EAAE6B;UACT,CAAC,CAAC;QACJ;MAAC,SAAAmD,GAAA;QAAAmC,UAAA,CAAAlC,CAAA,CAAAD,GAAA;MAAA;QAAAmC,UAAA,CAAAjC,CAAA;MAAA;MAED,OAAO3E,MAAM,CAACT,MAAM,GAAGS,MAAM,GAAG,IAAI;IACtC;EACF,CAAC;EAED;EACAiH,iBAAiB,EAAE;IACjB9H,OAAO,EAAE,mBAAmB;IAC5BC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,OAAO,CAAC;QACNI,QAAQ,EAAEC,uBAAS,CAACE,aAAa;QACjCD,UAAU,EAAEP,KAAK,CAAC,CAAC,CAAC;QACpBI,KAAK,EAAEJ,KAAK,CAAC,CAAC;MAChB,CAAC,CAAC;IACJ;EACF,CAAC;EAED;EACA6H,oBAAoB,EAAE;IACpB;IACA/H,OAAO,EAAE,kCAAkC;IAC3CC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB,IAAM6H,IAAI,GAAG9H,KAAK,CAAC,CAAC,CAAC;MACrB,IAAM+H,IAAI,GAAG/H,KAAK,CAAC,CAAC,CAAC;MACrB,IAAMI,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC;MACtB,OAAO,CAAC;QACNK,QAAQ,EAAEX,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS,CAAC0H,UAAU;QAAE;QAC1DzH,UAAU,EAAEuH,IAAI,GAAG,GAAG,GAAGC,IAAI;QAC7B3H,KAAK,EAALA;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA6H,yCAAyC,EAAE;IACzC;IACAnI,OAAO,EAAE,cAAc;IACvBC,OAAO,EAAE,SAATA,OAAOA,CAAGC,KAAK,EAAEC,IAAI,EAAK;MACxB;MACA,IAAI,CAAC,OAAO,CAACiE,IAAI,CAACjE,IAAI,CAAC,EAAE,OAAO,IAAI;;MAEpC;MACAA,IAAI,GAAGA,IAAI,CAACiI,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAACpH,IAAI,CAAC,CAAC;;MAEjE;MACA,IAAMqH,GAAG,GAAG,wFAAwF;MACpG,IAAInD,CAAC;QAAEV,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC3B,IAAI6D,QAAQ,GAAG,EAAE;MACjB,IAAIC,QAAQ,GAAGpI,IAAI;MACnB,OAAO,CAAC+E,CAAC,GAAGmD,GAAG,CAACG,IAAI,CAACrI,IAAI,CAAC,MAAM,IAAI,EAAE;QACpCmI,QAAQ,CAAC5F,IAAI,CAAC;UAAErC,GAAG,EAAE6E,CAAC,CAAC,CAAC,CAAC;UAAE5E,KAAK,EAAE4E,CAAC,CAAC,CAAC,CAAC;UAAEuD,GAAG,EAAEvD,CAAC,CAAC,CAAC;QAAE,CAAC,CAAC;QACpDqD,QAAQ,GAAGA,QAAQ,CAACH,OAAO,CAAClD,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;QACvC,IAAI,CAACV,QAAQ,CAACW,GAAG,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEV,QAAQ,CAACY,GAAG,CAACF,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAC/CV,QAAQ,CAACa,GAAG,CAACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAACxC,IAAI,CAACwC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;;MAEA;MACA,IAAIoD,QAAQ,CAAClI,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MACtC,IAAI,CAAC,SAAS,CAACgE,IAAI,CAACkE,QAAQ,CAAC,CAAC,CAAC,CAACjI,GAAG,CAAC,EAAE,OAAO,IAAI;;MAEjD;MACA,IAAIkI,QAAQ,CAACH,OAAO,CAAC,mCAAmC,EAAE,EAAE,CAAC,CAAChI,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI;;MAErF;MACA,IAAMI,SAAS,GAAGZ,OAAO,CAAC,gBAAgB,CAAC,CAACY,SAAS;MACrD,OAAOkF,KAAK,CAACC,IAAI,CAACnB,QAAQ,CAACoB,OAAO,CAAC,CAAC,CAAC,CAACxE,GAAG,CAAC,UAAAsH,KAAA;QAAA,IAAAC,KAAA,OAAA5C,eAAA,CAAAlB,OAAA,EAAA6D,KAAA;UAAEpI,KAAK,GAAAqI,KAAA;UAAE3C,IAAI,GAAA2C,KAAA;QAAA,OAAO;UAC5DpI,QAAQ,EAAEC,SAAS,CAAC2H,yCAAyC,IAAI,CAAC;UAClE1H,UAAU,EAAEuF,IAAI,CAAC5D,IAAI,CAAC,GAAG,CAAC;UAC1B9B,KAAK,EAALA;QACF,CAAC;MAAA,CAAC,CAAC;IACL;EACF;AAIF,CAAC;;AAED;AACAsI,MAAM,CAACC,IAAI,CAAChJ,iBAAiB,CAAC,CAACsD,OAAO,CAAC,UAAAgD,GAAG,EAAI;EAC5C,IAAMnG,OAAO,GAAGH,iBAAiB,CAACsG,GAAG,CAAC,CAACnG,OAAO;EAC9C,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAAC8I,QAAQ,CAAC,GAAG,CAAC,EAAE;IACxDjJ,iBAAiB,CAACsG,GAAG,CAAC,CAACnG,OAAO,GAAGA,OAAO,CAACoI,OAAO,CAAC,KAAK,EAAE,qBAAqB,CAAC;EAChF,CAAC,MAAM,IAAIpI,OAAO,YAAY+I,MAAM,IAAI/I,OAAO,CAACgJ,MAAM,CAACF,QAAQ,CAAC,GAAG,CAAC,EAAE;IACpEjJ,iBAAiB,CAACsG,GAAG,CAAC,CAACnG,OAAO,GAAG,IAAI+I,MAAM,CAAC/I,OAAO,CAACgJ,MAAM,CAACZ,OAAO,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;EACnG;AACF,CAAC,CAAC", "ignoreList": []}]}