package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.math.BigDecimal;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.PreDestroy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.web.client.RestTemplate;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.GameDraw;
import com.ruoyi.system.domain.GameRecord;
import com.ruoyi.system.domain.GameWinning;
import com.ruoyi.system.domain.GameDrawStats;
import com.ruoyi.system.mapper.GameDrawMapper;
import com.ruoyi.system.mapper.GameRecordMapper;
import com.ruoyi.system.mapper.GameWinningMapper;
import com.ruoyi.system.mapper.GameMethodOddsMapper;
import com.ruoyi.system.utils.DataPermissionUtils;
import com.ruoyi.system.utils.GameUserUtils;
import com.ruoyi.system.utils.SettlementLockManager;
import com.ruoyi.system.utils.DataStatisticsLockManager;
import com.ruoyi.system.utils.RecordDataCacheManager;
import com.ruoyi.system.utils.OddsCacheManager;
import com.ruoyi.system.utils.DataPermissionUtils;
import com.ruoyi.system.mapper.GameSerialMapper;
import com.ruoyi.system.service.IGameDrawService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.domain.GameMethod;
import com.ruoyi.system.mapper.GameMethodMapper;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.mapper.GameCustomerMapper;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.GameCustomer;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.text.SimpleDateFormat;
import java.util.concurrent.CompletionException;
import java.util.stream.Collectors;
import java.util.concurrent.CompletableFuture;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.system.mapper.GameQihaoMapper;
import com.ruoyi.system.domain.GameQihao;
import com.ruoyi.system.service.IGameDrawService;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.system.mapper.SysOperLogMapper;
import com.ruoyi.system.mapper.SysLogininforMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysTicketMapper;
import com.ruoyi.system.mapper.SysTicketReplyMapper;

/**
 * 开奖管理Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class GameDrawServiceImpl implements IGameDrawService {
    private static final Logger log = LoggerFactory.getLogger(GameDrawServiceImpl.class);

    // 创建线程池，核心线程数为CPU核心数
    private final ExecutorService executorService = Executors.newFixedThreadPool(
            Runtime.getRuntime().availableProcessors());

    @Autowired
    private GameDrawMapper gameDrawMapper;

    @Autowired
    private GameRecordMapper gameRecordMapper;

    @Autowired
    private GameWinningMapper gameWinningMapper;

    @Autowired
    private GameMethodMapper gameMethodMapper;

    @Autowired
    private GameCustomerMapper gameCustomerMapper;

    @Autowired
    private GameSerialMapper gameSerialMapper;

    @Autowired
    private GameMethodOddsMapper gameMethodOddsMapper;

    @Autowired
    private SettlementLockManager settlementLockManager;

    @Autowired
    private DataStatisticsLockManager dataStatisticsLockManager;

    @Autowired
    private RecordDataCacheManager recordDataCacheManager;

    @Autowired
    private OddsCacheManager oddsCacheManager;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private GameQihaoMapper gameQihaoMapper;

    @Autowired
    private IGameDrawService gameDrawService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SysOperLogMapper sysOperLogMapper;

    @Autowired
    private SysLogininforMapper sysLogininforMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysTicketMapper sysTicketMapper;

    @Autowired
    private SysTicketReplyMapper sysTicketReplyMapper;

    /**
     * 查询开奖管理
     * 
     * @param drawId 开奖管理主键
     * @return 开奖管理
     */
    @Override
    public GameDraw selectGameDrawByDrawId(Long drawId) {
        return gameDrawMapper.selectGameDrawByDrawId(drawId);
    }

    /**
     * 查询开奖管理列表
     *
     * @param gameDraw 开奖管理
     * @return 开奖管理
     */
    @Override
    public List<GameDraw> selectGameDrawList(GameDraw gameDraw) {
        // 添加详细调试日志


        // 应用数据权限过滤
        DataPermissionUtils.applyDataPermission(gameDraw);



        List<GameDraw> result = gameDrawMapper.selectGameDrawList(gameDraw);



        return result;
    }

    /**
     * 新增开奖管理
     *
     * @param gameDraw 开奖管理
     * @return 结果
     */
    @Override
    public int insertGameDraw(GameDraw gameDraw) {
        // 设置当前用户ID（如果没有设置的话）
        if (gameDraw.getSysUserId() == null) {
            DataPermissionUtils.setCurrentUser(gameDraw);
        }
        gameDraw.setCreateTime(DateUtils.getNowDate());
        if (gameDraw.getAllTotalBets() == null) {
            gameDraw.setAllTotalBets(0);
        }
        return gameDrawMapper.insertGameDraw(gameDraw);
    }

    /**
     * 修改开奖管理
     * 
     * @param gameDraw 开奖管理
     * @return 结果
     */
    @Override
    public int updateGameDraw(GameDraw gameDraw) {
        gameDraw.setUpdateTime(DateUtils.getNowDate());
        return gameDrawMapper.updateGameDraw(gameDraw);
    }

    /**
     * 批量删除开奖管理
     * 
     * @param drawIds 需要删除的开奖管理主键
     * @return 结果
     */
    @Override
    public int deleteGameDrawByDrawIds(Long[] drawIds) {
        return gameDrawMapper.deleteGameDrawByDrawIds(drawIds);
    }

    /**
     * 删除开奖管理信息
     * 
     * @param drawId 开奖管理主键
     * @return 结果
     */
    @Override
    public int deleteGameDrawByDrawId(Long drawId) {
        return gameDrawMapper.deleteGameDrawByDrawId(drawId);
    }

    /**
     * 根据彩种ID查询最大期号
     * 
     * @param lotteryId 彩种ID
     * @return 最大期号
     */
    @Override
    public String selectMaxQihaoByLotteryId(Long lotteryId) {
        // 根据用户权限返回不同的最大期号
        if (DataPermissionUtils.isAdmin()) {
            return gameDrawMapper.selectMaxQihaoByLotteryId(lotteryId);
        } else {
            Long currentUserId = GameUserUtils.getCurrentSysUserId();
            return gameDrawMapper.selectMaxQihaoByLotteryIdAndUser(lotteryId, currentUserId);
        }
    }

    /**
     * 根据期号和彩种ID查询开奖信息
     *
     * @param qihao     期号
     * @param lotteryId 彩种ID
     * @return 开奖信息
     */
    @Override
    public GameDraw selectByQihaoAndLotteryId(String qihao, Long lotteryId) {
        Map<String, Object> params = new HashMap<>();
        params.put("qihao", qihao);
        params.put("lotteryId", lotteryId);

        // 根据用户权限添加用户隔离
        if (!DataPermissionUtils.isAdmin()) {
            Long currentUserId = GameUserUtils.getCurrentSysUserId();
            params.put("sysUserId", currentUserId);
        }

        return gameDrawMapper.selectByQihaoAndLotteryId(params);
    }

    /**
     * 根据期号查询开奖信息
     * 
     * @param qihao 期号
     * @return 开奖信息
     */
    @Override
    public GameDraw selectByQihao(String qihao) {
        return gameDrawMapper.selectByQihao(qihao);
    }

    /**
     * 根据开奖时间范围查询开奖信息
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 开奖信息列表
     */
    @Override
    public List<GameDraw> selectByDrawTimeRange(String startTime, String endTime) {
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        return gameDrawMapper.selectByDrawTimeRange(params);
    }

    /**
     * 处理开奖结算
     */
    @Override
    public void processDrawSettlement() {
        Long currentUserId = GameUserUtils.getCurrentSysUserId();

        // 调试日志：确认用户ID
        log.info("[用户ID调试] GameUserUtils.getCurrentSysUserId()={}, SecurityUtils.getUserId()={}, 是否管理员={}",
                currentUserId, SecurityUtils.getUserId(), GameUserUtils.isAdmin());

        // 【第一步：开奖前draw数据一致性检查和修复 - 在事务外执行，使用独立的数据统计锁】
        if (!performDataConsistencyCheckWithLock(currentUserId)) {
            throw new ServiceException("系统繁忙，当前有太多用户正在进行结算操作，请5秒后再试");
        }

        // 【第二步：开奖前customer数据一致性检查和修复 - 在事务外执行，使用独立的customer数据统计锁】
        if (!performCustomerDataConsistencyCheckWithLock(currentUserId)) {
            throw new ServiceException("系统繁忙，当前有太多用户正在进行结算操作，请5秒后再试");
        }

        // 尝试获取结算锁，超时时间90秒
        if (!settlementLockManager.acquireSettlementLock(currentUserId, null, 90)) {
            throw new ServiceException("系统繁忙，请稍后再试。当前有太多用户正在进行结算操作。");
        }

        try {
            log.info("用户 {} 开始批量结算，{}", currentUserId, settlementLockManager.getSystemLoadInfo());

            // 在事务中执行结算逻辑
            processDrawSettlementInTransaction(currentUserId);



            // 结算完成后，自动新增下一期
            // try {
            // // 从Redis获取福彩3D数据
            // Object fcQihaoObj = redisCache.getCacheObject("fc:qihao");
            // Object fcOpenTimeObj = redisCache.getCacheObject("fc:opentime");

            // if (fcQihaoObj != null && fcOpenTimeObj != null) {
            // String fcQihao = fcQihaoObj.toString();
            // String fcOpenTime = fcOpenTimeObj.toString();

            // // 获取当前最大期号
            // String maxQihao = gameDrawService.selectMaxQihao();
            // int currentMaxQihao = Integer.parseInt(maxQihao);

            // // 计算下一期号
            // int nextFcQihao = Integer.parseInt(fcQihao) + 1;

            // // 检查是否需要新增福彩3D记录
            // if (nextFcQihao > currentMaxQihao) {
            // // 检查是否已存在该期号记录
            // GameDraw queryDraw = new GameDraw();
            // queryDraw.setLotteryId(1L);
            // queryDraw.setQihao(String.valueOf(nextFcQihao));
            // List<GameDraw> existingDraws = gameDrawService.selectGameDrawList(queryDraw);

            // if (existingDraws.isEmpty()) {
            // // 使用Redis中的opentime作为基准日期时间
            // SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // Date openTime = sdf.parse(fcOpenTime);

            // // 设置开奖时间为opentime的下一天 21:15:00
            // Calendar calendar = Calendar.getInstance();
            // calendar.setTime(openTime);
            // // 先加1天
            // calendar.add(Calendar.DAY_OF_MONTH, 1);
            // // 再设置时间
            // calendar.set(Calendar.HOUR_OF_DAY, 21);
            // calendar.set(Calendar.MINUTE, 15);
            // calendar.set(Calendar.SECOND, 0);
            // calendar.set(Calendar.MILLISECOND, 0);

            // GameDraw fcDraw = new GameDraw();
            // fcDraw.setLotteryId(1L);
            // fcDraw.setQihao(String.valueOf(nextFcQihao));
            // fcDraw.setDrawTime(calendar.getTime());
            // fcDraw.setJiesuan(0);
            // gameDrawService.insertNextDraw(fcDraw);
            // }
            // }
            // }

            // // 从Redis获取体彩排三数据
            // Object tcQihaoObj = redisCache.getCacheObject("tc:qihao");
            // Object tcOpenTimeObj = redisCache.getCacheObject("tc:opentime");

            // if (tcQihaoObj != null && tcOpenTimeObj != null) {
            // String tcQihao = tcQihaoObj.toString();
            // String tcOpenTime = tcOpenTimeObj.toString();

            // // 获取体彩排三的最大期号
            // String maxTcpsQihao = gameDrawService.selectMaxQihaoByLotteryId(2L);
            // int currentMaxTcpsQihao = maxTcpsQihao != null ?
            // Integer.parseInt(maxTcpsQihao) : 0;

            // // 计算下一期号
            // int nextTcQihao = Integer.parseInt(tcQihao) + 1;

            // // 检查是否需要新增体彩排三记录
            // if (nextTcQihao > currentMaxTcpsQihao) {
            // // 检查是否已存在该期号记录
            // GameDraw queryDraw = new GameDraw();
            // queryDraw.setLotteryId(2L);
            // queryDraw.setQihao(String.valueOf(nextTcQihao));
            // List<GameDraw> existingDraws = gameDrawService.selectGameDrawList(queryDraw);

            // if (existingDraws.isEmpty()) {
            // // 使用Redis中的opentime作为基准日期时间
            // SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // Date openTime = sdf.parse(tcOpenTime);

            // // 设置开奖时间为opentime的下一天 21:25:00
            // Calendar calendar = Calendar.getInstance();
            // calendar.setTime(openTime);
            // // 先加1天
            // calendar.add(Calendar.DAY_OF_MONTH, 1);
            // // 再设置时间
            // calendar.set(Calendar.HOUR_OF_DAY, 21);
            // calendar.set(Calendar.MINUTE, 25);
            // calendar.set(Calendar.SECOND, 0);
            // calendar.set(Calendar.MILLISECOND, 0);

            log.info("用户 {} 开奖结算处理完成", currentUserId);

        } catch (Exception e) {
            log.error("用户 {} 开奖结算处理失败", currentUserId, e);
            throw new RuntimeException("开奖结算处理失败", e);
        } finally {
            // 释放结算锁
            settlementLockManager.releaseSettlementLock(currentUserId, null);
            log.info("用户 {} 结算锁已释放，{}", currentUserId, settlementLockManager.getSystemLoadInfo());
        }
    }

    /**
     * 处理单个开奖记录的结算（优化版本）
     */
    private void processSingleDraw(GameDraw draw, Long currentUserId, boolean isAdmin) {
        processSingleDrawOptimized(draw, currentUserId, isAdmin);
    }

    /**
     * 处理单个开奖记录的结算（批量插入版本）
     * @param draw 开奖记录
     * @param currentUserId 当前用户ID
     * @param isAdmin 是否管理员
     * @param winningRecords 中奖记录收集器
     */
    private void processSingleDrawWithBatch(GameDraw draw, Long currentUserId, boolean isAdmin, List<GameWinning> winningRecords) {
        processSingleDrawOptimizedWithBatch(draw, currentUserId, isAdmin, winningRecords);
    }

    /**
     * 处理单个开奖记录的结算（优化版本）
     * 使用批量操作和缓存优化，大幅提升性能
     */
    private void processSingleDrawOptimized(GameDraw draw, Long currentUserId, boolean isAdmin) {
        try {
            // 验证数据权限（直接比较用户ID，避免在异步线程中调用SecurityUtils）
            if (!isAdmin) {
                if (!currentUserId.equals(draw.getSysUserId())) {
                    throw new ServiceException("无权处理该开奖记录");
                }
            }

            // 获取该期该用户的投注记录（每个用户只处理自己的记录）
            Map<String, Object> params = new HashMap<>();
            params.put("issueNumber", draw.getQihao());
            params.put("sysUserId", currentUserId);
            List<GameRecord> records = gameRecordMapper.selectByIssueNumberAndUser(params);

            if (records == null || records.isEmpty()) {
                log.info("期号 {} 用户 {} 没有投注记录", draw.getQihao(), currentUserId);
                // 没有投注记录，直接标记为已结算
                draw.setJiesuan(1);
                gameDrawMapper.updateGameDraw(draw);
                return;
            }

            // 验证开奖号码格式
            if (draw.getKaijianghao() == null || draw.getKaijianghao().trim().isEmpty()) {
                log.error("期号 {} 开奖号码为空，跳过结算", draw.getQihao());
                return;
            }

            String[] winningNumbers = draw.getKaijianghao().split(",");
            if (winningNumbers.length != 3) {
                log.error("期号 {} 开奖号码格式错误: {}", draw.getQihao(), draw.getKaijianghao());
                return;
            }

            // 只在单双大小和值为空时才计算并设置
            if (draw.getHezhi() == null || draw.getDanshuang() == null || draw.getDaxiao() == null) {
                // 计算开奖号码的和值、单双、大小
                int hezhi = Integer.parseInt(winningNumbers[0]) + Integer.parseInt(winningNumbers[1])
                        + Integer.parseInt(winningNumbers[2]);

                // 只在对应字段为空时才设置
                if (draw.getHezhi() == null) {
                    draw.setHezhi(hezhi);
                }
                if (draw.getDanshuang() == null) {
                    // 设置单双（200表示单，201表示双）
                    draw.setDanshuang(hezhi % 2 == 0 ? 201 : 200);
                }
                if (draw.getDaxiao() == null) {
                    // 设置大小（300表示大，301表示小，以13为分界点）
                    draw.setDaxiao(hezhi > 13 ? 300 : 301);
                }

                // 更新开奖记录
                gameDrawMapper.updateGameDraw(draw);

                log.info("期号 {} 设置开奖属性 - 和值: {}, 单双: {}, 大小: {}",
                        draw.getQihao(), draw.getHezhi(),
                        draw.getDanshuang() == 200 ? "单" : "双",
                        draw.getDaxiao() == 300 ? "大" : "小");
            }

            // 计算总中奖金额
            BigDecimal totalBetAmount = BigDecimal.ZERO;
            // 从game_draw表获取总投注金额（使用当前处理的draw的用户ID）
            Map<String, Object> drawParams = new HashMap<>();
            drawParams.put("qihao", draw.getQihao());
            drawParams.put("lotteryId", draw.getLotteryId());
            drawParams.put("sysUserId", draw.getSysUserId());  // 使用draw的用户ID
            GameDraw gameDraw = gameDrawMapper.selectByQihaoAndLotteryId(drawParams);
            if (gameDraw != null && gameDraw.getZongtouzhu() != null) {
                totalBetAmount = gameDraw.getZongtouzhu();
            }

            BigDecimal totalWinAmount = BigDecimal.ZERO;

            // 处理每条投注记录
            for (GameRecord record : records) {

                // 检查是否已经结算过，避免重复处理
                if (record.getJiesuan() != null && record.getJiesuan() == 1) {
                    log.info("[跳过已结算] betId={}, methodId={}, 已经结算过", record.getBetId(), record.getMethodId());
                    continue;
                }

                // 判断是否中奖
                boolean isWinning = checkWinning(record, draw);

                log.info("[中奖判断] betId={}, methodId={}, isWinning={}", record.getBetId(), record.getMethodId(), isWinning);

                if (isWinning) {
                    // 设置中奖号码
                    Long methodId = record.getMethodId();

                    log.info("[中奖处理] betId={}, methodId={}, 开始处理中奖记录", record.getBetId(), methodId);

                    // 对于组三和组六玩法，不在这里设置中奖号码，因为已经在各自的check方法中设置了
                    // 注意：独胆玩法(methodId=1)应该在第二分支处理，因为需要设置中奖号码
                    if (methodId == 7L || methodId == 9L || methodId == 11L || methodId == 13L ||
                            methodId == 15L || methodId == 17L || methodId == 19L ||
                            methodId == 8L || methodId == 10L || methodId == 12L || methodId == 14L ||
                            methodId == 16L || methodId == 18L) {

                        log.info("[中奖处理-第一分支] betId={}, methodId={}, 进入第一处理分支", record.getBetId(), methodId);
                        // 计算中奖金额
                        BigDecimal winAmount = calculateWinningAmount(record, methodId, currentUserId);
                        totalWinAmount = totalWinAmount.add(winAmount);

                        // 更新投注记录
                        record.setIsDrawn(1);
                        record.setShifouzj(1L);
                        record.setJiesuan(1L);
                        record.setWinAmount(winAmount);
                        record.setDrawTime(new Date());
                        gameRecordMapper.updateGameRecord(record);

                        // 创建中奖记录
                        GameWinning winning = new GameWinning();
                        winning.setSysUserId(currentUserId); // 设置系统用户ID
                        winning.setUserId(record.getUserId());
                        winning.setIssueNumber(draw.getQihao());
                        winning.setLotteryId(record.getLotteryId());
                        winning.setMethodId(record.getMethodId());
                        winning.setBetNumber(record.getBetNumbers());
                        winning.setWinAmount(winAmount);
                        winning.setWinningNumbers(record.getWinningNumbers());
                        winning.setIsPaid(0);
                        // 新增字段
                        winning.setSerialNumber(record.getSerialNumber());
                        winning.setShibie(record.getShibie());
                        winning.setTotalBets(record.getTotalBets());

                        log.info("[中奖记录插入-第一处] betId={}, methodId={}, userId={}, sysUserId={}, winAmount={}, serialNumber={}, shibie={}",
                                record.getBetId(), record.getMethodId(), record.getUserId(), currentUserId, winAmount,
                                record.getSerialNumber(), record.getShibie());

                        gameWinningMapper.insertGameWinning(winning);

                        log.info("[中奖记录插入-第一处完成] winId={}", winning.getWinId());

                        log.info("[中奖处理-第一分支完成] betId={}, methodId={}, 跳过后续处理", record.getBetId(), methodId);
                        continue;
                    }

                    // 对于其他玩法，设置中奖号码
                    Map<String, Object> winningMap = new HashMap<>();
                    List<Map<String, Object>> winningList = new ArrayList<>();
                    Map<String, Object> numberMap = new HashMap<>();

                    if (methodId == 1) { // 独胆
                        numberMap.put("a", winningNumbers[0]);
                        winningList.add(numberMap);
                    } else if (methodId == 4) { // 两码对子
                        numberMap.put("a", winningNumbers[0]);
                        numberMap.put("b", winningNumbers[1]);
                        winningList.add(numberMap);
                    } else if (methodId == 3) { // 两码组合
                        winningMap = JSON.parseObject(record.getWinningNumbers(), Map.class);
                    } else if (methodId == 2 || methodId == 20 || methodId == 31) {
                        // 一码定位、包打组六、包打组三，这里不赋值中奖号码，交给processWinningNumbers处理
                    } else {
                        // 其他玩法保持原有格式
                        numberMap.put("a", winningNumbers[0]);
                        numberMap.put("b", winningNumbers[1]);
                        numberMap.put("c", winningNumbers[2]);
                        winningList.add(numberMap);
                    }

                    // 如果不是组六类型且不是2/20/31/70，则使用winningList
                    // 排除70（两码组三），因为它已经在checkLiangMaZuSan中正确设置了中奖号码
                    if (methodId != 3 && methodId != 2 && methodId != 20 && methodId != 31 && methodId != 70) {
                        winningMap.put("winning", winningList);
                        record.setWinningNumbers(JSON.toJSONString(winningMap));
                    }

                    log.info("[中奖处理-第二分支] betId={}, methodId={}, 进入第二处理分支", record.getBetId(), methodId);

                    // 对于其他玩法，使用processWinningNumbers方法处理中奖号码格式
                    processWinningNumbers(record, winningNumbers, methodId, draw);

                    // 计算中奖金额
                    BigDecimal winAmount = calculateWinningAmount(record, record.getMethodId(), currentUserId);
                    totalWinAmount = totalWinAmount.add(winAmount);

                    // 更新投注记录
                    record.setIsDrawn(1);
                    record.setShifouzj(1L);
                    record.setJiesuan(1L);
                    record.setWinAmount(winAmount);
                    record.setDrawTime(new Date());
                    gameRecordMapper.updateGameRecord(record);

                    // 创建中奖记录
                    GameWinning winning = new GameWinning();

                    // 调试：检查各种用户ID的值
                    Long debugCurrentUserId = currentUserId;
                    Long debugRecordSysUserId = record.getSysUserId();
                    Long debugSecurityUserId = null;
                    Long debugGameUserUtilsId = null;

                    // 注释掉异步线程中的调试代码，避免获取用户上下文失败
                    // try {
                    //     debugSecurityUserId = SecurityUtils.getUserId();
                    // } catch (Exception e) {
                    //     log.warn("[中奖记录调试] SecurityUtils.getUserId()异常: {}", e.getMessage());
                    // }
                    debugSecurityUserId = null; // 异步线程中无法获取

                    // 注释掉异步线程中的调试代码，避免获取用户上下文失败
                    // try {
                    //     debugGameUserUtilsId = GameUserUtils.getCurrentSysUserId();
                    // } catch (Exception e) {
                    //     log.warn("[中奖记录调试] GameUserUtils.getCurrentSysUserId()异常: {}", e.getMessage());
                    // }
                    debugGameUserUtilsId = null; // 异步线程中无法获取

                    log.info("[中奖记录调试] currentUserId={}, record.getSysUserId()={}, SecurityUtils.getUserId()={}, GameUserUtils.getCurrentSysUserId()={}",
                            debugCurrentUserId, debugRecordSysUserId, debugSecurityUserId, debugGameUserUtilsId);

                    // 修复：使用当前用户的sys_user_id
                    winning.setSysUserId(currentUserId);
                    winning.setUserId(record.getUserId());
                    winning.setIssueNumber(draw.getQihao());
                    winning.setLotteryId(draw.getLotteryId());
                    winning.setMethodId(record.getMethodId());
                    winning.setBetNumber(record.getBetNumbers());
                    winning.setWinAmount(winAmount);
                    winning.setWinningNumbers(record.getWinningNumbers());
                    winning.setIsPaid(0);
                    // 新增字段
                    winning.setSerialNumber(record.getSerialNumber());
                    winning.setShibie(record.getShibie());
                    winning.setTotalBets(record.getTotalBets());

                    log.info("[中奖记录插入-第二处] betId={}, methodId={}, userId={}, sysUserId={}, winAmount={}, serialNumber={}, shibie={}",
                            record.getBetId(), record.getMethodId(), record.getUserId(), currentUserId, winAmount,
                            record.getSerialNumber(), record.getShibie());

                    log.info("[中奖记录插入前] winning.getSysUserId()={}", winning.getSysUserId());
                    gameWinningMapper.insertGameWinning(winning);
                    log.info("[中奖记录插入后] 插入完成，winId={}", winning.getWinId());

                    log.info("[中奖记录插入] 当前用户:{}, 下注记录用户:{}, 期号:{}, 中奖金额:{}, 流水号:{}",
                            currentUserId, record.getSysUserId(), draw.getQihao(), winAmount, record.getSerialNumber());

                } else {
                    // 更新未中奖记录
                    record.setIsDrawn(1);
                    record.setShifouzj(0L);
                    record.setJiesuan(1L);
                    record.setWinAmount(BigDecimal.ZERO);
                    record.setDrawTime(new Date());
                    gameRecordMapper.updateGameRecord(record);
                }
            }

            // 使用原子性操作更新开奖表字段（解决并发问题）
            // 先标记为已结算
            draw.setJiesuan(1);
            gameDrawMapper.updateGameDraw(draw);

            // 使用原子性操作累加结算金额和更新盈利
            int drawResult = gameDrawMapper.atomicUpdateZongjiesuan(draw.getQihao(), draw.getLotteryId(), totalWinAmount, currentUserId);
            log.info("[开奖表原子性更新] 期号{}, 彩种{}, 增加结算金额{}, 影响行数: {}",
                draw.getQihao(), draw.getLotteryId(), totalWinAmount, drawResult);

            // 重新查询开奖记录以获取最新的金额数据
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("qihao", draw.getQihao());
            queryParams.put("lotteryId", draw.getLotteryId());
            queryParams.put("sysUserId", currentUserId);
            GameDraw updatedDraw = gameDrawMapper.selectByQihaoAndLotteryId(queryParams);

            // 重新计算并更新盈利金额（确保准确性）
            if (updatedDraw != null) {
                BigDecimal correctProfit = updatedDraw.getZongtouzhu().subtract(updatedDraw.getZongjiesuan());
                updatedDraw.setYingli(correctProfit);
                gameDrawMapper.updateGameDraw(updatedDraw);
                log.info("期号 {} 盈利金额已重新计算: 投注额={}, 结算额={}, 盈利={}",
                    draw.getQihao(), updatedDraw.getZongtouzhu(), updatedDraw.getZongjiesuan(), correctProfit);
            }

            log.info("期号 {} 结算完成 - 总投注金额: {}, 总中奖金额: {}",
                    draw.getQihao(), totalBetAmount, totalWinAmount);

            // 修复：直接使用当前用户ID
            final Long settlementUserId = currentUserId;

            log.info("[用户ID确认] 当前用户ID:{}, 是否管理员:{}",
                    currentUserId, isAdmin);

            // 新增：结算后同步所有中奖用户中奖金额并同步到game_customer（改为同步执行）
            try {
                log.info("[结算同步调试] 开始查询期号{}用户{}的中奖记录",
                        draw.getQihao(), settlementUserId);

                // 查询本期所有中奖记录（按用户隔离）
                List<GameWinning> winningList;

                // 修复：无论是否管理员，都只查询当前用户的中奖记录
                winningList = gameWinningMapper.selectByIssueNumberAndSysUser(draw.getQihao(), settlementUserId);
                log.info("[结算同步调试] 查询期号{}用户{}的中奖记录，结果数量:{}", draw.getQihao(), settlementUserId, winningList.size());

                // 调试：查看查询到的记录中sys_user_id的分布
                Map<Long, Long> sysUserIdCount = winningList.stream()
                        .collect(Collectors.groupingBy(GameWinning::getSysUserId, Collectors.counting()));
                log.info("[结算同步调试] 查询到的中奖记录sys_user_id分布: {}", sysUserIdCount);
                // 按用户分组统计中奖金额
                Map<Long, BigDecimal> userWinMap = new HashMap<>();
                // 新增：按用户和彩种分组统计
                Map<Long, BigDecimal> userFcWinMap = new HashMap<>();
                Map<Long, BigDecimal> userTcWinMap = new HashMap<>();
                for (GameWinning gw : winningList) {
                    userWinMap.merge(gw.getUserId(), gw.getWinAmount(), BigDecimal::add);
                    if (gw.getLotteryId() != null && gw.getWinAmount() != null) {
                        if (gw.getLotteryId() == 1L) {
                            userFcWinMap.merge(gw.getUserId(), gw.getWinAmount(), BigDecimal::add);
                        } else if (gw.getLotteryId() == 2L) {
                            userTcWinMap.merge(gw.getUserId(), gw.getWinAmount(), BigDecimal::add);
                        }
                    }
                }
                // 批量同步到game_customer
                log.info("[结算日志] userFcWinMap: {}", userFcWinMap);
                log.info("[结算日志] userTcWinMap: {}", userTcWinMap);

                // 使用原子性增量更新 game_customer 表的中奖金额（解决并发问题）
                for (Map.Entry<Long, BigDecimal> entry : userWinMap.entrySet()) {
                    Long userId = entry.getKey();
                    BigDecimal currentWinAmount = entry.getValue(); // 本次结算的中奖金额

                    // 使用原子性增量更新，避免并发问题
                    int totalResult = gameCustomerMapper.atomicUpdateTotalWinAmount(userId, settlementUserId, currentWinAmount);
                    log.info("[结算同步-原子性] 用户{} 总中奖金额增加{}, 影响行数: {}", userId, currentWinAmount, totalResult);

                    // 按彩种原子性更新中奖金额
                    int lotteryResult = gameCustomerMapper.atomicUpdateLotteryWinAmount(userId, settlementUserId, draw.getLotteryId(), currentWinAmount);
                    log.info("[结算同步-原子性] 用户{} 彩种{}中奖金额增加{}, 影响行数: {}", userId, draw.getLotteryId(), currentWinAmount, lotteryResult);

                    log.info("[结算同步-原子性] 用户ID:{} (系统用户:{}) 本次中奖金额:{} 已原子性累加", userId, settlementUserId, currentWinAmount);
                }

                // 新增：更新 game_serial 表的中奖金额
                updateGameSerialWinMoney(draw.getQihao(), settlementUserId);

            } catch (Exception e) {
                log.error("[结算同步] 同步用户总中奖金额失败", e);
            }

        } catch (Exception e) {
            log.error("处理期号 {} 结算时发生错误: {}", draw.getQihao(), e.getMessage(), e);
            throw new ServiceException("结算处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理单个开奖记录的结算（批量插入优化版本）
     * 使用批量操作和缓存优化，大幅提升性能
     * @param draw 开奖记录
     * @param currentUserId 当前用户ID
     * @param isAdmin 是否管理员
     * @param winningRecords 中奖记录收集器
     */
    private void processSingleDrawOptimizedWithBatch(GameDraw draw, Long currentUserId, boolean isAdmin, List<GameWinning> winningRecords) {
        try {
            // 验证数据权限（直接比较用户ID，避免在异步线程中调用SecurityUtils）
            if (!isAdmin) {
                if (!currentUserId.equals(draw.getSysUserId())) {
                    throw new ServiceException("无权处理该开奖记录");
                }
            }

            // 获取该期该用户的投注记录（每个用户只处理自己的记录）
            Map<String, Object> params = new HashMap<>();
            params.put("issueNumber", draw.getQihao());
            params.put("sysUserId", currentUserId);
            List<GameRecord> records = gameRecordMapper.selectByIssueNumberAndUser(params);

            if (records == null || records.isEmpty()) {
                log.info("期号 {} 用户 {} 没有投注记录", draw.getQihao(), currentUserId);
                // 没有投注记录，直接标记为已结算
                draw.setJiesuan(1);
                gameDrawMapper.updateGameDraw(draw);
                return;
            }

            // 验证开奖号码格式
            if (draw.getKaijianghao() == null || draw.getKaijianghao().trim().isEmpty()) {
                log.error("期号 {} 开奖号码为空，跳过结算", draw.getQihao());
                return;
            }

            String[] winningNumbers = draw.getKaijianghao().split(",");
            if (winningNumbers.length != 3) {
                log.error("期号 {} 开奖号码格式错误: {}", draw.getQihao(), draw.getKaijianghao());
                return;
            }

            // 只在单双大小和值为空时才计算并设置
            if (draw.getHezhi() == null || draw.getDanshuang() == null || draw.getDaxiao() == null) {
                // 计算开奖号码的和值、单双、大小
                int hezhi = Integer.parseInt(winningNumbers[0]) + Integer.parseInt(winningNumbers[1])
                        + Integer.parseInt(winningNumbers[2]);

                // 只在对应字段为空时才设置
                if (draw.getHezhi() == null) {
                    draw.setHezhi(hezhi);
                }
                if (draw.getDanshuang() == null) {
                    // 设置单双（200表示单，201表示双）
                    draw.setDanshuang(hezhi % 2 == 0 ? 201 : 200);
                }
                if (draw.getDaxiao() == null) {
                    // 设置大小（300表示大，301表示小，以13为分界点）
                    draw.setDaxiao(hezhi > 13 ? 300 : 301);
                }

                // 更新开奖记录
                gameDrawMapper.updateGameDraw(draw);

                log.info("期号 {} 设置开奖属性 - 和值: {}, 单双: {}, 大小: {}",
                        draw.getQihao(), draw.getHezhi(),
                        draw.getDanshuang() == 200 ? "单" : "双",
                        draw.getDaxiao() == 300 ? "大" : "小");
            }

            // 计算总中奖金额
            BigDecimal totalBetAmount = BigDecimal.ZERO;
            // 从game_draw表获取总投注金额（使用当前处理的draw的用户ID）
            Map<String, Object> drawParams = new HashMap<>();
            drawParams.put("qihao", draw.getQihao());
            drawParams.put("lotteryId", draw.getLotteryId());
            drawParams.put("sysUserId", draw.getSysUserId());  // 使用draw的用户ID
            GameDraw gameDraw = gameDrawMapper.selectByQihaoAndLotteryId(drawParams);
            if (gameDraw != null && gameDraw.getZongtouzhu() != null) {
                totalBetAmount = gameDraw.getZongtouzhu();
            }

            BigDecimal totalWinAmount = BigDecimal.ZERO;

            // 【关键修改】收集本期的中奖记录，而不是立即插入
            List<GameWinning> currentDrawWinnings = new ArrayList<>();

            // 处理每条投注记录
            for (GameRecord record : records) {

                // 检查是否已经结算过，避免重复处理
                if (record.getJiesuan() != null && record.getJiesuan() == 1) {
                    log.info("[跳过已结算] betId={}, methodId={}, 已经结算过", record.getBetId(), record.getMethodId());
                    continue;
                }

                // 判断是否中奖
                boolean isWinning = checkWinning(record, draw);

                log.info("[中奖判断] betId={}, methodId={}, isWinning={}", record.getBetId(), record.getMethodId(), isWinning);

                if (isWinning) {
                    // 设置中奖号码
                    Long methodId = record.getMethodId();

                    log.info("[中奖处理] betId={}, methodId={}, 开始处理中奖记录", record.getBetId(), methodId);

                    // 对于组三和组六玩法，不在这里设置中奖号码，因为已经在各自的check方法中设置了
                    // 注意：独胆玩法(methodId=1)应该在第二分支处理，因为需要设置中奖号码
                    if (methodId == 7L || methodId == 9L || methodId == 11L || methodId == 13L ||
                            methodId == 15L || methodId == 17L || methodId == 19L ||
                            methodId == 8L || methodId == 10L || methodId == 12L || methodId == 14L ||
                            methodId == 16L || methodId == 18L) {

                        log.info("[中奖处理-第一分支] betId={}, methodId={}, 进入第一处理分支", record.getBetId(), methodId);
                        // 计算中奖金额
                        BigDecimal winAmount = calculateWinningAmount(record, methodId, currentUserId);
                        totalWinAmount = totalWinAmount.add(winAmount);

                        // 更新投注记录
                        record.setIsDrawn(1);
                        record.setShifouzj(1L);
                        record.setJiesuan(1L);
                        record.setWinAmount(winAmount);
                        record.setDrawTime(new Date());
                        gameRecordMapper.updateGameRecord(record);

                        // 【关键修改】创建中奖记录但不立即插入，而是添加到收集器
                        GameWinning winning = createGameWinning(currentUserId, record, draw, winAmount);
                        currentDrawWinnings.add(winning);

                        log.info("[中奖记录收集-第一处] betId={}, methodId={}, userId={}, sysUserId={}, winAmount={}, serialNumber={}, shibie={}",
                                record.getBetId(), record.getMethodId(), record.getUserId(), currentUserId, winAmount,
                                record.getSerialNumber(), record.getShibie());

                        log.info("[中奖处理-第一分支完成] betId={}, methodId={}, 跳过后续处理", record.getBetId(), methodId);
                        continue;
                    }

                    // 对于其他玩法，设置中奖号码
                    Map<String, Object> winningMap = new HashMap<>();
                    List<Map<String, Object>> winningList = new ArrayList<>();
                    Map<String, Object> numberMap = new HashMap<>();

                    if (methodId == 1) { // 独胆
                        numberMap.put("a", winningNumbers[0]);
                        winningList.add(numberMap);
                    } else if (methodId == 4) { // 两码对子
                        numberMap.put("a", winningNumbers[0]);
                        numberMap.put("b", winningNumbers[1]);
                        winningList.add(numberMap);
                    } else if (methodId == 3) { // 两码组合
                        winningMap = JSON.parseObject(record.getWinningNumbers(), Map.class);
                    } else if (methodId == 2 || methodId == 20 || methodId == 31) {
                        // 一码定位、包打组六、包打组三，这里不赋值中奖号码，交给processWinningNumbers处理
                    } else {
                        // 其他玩法保持原有格式
                        numberMap.put("a", winningNumbers[0]);
                        numberMap.put("b", winningNumbers[1]);
                        numberMap.put("c", winningNumbers[2]);
                        winningList.add(numberMap);
                    }

                    // 如果不是组六类型且不是2/20/31/70，则使用winningList
                    // 排除70（两码组三），因为它已经在checkLiangMaZuSan中正确设置了中奖号码
                    if (methodId != 3 && methodId != 2 && methodId != 20 && methodId != 31 && methodId != 70) {
                        winningMap.put("winning", winningList);
                        record.setWinningNumbers(JSON.toJSONString(winningMap));
                    }

                    log.info("[中奖处理-第二分支] betId={}, methodId={}, 进入第二处理分支", record.getBetId(), methodId);

                    // 对于其他玩法，使用processWinningNumbers方法处理中奖号码格式
                    processWinningNumbers(record, winningNumbers, methodId, draw);

                    // 计算中奖金额
                    BigDecimal winAmount = calculateWinningAmount(record, record.getMethodId(), currentUserId);
                    totalWinAmount = totalWinAmount.add(winAmount);

                    // 更新投注记录
                    record.setIsDrawn(1);
                    record.setShifouzj(1L);
                    record.setJiesuan(1L);
                    record.setWinAmount(winAmount);
                    record.setDrawTime(new Date());
                    gameRecordMapper.updateGameRecord(record);

                    // 【关键修改】创建中奖记录但不立即插入，而是添加到收集器
                    GameWinning winning = createGameWinning(currentUserId, record, draw, winAmount);
                    currentDrawWinnings.add(winning);

                    log.info("[中奖记录收集-第二处] betId={}, methodId={}, userId={}, sysUserId={}, winAmount={}, serialNumber={}, shibie={}",
                            record.getBetId(), record.getMethodId(), record.getUserId(), currentUserId, winAmount,
                            record.getSerialNumber(), record.getShibie());

                } else {
                    // 更新未中奖记录
                    record.setIsDrawn(1);
                    record.setShifouzj(0L);
                    record.setJiesuan(1L);
                    record.setWinAmount(BigDecimal.ZERO);
                    record.setDrawTime(new Date());
                    gameRecordMapper.updateGameRecord(record);
                }
            }

            // 【新增】将本期的中奖记录添加到总收集器
            winningRecords.addAll(currentDrawWinnings);
            log.info("期号 {} 收集到 {} 条中奖记录", draw.getQihao(), currentDrawWinnings.size());

            // 使用原子性操作更新开奖表字段（解决并发问题）
            // 先标记为已结算
            draw.setJiesuan(1);
            gameDrawMapper.updateGameDraw(draw);

            // 使用原子性操作累加结算金额和更新盈利
            int drawResult = gameDrawMapper.atomicUpdateZongjiesuan(draw.getQihao(), draw.getLotteryId(), totalWinAmount, currentUserId);
            log.info("[开奖表原子性更新] 期号{}, 彩种{}, 增加结算金额{}, 影响行数: {}",
                draw.getQihao(), draw.getLotteryId(), totalWinAmount, drawResult);

            // 重新查询开奖记录以获取最新的金额数据
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("qihao", draw.getQihao());
            queryParams.put("lotteryId", draw.getLotteryId());
            queryParams.put("sysUserId", currentUserId);
            GameDraw updatedDraw = gameDrawMapper.selectByQihaoAndLotteryId(queryParams);

            // 重新计算并更新盈利金额（确保准确性）
            if (updatedDraw != null) {
                BigDecimal correctProfit = updatedDraw.getZongtouzhu().subtract(updatedDraw.getZongjiesuan());
                updatedDraw.setYingli(correctProfit);
                gameDrawMapper.updateGameDraw(updatedDraw);
                log.info("期号 {} 盈利金额已重新计算: 投注额={}, 结算额={}, 盈利={}",
                    draw.getQihao(), updatedDraw.getZongtouzhu(), updatedDraw.getZongjiesuan(), correctProfit);
            }

            log.info("期号 {} 结算完成 - 总投注金额: {}, 总中奖金额: {}",
                    draw.getQihao(), totalBetAmount, totalWinAmount);

            // 注意：数据同步逻辑移到批量插入完成后执行
            // 这里不再执行同步逻辑，避免查询不到刚插入的中奖记录

        } catch (Exception e) {
            log.error("处理期号 {} 结算时发生错误: {}", draw.getQihao(), e.getMessage(), e);
            throw new ServiceException("结算处理失败: " + e.getMessage());
        }
    }

    /**
     * 创建中奖记录对象的辅助方法
     * @param currentUserId 当前用户ID
     * @param record 投注记录
     * @param draw 开奖记录
     * @param winAmount 中奖金额
     * @return 中奖记录对象
     */
    private GameWinning createGameWinning(Long currentUserId, GameRecord record, GameDraw draw, BigDecimal winAmount) {
        GameWinning winning = new GameWinning();
        winning.setSysUserId(currentUserId);
        winning.setUserId(record.getUserId());
        winning.setIssueNumber(draw.getQihao());
        winning.setLotteryId(draw.getLotteryId());
        winning.setMethodId(record.getMethodId());
        winning.setBetNumber(record.getBetNumbers());
        winning.setWinAmount(winAmount);
        winning.setWinningNumbers(record.getWinningNumbers());
        winning.setIsPaid(0);
        // 新增字段
        winning.setSerialNumber(record.getSerialNumber());
        winning.setShibie(record.getShibie());
        winning.setTotalBets(record.getTotalBets());

        return winning;
    }

    /**
     * 批量数据同步方法
     * 在批量插入中奖记录后，同步更新相关统计数据
     * @param currentUserId 当前用户ID
     * @param winningRecords 已插入的中奖记录列表
     */
    private void performBatchDataSynchronization(Long currentUserId, List<GameWinning> winningRecords) {
        try {
            log.info("[批量数据同步] 开始同步 {} 条中奖记录的统计数据", winningRecords.size());

            // 按期号分组统计
            Map<String, List<GameWinning>> issueGroupMap = winningRecords.stream()
                    .collect(Collectors.groupingBy(GameWinning::getIssueNumber));

            // 按用户分组统计中奖金额
            Map<Long, BigDecimal> userWinMap = new HashMap<>();
            Map<Long, BigDecimal> userFcWinMap = new HashMap<>();
            Map<Long, BigDecimal> userTcWinMap = new HashMap<>();

            for (GameWinning gw : winningRecords) {
                userWinMap.merge(gw.getUserId(), gw.getWinAmount(), BigDecimal::add);
                if (gw.getLotteryId() != null && gw.getWinAmount() != null) {
                    if (gw.getLotteryId() == 1L) {
                        userFcWinMap.merge(gw.getUserId(), gw.getWinAmount(), BigDecimal::add);
                    } else if (gw.getLotteryId() == 2L) {
                        userTcWinMap.merge(gw.getUserId(), gw.getWinAmount(), BigDecimal::add);
                    }
                }
            }

            log.info("[批量数据同步] userFcWinMap: {}", userFcWinMap);
            log.info("[批量数据同步] userTcWinMap: {}", userTcWinMap);

            // 使用原子性增量更新 game_customer 表的中奖金额（解决并发问题）
            for (Map.Entry<Long, BigDecimal> entry : userWinMap.entrySet()) {
                Long userId = entry.getKey();
                BigDecimal currentWinAmount = entry.getValue(); // 本次结算的中奖金额

                // 使用原子性增量更新，避免并发问题
                int totalResult = gameCustomerMapper.atomicUpdateTotalWinAmount(userId, currentUserId, currentWinAmount);
                log.info("[批量数据同步-原子性] 用户{} 总中奖金额增加{}, 影响行数: {}", userId, currentWinAmount, totalResult);

                // 按彩种原子性更新中奖金额
                BigDecimal fcWinAmount = userFcWinMap.getOrDefault(userId, BigDecimal.ZERO);
                BigDecimal tcWinAmount = userTcWinMap.getOrDefault(userId, BigDecimal.ZERO);

                if (fcWinAmount.compareTo(BigDecimal.ZERO) > 0) {
                    int fcResult = gameCustomerMapper.atomicUpdateLotteryWinAmount(userId, currentUserId, 1L, fcWinAmount);
                    log.info("[批量数据同步-原子性] 用户{} 福彩中奖金额增加{}, 影响行数: {}", userId, fcWinAmount, fcResult);
                }

                if (tcWinAmount.compareTo(BigDecimal.ZERO) > 0) {
                    int tcResult = gameCustomerMapper.atomicUpdateLotteryWinAmount(userId, currentUserId, 2L, tcWinAmount);
                    log.info("[批量数据同步-原子性] 用户{} 体彩中奖金额增加{}, 影响行数: {}", userId, tcWinAmount, tcResult);
                }

                log.info("[批量数据同步-原子性] 用户ID:{} (系统用户:{}) 本次中奖金额:{} 已原子性累加", userId, currentUserId, currentWinAmount);
            }

            // 按期号更新 game_serial 表的中奖金额
            for (String issueNumber : issueGroupMap.keySet()) {
                updateGameSerialWinMoney(issueNumber, currentUserId);
            }

            log.info("[批量数据同步] 数据同步完成，共处理 {} 个用户的中奖数据", userWinMap.size());

        } catch (Exception e) {
            log.error("[批量数据同步] 同步用户总中奖金额失败", e);
            // 注意：这里不抛出异常，避免影响主流程
        }
    }

    /**
     * 更新 game_serial 表的中奖金额
     */
    private void updateGameSerialWinMoney(String issueNumber, Long sysUserId) {
        try {
            log.info("[流水中奖金额更新] 开始更新期号{}用户{}的流水中奖金额", issueNumber, sysUserId);

            // 使用game_winning表来统计流水中奖金额（更准确）
            List<GameWinning> winningRecords = gameWinningMapper.selectByIssueNumberAndSysUser(issueNumber, sysUserId);

            // 按流水号分组统计中奖金额
            Map<Integer, BigDecimal> serialWinMap = new HashMap<>();
            for (GameWinning winning : winningRecords) {
                if (winning.getSerialNumber() != null && winning.getWinAmount() != null) {
                    serialWinMap.merge(winning.getSerialNumber(), winning.getWinAmount(), BigDecimal::add);
                }
            }

            // 更新每个流水号的中奖金额
            for (Map.Entry<Integer, BigDecimal> entry : serialWinMap.entrySet()) {
                Integer serialNumber = entry.getKey();
                BigDecimal totalWinAmount = entry.getValue();

                log.info("[流水中奖金额更新] 用户{}流水号{}本期中奖金额: {}", sysUserId, serialNumber, totalWinAmount);

                // 更新该用户该流水号的中奖金额
                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("serialNumbers", serialNumber);
                updateParams.put("serialWinMoney", totalWinAmount);
                updateParams.put("sysUserId", sysUserId);

                int updateResult = gameSerialMapper.updateSerialWinMoneyByUser(updateParams);
                if (updateResult > 0) {
                    log.info("[流水中奖金额更新] 用户{}流水号{}的中奖金额已更新为{}", sysUserId, serialNumber, totalWinAmount);
                } else {
                    log.warn("[流水中奖金额更新] 用户{}流水号{}更新失败", sysUserId, serialNumber);
                }
            }

            log.info("[流水中奖金额更新] 期号{}的流水中奖金额更新完成，共更新{}个流水", issueNumber, serialWinMap.size());

        } catch (Exception e) {
            log.error("[流水中奖金额更新] 更新期号{}的流水中奖金额时发生错误", issueNumber, e);
        }
    }

    /**
     * 结算指定期号的开奖
     */
    @Override
    public void settleGameDraw(String qihao) {
        Long currentUserId = DataPermissionUtils.getCurrentUserId();

        // 【第一步：数据预加载到内存缓存 - 避免长时间占用MySQL】
        if (!loadDataToMemoryCache(currentUserId)) {
            throw new ServiceException("系统繁忙，数据加载中，请稍后再试");
        }

        // 【第一步补充：预加载赔率缓存 - 避免结算时频繁查询赔率】
        oddsCacheManager.loadUserOdds(currentUserId);

        try {
            // 【第二步：开奖前draw数据一致性检查和修复 - 使用内存缓存数据】
            if (!performDataConsistencyCheckWithMemoryCache(currentUserId)) {
                throw new ServiceException("系统繁忙，draw数据统计中，请5秒后再试");
            }

            // 【第三步：开奖前customer数据一致性检查和修复 - 使用内存缓存数据】
            if (!performCustomerDataConsistencyCheckWithMemoryCache(currentUserId)) {
                throw new ServiceException("系统繁忙，customer数据统计中，请5秒后再试");
            }

        } finally {
            // 【清理内存缓存】无论成功失败都要清理内存
            recordDataCacheManager.clearUserData(currentUserId);
            oddsCacheManager.clearUserOdds(currentUserId);
        }

        // 尝试获取结算锁，包含期号锁，超时时间90秒
        if (!settlementLockManager.acquireSettlementLock(currentUserId, qihao, 90)) {
            throw new ServiceException("系统繁忙，请稍后再试。期号 " + qihao + " 可能正在被其他用户结算。");
        }

        try {
            log.info("用户 {} 开始处理期号 {} 的开奖结算，{}", currentUserId, qihao, settlementLockManager.getSystemLoadInfo());

            // 在事务中执行结算逻辑
            settleGameDrawInTransaction(qihao, currentUserId);

            log.info("用户 {} 期号 {} 的开奖结算处理完成", currentUserId, qihao);

        } catch (Exception e) {
            log.error("用户 {} 处理期号 {} 的开奖结算时发生错误: {}", currentUserId, qihao, e.getMessage(), e);
            throw new RuntimeException("处理开奖结算时发生错误: " + e.getMessage(), e);
        } finally {
            // 释放结算锁
            settlementLockManager.releaseSettlementLock(currentUserId, qihao);
            log.info("用户 {} 期号 {} 结算锁已释放，{}", currentUserId, qihao, settlementLockManager.getSystemLoadInfo());
        }
    }

    /**
     * 在事务中处理单期开奖结算
     */
    @Transactional(rollbackFor = Exception.class)
    private void settleGameDrawInTransaction(String qihao, Long currentUserId) {
        // 1. 获取指定期号的开奖记录
        GameDraw draw = gameDrawMapper.selectByQihao(qihao);
        if (draw == null) {
            String errorMsg = "未找到指定期号的开奖记录: " + qihao;
            log.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }

        // 验证数据权限
        boolean isAdmin = DataPermissionUtils.isAdmin();
        if (!isAdmin) {
            DataPermissionUtils.validateDataOwnership(draw.getSysUserId(), "无权结算该期号的开奖记录");
        }

        // 直接处理，不使用异步（因为已经有锁控制）
        processSingleDraw(draw, currentUserId, isAdmin);
    }

    /**
     * 处理指定用户的开奖结算
     * @param targetUserId 目标用户ID
     */
    @Override
    public void processDrawSettlementForUser(Long targetUserId) {
        Long currentUserId = GameUserUtils.getCurrentSysUserId();

        // 只有管理员可以为其他用户结算
        if (!DataPermissionUtils.isAdmin()) {
            throw new ServiceException("无权限为其他用户进行结算");
        }

        log.info("管理员 {} 开始为用户 {} 进行开奖结算", currentUserId, targetUserId);

        // 【第一步：数据预加载到内存缓存 - 避免长时间占用MySQL】
        if (!loadDataToMemoryCache(targetUserId)) {
            throw new ServiceException("系统繁忙，数据加载中，请稍后再试");
        }

        try {
            // 【第二步：开奖前draw数据一致性检查和修复 - 使用内存缓存数据】
            if (!performDataConsistencyCheckWithMemoryCache(targetUserId)) {
                throw new ServiceException("系统繁忙，draw数据统计中，请5秒后再试");
            }

            // 【第三步：开奖前customer数据一致性检查和修复 - 使用内存缓存数据】
            if (!performCustomerDataConsistencyCheckWithMemoryCache(targetUserId)) {
                throw new ServiceException("系统繁忙，customer数据统计中，请5秒后再试");
            }

        } finally {
            // 【清理内存缓存】无论成功失败都要清理内存
            recordDataCacheManager.clearUserData(targetUserId);
        }

        // 尝试获取结算锁，超时时间90秒
        if (!settlementLockManager.acquireSettlementLock(currentUserId, null, 90)) {
            throw new ServiceException("系统繁忙，请稍后再试。当前有太多用户正在进行结算操作。");
        }

        try {
            log.info("管理员 {} 开始为用户 {} 批量结算，{}", currentUserId, targetUserId, settlementLockManager.getSystemLoadInfo());

            // 在事务中执行结算逻辑
            processDrawSettlementForUserInTransaction(targetUserId);

            log.info("管理员为用户 {} 开奖结算处理完成", targetUserId);

        } catch (Exception e) {
            log.error("管理员 {} 为用户 {} 处理开奖结算时发生错误: {}", currentUserId, targetUserId, e.getMessage(), e);
            throw new ServiceException("处理开奖结算时发生错误: " + e.getMessage());
        } finally {
            // 释放结算锁
            settlementLockManager.releaseSettlementLock(currentUserId, null);
            log.info("管理员 {} 为用户 {} 结算锁已释放，{}", currentUserId, targetUserId, settlementLockManager.getSystemLoadInfo());
        }
    }

    /**
     * 撤销指定用户的结算
     * @param drawId 开奖ID
     * @param targetUserId 目标用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelPayForUser(Long drawId, Long targetUserId) {
        Long currentUserId = GameUserUtils.getCurrentSysUserId();

        // 只有管理员可以为其他用户撤销结算
        if (!DataPermissionUtils.isAdmin()) {
            throw new ServiceException("无权限为其他用户撤销结算");
        }

        log.info("管理员 {} 开始为用户 {} 撤销开奖ID {} 的结算", currentUserId, targetUserId, drawId);

        // 尝试获取撤销锁，超时时间30秒
        if (!settlementLockManager.acquireCancelLock(currentUserId, 30)) {
            throw new ServiceException("系统繁忙，请稍后再试。当前有太多用户正在进行撤销操作。");
        }

        try {
            // 1. 获取开奖记录并验证权限
            GameDraw gameDraw = gameDrawMapper.selectGameDrawByDrawId(drawId);
            if (gameDraw == null) {
                throw new ServiceException("开奖记录不存在");
            }

            // 验证是否为目标用户的记录
            if (!targetUserId.equals(gameDraw.getSysUserId())) {
                throw new ServiceException("开奖记录不属于指定用户");
            }

            // 2. 获取该期号该用户的中奖记录
            Map<String, Object> winningParams = new HashMap<>();
            winningParams.put("issueNumber", gameDraw.getQihao());
            winningParams.put("sysUserId", targetUserId);
            List<GameWinning> winningRecords = gameWinningMapper.selectByIssueNumberAndUser(winningParams);

            // 3. 获取该期号该用户的投注记录
            Map<String, Object> recordParams = new HashMap<>();
            recordParams.put("issueNumber", gameDraw.getQihao());
            recordParams.put("sysUserId", targetUserId);
            List<GameRecord> records = gameRecordMapper.selectByIssueNumberAndUser(recordParams);

            // 4. 删除中奖记录
            for (GameWinning winning : winningRecords) {
                gameWinningMapper.deleteGameWinningByWinId(winning.getWinId());
            }

            // 5. 重置投注记录状态
            for (GameRecord record : records) {
                Map<String, Object> updateParams = new HashMap<>();
                updateParams.put("betId", record.getBetId());
                updateParams.put("shifouzj", 0L);
                updateParams.put("jiesuan", 0L);
                updateParams.put("winAmount", BigDecimal.ZERO);
                updateParams.put("drawTime", null);
                updateParams.put("winningNumbers", null);
                updateParams.put("isDrawn", 0);
                gameRecordMapper.updateGameRecordByBetId(updateParams);
            }

            // 6. 重置开奖记录状态
            gameDraw.setJiesuan(0);
            gameDraw.setZongjiesuan(BigDecimal.ZERO);
            gameDraw.setYingli(BigDecimal.ZERO);
            gameDrawMapper.updateGameDraw(gameDraw);

            // 7. 重置流水记录的中奖金额
            gameSerialMapper.resetSerialWinMoneyByUser(targetUserId);

            log.info("管理员 {} 为用户 {} 撤销开奖ID {} 的结算完成", currentUserId, targetUserId, drawId);
            return 1;

        } catch (Exception e) {
            log.error("管理员 {} 为用户 {} 撤销开奖ID {} 的结算时发生错误: {}", currentUserId, targetUserId, drawId, e.getMessage(), e);
            throw new ServiceException("撤销结算时发生错误: " + e.getMessage());
        } finally {
            // 释放撤销锁
            settlementLockManager.releaseCancelLock(currentUserId);
            log.info("管理员 {} 为用户 {} 撤销开奖ID {} 的锁已释放", currentUserId, targetUserId, drawId);
        }
    }

    /**
     * 在事务中处理管理员为用户的开奖结算
     */
    @Transactional(rollbackFor = Exception.class)
    private void processDrawSettlementForUserInTransaction(Long targetUserId) {
        // 获取目标用户的未结算开奖记录
        List<GameDraw> unsettledDraws = gameDrawMapper.selectUnsettledDrawsByUser(targetUserId);

        if (unsettledDraws == null || unsettledDraws.isEmpty()) {
            log.info("用户 {} 没有未结算的开奖记录", targetUserId);
            return;
        }

        log.info("为用户 {} 找到 {} 条未结算开奖记录", targetUserId, unsettledDraws.size());

        // 同步处理开奖记录（在事务中不使用并行处理）
        for (GameDraw draw : unsettledDraws) {
            try {
                processSingleDrawForTargetUser(draw, targetUserId);
            } catch (Exception e) {
                log.error("处理用户 {} 开奖记录失败: {}", targetUserId, draw.getQihao(), e);
                throw new RuntimeException("处理开奖记录失败", e);
            }
        }

        log.info("用户 {} 所有开奖记录处理完成，共处理 {} 条记录", targetUserId, unsettledDraws.size());
    }

    /**
     * 专门处理指定用户的单个开奖记录结算
     * 用于管理员为指定用户进行结算
     */
    private void processSingleDrawForTargetUser(GameDraw draw, Long targetUserId) {
        try {
            log.info("开始处理用户 {} 的开奖记录，期号: {}, 开奖ID: {}", targetUserId, draw.getQihao(), draw.getDrawId());

            // 验证开奖记录属于目标用户
            if (!targetUserId.equals(draw.getSysUserId())) {
                throw new ServiceException("开奖记录不属于指定用户");
            }

            // 获取该期该用户的投注记录
            Map<String, Object> params = new HashMap<>();
            params.put("issueNumber", draw.getQihao());
            params.put("sysUserId", targetUserId); // 使用目标用户ID查询
            List<GameRecord> records = gameRecordMapper.selectByIssueNumberAndUser(params);

            if (records == null || records.isEmpty()) {
                log.info("期号 {} 用户 {} 没有投注记录", draw.getQihao(), targetUserId);
                // 没有投注记录，直接标记为已结算
                draw.setJiesuan(1);
                gameDrawMapper.updateGameDraw(draw);
                return;
            }

            log.info("期号 {} 用户 {} 找到 {} 条投注记录", draw.getQihao(), targetUserId, records.size());

            // 处理每条投注记录
            BigDecimal totalBetAmount = BigDecimal.ZERO;
            BigDecimal totalWinAmount = BigDecimal.ZERO;

            for (GameRecord record : records) {
                totalBetAmount = totalBetAmount.add(record.getMoney());

                // 判断是否中奖 - 使用现有的checkWinning方法
                boolean isWinning = checkWinning(record, draw);
                log.info("[中奖判断] betId={}, methodId={}, isWinning={}", record.getBetId(), record.getMethodId(), isWinning);

                if (isWinning) {
                    log.info("[中奖处理] betId={}, methodId={}, 开始处理中奖记录", record.getBetId(), record.getMethodId());

                    // 计算中奖金额 - 使用现有的calculateWinningAmount方法
                    BigDecimal winAmount = calculateWinningAmount(record, record.getMethodId(), targetUserId);
                    totalWinAmount = totalWinAmount.add(winAmount);

                    // 创建中奖记录
                    GameWinning winning = new GameWinning();
                    winning.setUserId(record.getUserId());
                    winning.setSysUserId(targetUserId); // 使用目标用户ID
                    winning.setLotteryId(record.getLotteryId());
                    winning.setMethodId(record.getMethodId());
                    winning.setBetNumber(record.getBetNumbers()); // 修正方法名
                    winning.setWinningNumbers(record.getWinningNumbers()); // 使用record中的中奖号码
                    winning.setWinAmount(winAmount);
                    winning.setIssueNumber(record.getIssueNumber());
                    winning.setSerialNumber(record.getSerialNumber());
                    winning.setShibie(record.getShibie());

                    // 插入中奖记录
                    int winId = gameWinningMapper.insertGameWinning(winning);
                    log.info("[中奖记录插入] betId={}, methodId={}, userId={}, sysUserId={}, winAmount={}, serialNumber={}, shibie={}",
                            record.getBetId(), record.getMethodId(), record.getUserId(), targetUserId, winAmount,
                            record.getSerialNumber(), record.getShibie());

                    // 更新投注记录
                    record.setShifouzj(1L); // 已中奖
                    record.setWinAmount(winAmount);
                } else {
                    record.setShifouzj(0L); // 未中奖
                    record.setWinAmount(BigDecimal.ZERO);
                }

                // 更新投注记录状态
                record.setJiesuan(1L); // 已结算
                record.setDrawTime(new Date());
                record.setWinningNumbers(draw.getKaijianghao());
                record.setIsDrawn(1);
                gameRecordMapper.updateGameRecord(record);
            }

            // 更新开奖记录
            draw.setZongtouzhu(totalBetAmount);
            draw.setZongjiesuan(totalWinAmount);
            draw.setYingli(totalBetAmount.subtract(totalWinAmount));
            draw.setJiesuan(1); // 已结算
            gameDrawMapper.updateGameDraw(draw);

            log.info("期号 {} 用户 {} 结算完成 - 总投注金额: {}, 总中奖金额: {}, 盈利金额: {}",
                    draw.getQihao(), targetUserId, totalBetAmount, totalWinAmount, draw.getYingli());

            // 同步用户中奖金额 - 使用现有方法，传入正确的参数
            // 这里我们需要重新计算该用户的总中奖金额并同步
            syncTargetUserWinningAmounts(targetUserId, draw.getQihao());

            // 更新流水记录的中奖金额
            updateGameSerialWinMoney(draw.getQihao(), targetUserId);

        } catch (Exception e) {
            log.error("处理用户 {} 开奖记录失败，期号: {}", targetUserId, draw.getQihao(), e);
            throw new RuntimeException("处理开奖记录失败", e);
        }
    }

    /**
     * 同步指定用户的中奖金额到game_customer表
     */
    private void syncTargetUserWinningAmounts(Long targetUserId, String issueNumber) {
        try {
            // 查询该期号该用户的中奖记录
            Map<String, Object> params = new HashMap<>();
            params.put("issueNumber", issueNumber);
            params.put("sysUserId", targetUserId);
            List<GameWinning> winningRecords = gameWinningMapper.selectByIssueNumberAndUser(params);

            // 按用户ID分组统计中奖金额
            Map<Long, BigDecimal> userFcWinMap = new HashMap<>();
            Map<Long, BigDecimal> userTcWinMap = new HashMap<>();

            for (GameWinning winning : winningRecords) {
                Long userId = winning.getUserId();
                BigDecimal winAmount = winning.getWinAmount();

                if (winning.getLotteryId() == 1) { // 福彩3D
                    userFcWinMap.merge(userId, winAmount, BigDecimal::add);
                } else if (winning.getLotteryId() == 2) { // 体彩排三
                    userTcWinMap.merge(userId, winAmount, BigDecimal::add);
                }
            }

            // 同步福彩中奖金额
            for (Map.Entry<Long, BigDecimal> entry : userFcWinMap.entrySet()) {
                Long userId = entry.getKey();
                BigDecimal fcZj = gameWinningMapper.sumWinAmountByUserIdAndLotteryIdAndSysUser(userId, 1L, targetUserId);
                gameCustomerMapper.updateFcZj(userId, targetUserId, fcZj);
                log.info("[结算同步] userId={}, fcZj={} (用户隔离)", userId, fcZj);
            }

            // 同步体彩中奖金额
            for (Map.Entry<Long, BigDecimal> entry : userTcWinMap.entrySet()) {
                Long userId = entry.getKey();
                BigDecimal tcZj = gameWinningMapper.sumWinAmountByUserIdAndLotteryIdAndSysUser(userId, 2L, targetUserId);
                gameCustomerMapper.updateTcZj(userId, targetUserId, tcZj);
                log.info("[结算同步] userId={}, tcZj={} (用户隔离)", userId, tcZj);
            }

        } catch (Exception e) {
            log.error("同步用户 {} 中奖金额失败", targetUserId, e);
        }
    }

    /**
     * 判断是否中奖
     * 
     * @param gameRecord 投注记录
     * @param draw       开奖记录
     * @return 是否中奖
     */
    private boolean checkWinning(GameRecord gameRecord, GameDraw draw) {
        try {
            Long methodId = gameRecord.getMethodId();
            String[] winningArray = draw.getKaijianghao().split(",");
            JSONArray winningNumbersArray = new JSONArray();
            boolean isWinning = false;

            // 特殊玩法（单双、大小、和值、包打组三、包打组六、豹子、杀码）不需要检查bet_numbers
            if (methodId >= 21 && methodId <= 27) { // 和值玩法
                if (gameRecord.getHezhi() == null) {
                    log.error("和值参数为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                isWinning = checkHeZhi(gameRecord.getHezhi(), draw.getHezhi());
                if (isWinning) {
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", String.valueOf(gameRecord.getHezhi()));
                    winningNumbersArray.add(winningObj);
                }
            } else if (methodId == 29) { // 和值单双
                if (gameRecord.getDanshuang() == null) {
                    log.error("单双参数为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                isWinning = checkHeZhiDanShuang(gameRecord.getDanshuang(), draw.getDanshuang());
                if (isWinning) {
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", gameRecord.getDanshuang() == 200 ? "单" : "双");
                    winningNumbersArray.add(winningObj);
                }
            } else if (methodId == 30) { // 和值大小
                if (gameRecord.getDaxiao() == null) {
                    log.error("大小参数为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                isWinning = checkHeZhiDaXiao(gameRecord.getDaxiao(), draw.getDaxiao());
                if (isWinning) {
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", gameRecord.getDaxiao() == 300 ? "大" : "小");
                    winningNumbersArray.add(winningObj);
                }
            } else if (methodId == 28) { // 和值
                if (gameRecord.getHezhi() == null) {
                    log.error("和值参数为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                isWinning = checkHeZhi(gameRecord.getHezhi(), draw.getHezhi());
                if (isWinning) {
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", String.valueOf(gameRecord.getHezhi()));
                    winningNumbersArray.add(winningObj);
                }
            } else if (methodId >= 37 && methodId <= 43) { // 新增的和值玩法
                if (gameRecord.getHezhi() == null) {
                    log.error("和值参数为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                // 使用原有的和值判断方法：投注和值 == 开奖和值
                isWinning = checkHeZhi(gameRecord.getHezhi(), draw.getHezhi());
                if (isWinning) {
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", String.valueOf(gameRecord.getHezhi()));
                    winningNumbersArray.add(winningObj);
                }
            } else if (methodId == 20) { // 包打组六
                // 直接判断开奖号码的duizi是否为0
                if (draw.getDuizi() == 0) {
                    isWinning = true;
                    JSONObject winningObj = new JSONObject();
                    JSONArray winningList = new JSONArray();
                    JSONObject winningNumber = new JSONObject();
                    winningNumber.put("a", "包打组六");
                    winningList.add(winningNumber);
                    winningObj.put("winning", winningList);
                    gameRecord.setWinningNumbers(winningObj.toJSONString());
                }
            } else if (methodId == 31) { // 包打组三
                // 直接判断开奖号码的duizi是否为1
                if (draw.getDuizi() == 1) {
                    isWinning = true;
                    JSONObject winningObj = new JSONObject();
                    JSONArray winningList = new JSONArray();
                    JSONObject winningNumber = new JSONObject();
                    winningNumber.put("a", "包打组三");
                    winningList.add(winningNumber);
                    winningObj.put("winning", winningList);
                    gameRecord.setWinningNumbers(winningObj.toJSONString());
                }
            } else if (methodId == 32) { // 豹子
                isWinning = checkBaoZi(winningArray);
                if (isWinning) {
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", "豹子");
                    winningNumbersArray.add(winningObj);
                }
            } else if (methodId >= 44 && methodId <= 59) { // 胆拖玩法 (44-59)
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                isWinning = checkDantuoWinning(gameRecord, winningArray);
                if (isWinning) {
                    // 设置胆拖中奖号码
                    JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
                    JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");
                    if (numbersArray != null && numbersArray.size() > 0) {
                        JSONObject dantuoInfo = numbersArray.getJSONObject(0);
                        JSONObject winningObj = new JSONObject();
                        winningObj.put("danma", dantuoInfo.getString("danma"));
                        winningObj.put("tuoma", dantuoInfo.getString("tuoma"));
                        winningNumbersArray.add(winningObj);
                    }
                }
            } else if (methodId >= 60 && methodId <= 69) { // 跨度玩法 (60-69)
                isWinning = checkKuaduWinning(gameRecord, winningArray);
                if (isWinning) {
                    // 设置跨度中奖号码
                    int kuaduValue = (int) (methodId - 60);
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("kuadu", kuaduValue);
                    winningNumbersArray.add(winningObj);
                }
            } else if (methodId == 33) { // 杀码
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                isWinning = checkShaMa(JSON.parseObject(gameRecord.getBetNumbers()).getJSONArray("numbers"),
                        winningArray);
                if (isWinning) {
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", "杀码");
                    winningNumbersArray.add(winningObj);
                }
            } else if (methodId == 1) { // 独胆
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
                JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");
                isWinning = checkDuDan(numbersArray, winningArray);
            } else if (methodId == 2) { // 一码定位
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                if (gameRecord.getDingwei() == null) {
                    log.error("定位参数为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }

                JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
                JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");

                // 根据dingwei值选择对应的开奖号码位置
                int position = 0;
                if (gameRecord.getDingwei() == 100) {
                    position = 0; // 第一个位置
                } else if (gameRecord.getDingwei() == 101) {
                    position = 1; // 第二个位置
                } else if (gameRecord.getDingwei() == 102) {
                    position = 2; // 第三个位置
                } else {
                    log.error("无效的定位参数: {} - 投注记录ID: {}", gameRecord.getDingwei(), gameRecord.getBetId());
                    return false;
                }

                String targetNumber = winningArray[position];
                boolean hasWinning = false;
                JSONArray winningList = new JSONArray();

                // 遍历投注号码，检查是否与目标位置的开奖号码匹配
                for (int i = 0; i < numbersArray.size(); i++) {
                    JSONObject betNumber = numbersArray.getJSONObject(i);
                    String betA = betNumber.getString("a");
                    if (targetNumber.equals(betA)) {
                        hasWinning = true;
                        JSONObject winningObj = new JSONObject();
                        winningObj.put("a", betA);
                        winningList.add(winningObj);
                    }
                }

                if (hasWinning) {
                    isWinning = true;
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("winning", winningList);
                    gameRecord.setWinningNumbers(winningObj.toJSONString());
                }
            } else if (methodId == 3) { // 两码组合
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
                JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");
                isWinning = checkLiangMaZuHe(numbersArray, winningArray, gameRecord);
            } else if (methodId == 4) { // 两码对子
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
                JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");
                isWinning = checkLiangMaDuiZi(numbersArray, winningArray, gameRecord);
            } else if (methodId == 5) { // 三码直选
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
                JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");
                isWinning = checkSanMaZhiXuan(numbersArray, winningArray);
            } else if (methodId == 6) { // 三码组选
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
                JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");
                isWinning = checkSanMaZuXuan(numbersArray, winningArray);
            } else if (methodId == 7) { // 三码组三
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
                JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");
                isWinning = checkSanMaZuSan(numbersArray, winningArray, draw.getDuizi(), gameRecord, draw);
            } else if (methodId == 8) { // 四码组六
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
                JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");
                isWinning = checkSiMaZuLiu(numbersArray, winningArray, gameRecord, draw);

            } else if (methodId >= 9 && methodId <= 19 && methodId % 2 == 1) { // 多码组三
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }

                JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
                JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");
                isWinning = checkMultiMaZuSan(numbersArray, winningArray, draw.getDuizi(), gameRecord, draw, methodId);
            } else if (methodId >= 10 && methodId <= 18 && methodId % 2 == 0) { // 多码组六
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
                JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");
                isWinning = checkMultiMaZuLiu(numbersArray, winningArray, gameRecord, draw, methodId);

            } else if (methodId == 34) { // 两码定位
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
                JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");
                // 两码定位：逐组判断每个三位数模式
                isWinning = checkLiangMaDingWeiByPattern(numbersArray, winningArray, gameRecord);
            } else if (methodId == 35) { // 一码单双
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                if (gameRecord.getDingwei() == null) {
                    log.error("定位参数为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
                JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");
                isWinning = checkYiMaDanShuang(numbersArray, winningArray, gameRecord.getDingwei(), gameRecord);
            } else if (methodId == 36) { // 一码大小
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                if (gameRecord.getDingwei() == null) {
                    log.error("定位参数为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
                JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");
                isWinning = checkYiMaDaXiao(numbersArray, winningArray, gameRecord.getDingwei(), gameRecord);
            } else if (methodId == 70) { // 两码组三
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
                JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");
                isWinning = checkLiangMaZuSan(numbersArray, winningArray, draw.getDuizi(), gameRecord);
            } else if (methodId == 100) { // 三码防对
                if (gameRecord.getBetNumbers() == null) {
                    log.error("投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                    return false;
                }
                JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
                JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");
                isWinning = checkSanMaFangDui(numbersArray, winningArray, draw.getDuizi());
                if (isWinning) {
                    // 使用新的方法获取所有中奖号码
                    JSONArray winningList = getSanMaFangDuiWinningNumbers(numbersArray, winningArray, draw.getDuizi());
                    if (!winningList.isEmpty()) {
                        JSONObject winningObj = new JSONObject();
                        winningObj.put("winning", winningList);
                        gameRecord.setWinningNumbers(winningObj.toJSONString());
                        log.info("[三码防对中奖] betId={}, 中奖组数={}, 中奖号码={}",
                                gameRecord.getBetId(), winningList.size(), winningList.toJSONString());
                    }
                }
            } else {
                log.error("未知的玩法ID: {}", methodId);
                return false;
            }

            if (isWinning && !winningNumbersArray.isEmpty()) {
                JSONObject winningObj = new JSONObject();
                winningObj.put("winning", winningNumbersArray);
                gameRecord.setWinningNumbers(winningObj.toJSONString());
            }

            return isWinning;
        } catch (Exception e) {
            log.error("检查中奖时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 计算中奖金额
     * 
     * @param gameRecord 投注记录
     * @param methodId   玩法ID
     * @return 中奖金额
     */
    private BigDecimal calculateWinningAmount(GameRecord gameRecord, Long methodId, Long currentUserId) {
        try {
            // 获取用户自定义赔率
            BigDecimal odds = gameMethodOddsMapper.getOddsByMethodAndUser(methodId, currentUserId);
            if (odds == null) {
                log.error("未找到用户 {} 的玩法 {} 赔率配置", currentUserId, methodId);
                return BigDecimal.ZERO;
            }

            // 获取中奖号码
            String winningNumbers = gameRecord.getWinningNumbers();
            if (StringUtils.isEmpty(winningNumbers)) {
                return BigDecimal.ZERO;
            }

            // 解析中奖号码
            JSONObject winningJson = JSON.parseObject(winningNumbers);
            JSONArray winningArray = winningJson.getJSONArray("winning");
            if (winningArray == null || winningArray.isEmpty()) {
                return BigDecimal.ZERO;
            }

            // 计算中奖组数
            int winningGroups = winningArray.size();
            log.info("中奖组数: {}", winningGroups);

            // 获取投注金额
            BigDecimal betAmount = gameRecord.getMoney();

            // 计算中奖金额
            BigDecimal winAmount = betAmount.multiply(odds).multiply(new BigDecimal(winningGroups));
            log.info("普通玩法中奖金额计算 - 用户: {}, 玩法ID: {}, 投注金额: {}, 赔率: {}, 中奖组数: {}, 中奖金额: {}",
                    currentUserId, methodId, betAmount, odds, winningGroups, winAmount);

            return winAmount;
        } catch (Exception e) {
            log.error("计算中奖金额时发生错误: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 独胆判断
     * 判断开奖号码中是否包含投注号码
     * 如果开奖号码中有任意一个数字与投注号码相同，返回true
     */
    private boolean checkDuDan(JSONArray numbersArray, String[] winningArray) {
        for (int i = 0; i < numbersArray.size(); i++) {
            JSONObject betObj = numbersArray.getJSONObject(i);
            int betA = betObj.getInteger("a");
            for (String winningNumber : winningArray) {
                if (Integer.parseInt(winningNumber) == betA) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 一码定位判断
     * 判断开奖号码指定位置的数字是否与投注号码匹配
     * 投注号码格式：{"numbers": [{"a": 8}, {"a": 1}, {"a": 2}, {"a": 0}, {"a": 3}]}
     * 定位值：
     * 100 - 百位
     * 101 - 十位
     * 102 - 个位
     */
    private boolean checkYiMaDingWei(JSONArray numbersArray, String[] winningArray, Long dingwei,
            GameRecord gameRecord) {
        try {
            if (numbersArray == null || numbersArray.isEmpty() || winningArray == null || winningArray.length != 3
                    || dingwei == null) {
                return false;
            }
            int position = 0;
            if (dingwei == 100) {
                position = 0;
            } else if (dingwei == 101) {
                position = 1;
            } else if (dingwei == 102) {
                position = 2;
            } else {
                log.error("无效的定位参数: {} - 投注记录ID: {}", dingwei, gameRecord.getBetId());
                return false;
            }
            boolean hasWinning = false;
            JSONArray winningCombinations = new JSONArray();
            for (int i = 0; i < numbersArray.size(); i++) {
                JSONObject betNumber = numbersArray.getJSONObject(i);
                String betNum = betNumber.getString("a");
                if (betNum.equals(winningArray[position])) {
                    hasWinning = true;
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", betNum);
                    winningCombinations.add(winningObj);
                }
            }
            if (hasWinning) {
                JSONObject winningNumbers = new JSONObject();
                winningNumbers.put("winning", winningCombinations);
                gameRecord.setWinningNumbers(winningNumbers.toJSONString());
                log.info("一码定位中奖 - 投注号码: {}, 中奖号码: {}, 位置: {}", gameRecord.getBetNumbers(),
                        gameRecord.getWinningNumbers(), dingwei);
            }
            return hasWinning;
        } catch (Exception e) {
            log.error("检查一码定位中奖时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 两码组合判断
     * 判断开奖号码中是否包含投注的两个号码组合
     * 投注号码格式：{"numbers": [{"a": 第一个号码, "b": 第二个号码}, ...]}
     * 不考虑位置，只要开奖号码中包含这两个号码即可
     */
    private boolean checkLiangMaZuHe(JSONArray numbersArray, String[] winningArray, GameRecord gameRecord) {
        // 统计开奖号码中每个数字出现的次数
        Map<Integer, Integer> winningCount = new HashMap<>();
        for (String num : winningArray) {
            int n = Integer.parseInt(num);
            winningCount.put(n, winningCount.getOrDefault(n, 0) + 1);
        }

        // 存储中奖的组合
        JSONArray winningCombinations = new JSONArray();

        // 遍历每组投注号码
        for (int i = 0; i < numbersArray.size(); i++) {
            JSONObject betObj = numbersArray.getJSONObject(i);
            int betA = betObj.getInteger("a");
            int betB = betObj.getInteger("b");

            // 检查开奖号码中是否同时包含投注的两个号码
            // 如果两个号码相同，需要确保开奖号码中该号码至少出现两次
            if (betA == betB) {
                if (winningCount.getOrDefault(betA, 0) >= 2) {
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", String.valueOf(betA));
                    winningObj.put("b", String.valueOf(betB));
                    winningCombinations.add(winningObj);
                }
            } else {
                // 如果两个号码不同，只需要确保开奖号码中包含这两个号码
                if (winningCount.containsKey(betA) && winningCount.containsKey(betB)) {
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", String.valueOf(betA));
                    winningObj.put("b", String.valueOf(betB));
                    winningCombinations.add(winningObj);
                }
            }
        }

        // 如果有中奖组合，更新中奖号码
        if (!winningCombinations.isEmpty()) {
            JSONObject winningJson = new JSONObject();
            winningJson.put("winning", winningCombinations);
            gameRecord.setWinningNumbers(winningJson.toJSONString());
            log.info("两码组合中奖号码: {}", winningJson.toJSONString());
            return true;
        }

        return false;
    }

    /**
     * 两码对子判断
     * 判断开奖号码中是否包含投注的对子号码
     * 投注号码格式：{"numbers": [{"a": 2,"b": 2}, {"a": 6,"b": 6}, ...]}
     * 要求：
     * 1. 开奖号码中必须有两个或三个相同的数字
     * 2. 投注的对子号码必须与开奖号码中的对子匹配
     */
    private boolean checkLiangMaDuiZi(JSONArray numbersArray, String[] winningArray, GameRecord gameRecord) {
        try {
            // 统计开奖号码中每个数字出现的次数
            Map<String, Integer> numberCount = new HashMap<>();
            for (String num : winningArray) {
                numberCount.put(num, numberCount.getOrDefault(num, 0) + 1);
            }

            // 找出开奖号码中出现两次或三次的数字
            List<String> duiziNumbers = new ArrayList<>();
            for (Map.Entry<String, Integer> entry : numberCount.entrySet()) {
                if (entry.getValue() >= 2) {
                    duiziNumbers.add(entry.getKey());
                }
            }

            if (duiziNumbers.isEmpty()) {
                log.info("开奖号码中没有对子: {}", Arrays.toString(winningArray));
                return false;
            }

            log.info("开奖号码中的对子数字: {}", duiziNumbers);

            // 存储中奖的对子
            JSONArray winningNumbers = new JSONArray();
            boolean hasWinning = false;

            // 检查投注号码中是否有匹配的对子
            for (int i = 0; i < numbersArray.size(); i++) {
                JSONObject betObj = numbersArray.getJSONObject(i);
                String a = String.valueOf(betObj.getInteger("a"));
                String b = String.valueOf(betObj.getInteger("b"));

                // 检查是否是对子且与开奖号码中的对子匹配
                if (a.equals(b) && duiziNumbers.contains(a)) {
                    log.info("找到匹配的对子: a={}, b={}, 开奖对子={}", a, b, duiziNumbers);
                    hasWinning = true;
                    winningNumbers.add(betObj);
                }
            }

            if (hasWinning) {
                JSONObject winningObj = new JSONObject();
                winningObj.put("winning", winningNumbers);
                gameRecord.setWinningNumbers(winningObj.toJSONString());
            }

            return hasWinning;
        } catch (Exception e) {
            log.error("两码对子判断出错", e);
            return false;
        }
    }

    /**
     * 两码组三判断
     * 判断开奖号码是否为组三形态且同时包含投注的两个号码
     * 投注号码格式：{"numbers": [{"a": 第一个号码, "b": 第二个号码}, ...]}
     * 中奖条件：
     * 1. 开奖号码必须是组三形态（有且仅有一对相同数字）
     * 2. 开奖号码必须同时包含投注的两个号码
     */
    private boolean checkLiangMaZuSan(JSONArray numbersArray, String[] winningArray, Integer duizi, GameRecord gameRecord) {
        try {
            // 检查开奖号码是否为组三形态
            if (duizi == null || duizi != 1) {
                log.debug("开奖号码不是组三形态 - 投注记录ID: {}, duizi: {}", gameRecord.getBetId(), duizi);
                return false;
            }

            // 统计开奖号码中每个数字出现的次数
            Map<String, Integer> winningCount = new HashMap<>();
            for (String num : winningArray) {
                winningCount.put(num, winningCount.getOrDefault(num, 0) + 1);
            }

            // 存储中奖的组合
            JSONArray winningCombinations = new JSONArray();
            boolean hasWinning = false;

            // 遍历每组投注号码
            for (int i = 0; i < numbersArray.size(); i++) {
                JSONObject betObj = numbersArray.getJSONObject(i);
                // 修复：统一转换为字符串类型进行比较
                String betA = String.valueOf(betObj.get("a"));
                String betB = String.valueOf(betObj.get("b"));

                // 检查开奖号码是否同时包含投注的两个号码
                boolean containsA = winningCount.getOrDefault(betA, 0) > 0;
                boolean containsB = winningCount.getOrDefault(betB, 0) > 0;

                log.debug("两码组三判断 - 投注记录ID: {}, 投注号码: {}{}, 开奖号码: {}, containsA: {}, containsB: {}",
                        gameRecord.getBetId(), betA, betB, Arrays.toString(winningArray), containsA, containsB);

                if (containsA && containsB) {
                    hasWinning = true;
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", betA);
                    winningObj.put("b", betB);
                    winningCombinations.add(winningObj);

                    log.info("两码组三中奖 - 投注记录ID: {}, 投注号码: {}{}, 开奖号码: {}",
                            gameRecord.getBetId(), betA, betB, Arrays.toString(winningArray));
                }
            }

            if (hasWinning) {
                JSONObject winningObj = new JSONObject();
                winningObj.put("winning", winningCombinations);
                gameRecord.setWinningNumbers(winningObj.toJSONString());
                log.info("[两码组三中奖] betId={}, 中奖组数={}", gameRecord.getBetId(), winningCombinations.size());
            }

            return hasWinning;
        } catch (Exception e) {
            log.error("两码组三判断出错 - 投注记录ID: {}", gameRecord.getBetId(), e);
            return false;
        }
    }

    /**
     * 三码直选判断
     * 判断开奖号码是否与投注号码完全相同且位置相同
     * 投注号码格式：{"a": 百位, "b": 十位, "c": 个位}
     * 要求开奖号码与投注号码完全相同，且位置也相同
     */
    private boolean checkSanMaZhiXuan(JSONArray numbersArray, String[] winningArray) {
        try {
            // 遍历每组投注号码
            for (int i = 0; i < numbersArray.size(); i++) {
                JSONObject betObj = numbersArray.getJSONObject(i);
                String betA = betObj.getString("a");
                String betB = betObj.getString("b");
                String betC = betObj.getString("c");

                // 判断是否完全匹配
                if (betA.equals(winningArray[0]) &&
                        betB.equals(winningArray[1]) &&
                        betC.equals(winningArray[2])) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("三码直选判断出错", e);
            return false;
        }
    }

    /**
     * 三码组选判断
     * 判断开奖号码是否与投注号码完全相同（不考虑位置）
     * 投注号码格式：{"a": 号码1, "b": 号码2, "c": 号码3}
     * 只要开奖号码包含投注的三个号码即可，不考虑位置
     */
    private boolean checkSanMaZuXuan(JSONArray numbersArray, String[] winningArray) {
        for (int i = 0; i < numbersArray.size(); i++) {
            JSONObject betObj = numbersArray.getJSONObject(i);
            int betA = betObj.getInteger("a");
            int betB = betObj.getInteger("b");
            int betC = betObj.getInteger("c");
            int winA = Integer.parseInt(winningArray[0]);
            int winB = Integer.parseInt(winningArray[1]);
            int winC = Integer.parseInt(winningArray[2]);
            if ((winA == betA && winB == betB && winC == betC) ||
                    (winA == betA && winB == betC && winC == betB) ||
                    (winA == betB && winB == betA && winC == betC) ||
                    (winA == betB && winB == betC && winC == betA) ||
                    (winA == betC && winB == betA && winC == betB) ||
                    (winA == betC && winB == betB && winC == betA)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 三码组三判断
     * 判断开奖号码是否为组三形式，且投注号码与开奖号码三个数字完全一致（顺序不限，数量一致）
     * 投注号码格式：{"numbers": [{"a": 号码1, "b": 号码2, "c": 号码3}, ...]}
     */
    private boolean checkSanMaZuSan(JSONArray numbersArray, String[] winningArray, Integer duizi, GameRecord gameRecord,
            GameDraw draw) {
        try {
            if (numbersArray == null || numbersArray.isEmpty() || winningArray == null || winningArray.length != 3) {
                return false;
            }

            // 统计开奖号码中每个数字出现的次数
            Map<String, Integer> winningCounter = new HashMap<>();
            for (String number : winningArray) {
                winningCounter.put(number, winningCounter.getOrDefault(number, 0) + 1);
            }

            // 检查开奖号码是否为组三（一个对子一个单数）
            boolean isZuSan = false;
            for (Integer count : winningCounter.values()) {
                if (count == 2) {
                    isZuSan = true;
                } else if (count != 1) {
                    isZuSan = false;
                    break;
                }
            }
            if (!isZuSan) {
                return false;
            }
            // duizi==1 也要判断
            if (duizi == null || duizi != 1) {
                return false;
            }

            boolean hasWinning = false;
            JSONArray winningCombinations = new JSONArray();

            // 遍历每组投注号码
            for (int i = 0; i < numbersArray.size(); i++) {
                JSONObject betNumber = numbersArray.getJSONObject(i);
                Map<String, Integer> betCounter = new HashMap<>();
                for (String key : betNumber.keySet()) {
                    String v = betNumber.getString(key);
                    betCounter.put(v, betCounter.getOrDefault(v, 0) + 1);
                }
                if (betCounter.equals(winningCounter)) {
                    hasWinning = true;
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", betNumber.getString("a"));
                    winningObj.put("b", betNumber.getString("b"));
                    winningObj.put("c", betNumber.getString("c"));
                    winningCombinations.add(winningObj);
                }
            }

            if (hasWinning) {
                JSONObject winningNumbers = new JSONObject();
                winningNumbers.put("winning", winningCombinations);
                gameRecord.setWinningNumbers(winningNumbers.toJSONString());
            }
            return hasWinning;
        } catch (Exception e) {
            log.error("检查三码组三中奖时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 三码防对判断
     * 判断开奖号码为组三（有且只有两个数字相同，且不是豹子），且每组下注号码必须同时包含开奖号码的对子数字和单数数字才中奖
     */
    private boolean checkSanMaFangDui(JSONArray numbersArray, String[] winningArray, Integer duizi) {
        // 如果没有对子，直接返回不中奖
        if (duizi == null || duizi != 1) {
            return false;
        }
        // 统计开奖号码中每个数字出现的次数
        Map<Integer, Integer> countMap = new HashMap<>();
        for (String num : winningArray) {
            int n = Integer.parseInt(num);
            countMap.put(n, countMap.getOrDefault(n, 0) + 1);
        }
        // 找出对子数字和单数数字
        Integer pairNumber = null;
        Integer singleNumber = null;
        for (Map.Entry<Integer, Integer> entry : countMap.entrySet()) {
            if (entry.getValue() == 2) {
                pairNumber = entry.getKey();
            } else if (entry.getValue() == 1) {
                singleNumber = entry.getKey();
            }
        }
        if (pairNumber == null || singleNumber == null) {
            return false;
        }
        // 遍历每组投注号码，检查是否有任何一组中奖
        boolean hasWinning = false;
        for (int i = 0; i < numbersArray.size(); i++) {
            JSONObject betObj = numbersArray.getJSONObject(i);
            Set<Integer> betNumbers = new HashSet<>();
            betNumbers.add(betObj.getInteger("a"));
            betNumbers.add(betObj.getInteger("b"));
            betNumbers.add(betObj.getInteger("c"));
            if (betNumbers.contains(pairNumber) && betNumbers.contains(singleNumber)) {
                hasWinning = true;
                // 不要在这里return true，继续检查所有号码组合
            }
        }
        return hasWinning;
    }

    /**
     * 三码防对中奖号码收集
     * 返回所有中奖的号码组合
     */
    private JSONArray getSanMaFangDuiWinningNumbers(JSONArray numbersArray, String[] winningArray, Integer duizi) {
        JSONArray winningList = new JSONArray();

        // 如果没有对子，直接返回空列表
        if (duizi == null || duizi != 1) {
            return winningList;
        }

        // 统计开奖号码中每个数字出现的次数
        Map<Integer, Integer> countMap = new HashMap<>();
        for (String num : winningArray) {
            int n = Integer.parseInt(num);
            countMap.put(n, countMap.getOrDefault(n, 0) + 1);
        }

        // 找出对子数字和单数数字
        Integer pairNumber = null;
        Integer singleNumber = null;
        for (Map.Entry<Integer, Integer> entry : countMap.entrySet()) {
            if (entry.getValue() == 2) {
                pairNumber = entry.getKey();
            } else if (entry.getValue() == 1) {
                singleNumber = entry.getKey();
            }
        }

        if (pairNumber == null || singleNumber == null) {
            return winningList;
        }

        // 遍历每组投注号码，收集所有中奖的号码组合
        for (int i = 0; i < numbersArray.size(); i++) {
            JSONObject betNumber = numbersArray.getJSONObject(i);
            Set<Integer> betNums = new HashSet<>();
            betNums.add(betNumber.getInteger("a"));
            betNums.add(betNumber.getInteger("b"));
            betNums.add(betNumber.getInteger("c"));
            if (betNums.contains(pairNumber) && betNums.contains(singleNumber)) {
                winningList.add(betNumber);
            }
        }

        return winningList;
    }

    /**
     * 直选复式判断
     * 判断开奖号码是否与投注号码完全相同且位置相同
     * 投注号码格式：["百位号码", "十位号码", "个位号码"]
     * 要求开奖号码与投注号码完全相同，且位置也相同
     */
    private boolean checkZhiXuanFuShi(JSONArray numbersArray, String[] winningArray) {
        String betNumber1 = numbersArray.getString(0);
        String betNumber2 = numbersArray.getString(1);
        String betNumber3 = numbersArray.getString(2);
        return winningArray[0].equals(betNumber1) &&
                winningArray[1].equals(betNumber2) &&
                winningArray[2].equals(betNumber3);
    }

    /**
     * 四码组六判断
     * 判断开奖号码中是否包含投注号码中的至少三个数字
     * 投注号码格式：{"numbers": [{"a": 号码1, "b": 号码2, "c": 号码3, "d": 号码4}, ...]}
     */
    private boolean checkSiMaZuLiu(JSONArray numbersArray, String[] winningArray, GameRecord gameRecord,
            GameDraw draw) {
        try {
            // 检查开奖号码是否为组六（三个不同的数字）
            Set<String> winningSet = new HashSet<>();
            for (String number : winningArray) {
                winningSet.add(number);
            }
            // 如果开奖号码有重复，则不是组六
            if (winningSet.size() != 3) {
                log.info("开奖号码不是组六: {}", Arrays.toString(winningArray));
                return false;
            }

            boolean hasWinning = false;
            JSONArray winningCombinations = new JSONArray();

            // 遍历每组投注号码
            for (int i = 0; i < numbersArray.size(); i++) {
                JSONObject betNumber = numbersArray.getJSONObject(i);
                String a = betNumber.getString("a");
                String b = betNumber.getString("b");
                String c = betNumber.getString("c");
                String d = betNumber.getString("d");

                // 获取投注号码集合
                Set<String> betSet = new HashSet<>();
                betSet.add(a);
                betSet.add(b);
                betSet.add(c);
                betSet.add(d);

                // 检查开奖号码是否都在投注号码中
                boolean isMatch = true;
                for (String number : winningSet) {
                    if (!betSet.contains(number)) {
                        isMatch = false;
                        break;
                    }
                }

                if (isMatch) {
                    hasWinning = true;
                    // 创建中奖组合，使用开奖号码
                    JSONObject winningCombination = new JSONObject();
                    winningCombination.put("a", winningArray[0]);
                    winningCombination.put("b", winningArray[1]);
                    winningCombination.put("c", winningArray[2]);
                    winningCombinations.add(winningCombination);
                }
            }

            if (hasWinning) {
                // 设置中奖号码
                JSONObject winningNumbers = new JSONObject();
                winningNumbers.put("winning", winningCombinations);
                gameRecord.setWinningNumbers(winningNumbers.toJSONString());
                log.info("四码组六中奖 - 投注号码: {}, 中奖号码: {}", gameRecord.getBetNumbers(), gameRecord.getWinningNumbers());
            }

            return hasWinning;
        } catch (Exception e) {
            log.error("检查四码组六中奖时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 撤销开奖
     * 
     * @param qihao 期号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelDraw(String qihao) {
        Long currentUserId = GameUserUtils.getCurrentSysUserId();

        // 尝试获取撤销锁，超时时间30秒
        if (!settlementLockManager.acquireCancelLock(currentUserId, 30)) {
            throw new ServiceException("系统繁忙，请稍后再试。当前有太多用户正在进行撤销操作。");
        }

        try {
            log.info("用户 {} 开始撤销期号 {} 的开奖，{}", currentUserId, qihao, settlementLockManager.getSystemLoadInfo());

            // 1. 获取指定期号的开奖记录
            GameDraw draw = gameDrawMapper.selectByQihao(qihao);
            if (draw == null) {
                String errorMsg = "未找到指定期号的开奖记录: " + qihao;
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }

            // 验证数据权限
            if (!DataPermissionUtils.isAdmin()) {
                DataPermissionUtils.validateDataOwnership(draw.getSysUserId(), "无权撤销该开奖记录");
            }

            // 2. 获取该期号该用户的投注记录（每个用户只处理自己的记录，包括管理员）
            Map<String, Object> params = new HashMap<>();
            params.put("issueNumber", qihao);
            params.put("sysUserId", currentUserId);
            List<GameRecord> records = gameRecordMapper.selectByIssueNumberAndUser(params);
            log.info("期号 {} 用户 {} 共有 {} 条投注记录", qihao, currentUserId, records.size());

            // 3. 清除投注记录的中奖信息
            for (GameRecord record : records) {
                try {
                    // 先清空中奖号码
                    int clearResult = gameRecordMapper.clearWinningNumbers(record.getBetId());
                    log.info("清空中奖号码结果: {}, betId: {}", clearResult, record.getBetId());

                    // 更新其他字段
                    Map<String, Object> updateParams = new HashMap<>();
                    updateParams.put("betId", record.getBetId());
                    updateParams.put("shifouzj", 0L);
                    updateParams.put("jiesuan", 0L);
                    updateParams.put("winAmount", BigDecimal.ZERO);

                    int updateResult = gameRecordMapper.updateGameRecordByBetId(updateParams);
                    log.info("更新其他字段结果: {}, betId: {}", updateResult, record.getBetId());

                    if (clearResult <= 0 || updateResult <= 0) {
                        throw new RuntimeException("更新投注记录失败，betId: " + record.getBetId());
                    }
                } catch (Exception e) {
                    log.error("处理投注记录 {} 时发生错误: {}", record.getBetId(), e.getMessage(), e);
                    throw e;
                }
            }

            // 4. 删除中奖记录
            // 4.1 先查询该期号的所有中奖记录
            GameWinning queryWinning = new GameWinning();
            queryWinning.setIssueNumber(qihao);
            List<GameWinning> winningRecords = gameWinningMapper.selectGameWinningList(queryWinning);
            log.info("期号 {} 共有 {} 条中奖记录", qihao, winningRecords.size());

            // 4.2 收集受影响的用户ID
            Set<Long> affectedUserIds = new HashSet<>();
            for (GameWinning winning : winningRecords) {
                affectedUserIds.add(winning.getUserId());
            }

            // 4.3 删除所有中奖记录
            for (GameWinning winning : winningRecords) {
                int deleteResult = gameWinningMapper.deleteGameWinningByWinId(winning.getWinId());
                if (deleteResult <= 0) {
                    throw new RuntimeException("删除中奖记录失败，winId: " + winning.getWinId());
                }
                log.info("已删除中奖记录ID: {}", winning.getWinId());
            }

            // 4.4 获取当前用户的所有玩家ID（确保同步所有玩家）
            List<GameCustomer> customers = gameCustomerMapper.selectGameCustomerList(new GameCustomer() {{
                setSysUserId(currentUserId);
            }});
            for (GameCustomer customer : customers) {
                affectedUserIds.add(customer.getUserId());
            }

            // 4.5 撤销后需要重新计算中奖金额（使用带sys_user_id隔离的绝对值更新）
            // 注意：撤销操作需要重新计算总金额，所以这里使用绝对值更新是合理的
            for (Long userId : affectedUserIds) {
                // 重新计算该用户在当前系统用户下的总中奖金额
                BigDecimal totalWin = gameWinningMapper.sumWinAmountByUserIdAndSysUser(userId, currentUserId);
                gameCustomerMapper.updateTotalWinAmountAbsolute(userId, currentUserId, totalWin);
                log.info("[撤销同步] 玩家ID:{} (系统用户:{}) 总中奖金额已重新计算为:{}", userId, currentUserId, totalWin);

                // 重新计算福彩中奖金额
                BigDecimal fcZj = gameWinningMapper.sumWinAmountByUserIdAndLotteryIdAndSysUser(userId, 1L, currentUserId);
                gameCustomerMapper.updateFcZj(userId, currentUserId, fcZj);
                log.info("[撤销同步] 玩家ID:{} (系统用户:{}) 福彩中奖金额已重新计算为:{}", userId, currentUserId, fcZj);

                // 重新计算体彩中奖金额
                BigDecimal tcZj = gameWinningMapper.sumWinAmountByUserIdAndLotteryIdAndSysUser(userId, 2L, currentUserId);
                gameCustomerMapper.updateTcZj(userId, currentUserId, tcZj);
                log.info("[撤销同步] 玩家ID:{} (系统用户:{}) 体彩中奖金额已重新计算为:{}", userId, currentUserId, tcZj);
            }

            // 5. 重置开奖记录状态
            draw.setJiesuan(0); // 未结算
            draw.setZongjiesuan(BigDecimal.ZERO); // 重置总中奖金额
            draw.setYingli(BigDecimal.ZERO); // 重置盈利值

            int updateResult = gameDrawMapper.updateGameDraw(draw);
            if (updateResult <= 0) {
                throw new RuntimeException("更新开奖记录失败，期号：" + qihao);
            }

            // 重置流水记录的中奖金额（每个用户只重置自己的流水记录，包括管理员）
            gameSerialMapper.resetSerialWinMoneyByUser(currentUserId);
            log.info("用户 {} 撤销期号 {} 开奖时已重置自己的流水中奖金额", currentUserId, qihao);

            log.info("期号 {} 的开奖撤销完成，用户 {} 相关记录已重置", qihao, currentUserId);

        } catch (Exception e) {
            log.error("用户 {} 撤销期号 {} 的开奖时发生错误: {}", currentUserId, qihao, e.getMessage(), e);
            throw new RuntimeException("撤销开奖时发生错误: " + e.getMessage(), e);
        } finally {
            // 释放撤销锁
            settlementLockManager.releaseCancelLock(currentUserId);
            log.info("用户 {} 撤销锁已释放，{}", currentUserId, settlementLockManager.getSystemLoadInfo());
        }
    }

    /**
     * 撤销结算
     * 
     * @param drawId 开奖ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelPay(Long drawId) {
        Long currentUserId = GameUserUtils.getCurrentSysUserId();

        // 尝试获取撤销锁，超时时间30秒
        if (!settlementLockManager.acquireCancelLock(currentUserId, 30)) {
            throw new ServiceException("系统繁忙，请稍后再试。当前有太多用户正在进行撤销操作。");
        }

        try {
            log.info("用户 {} 开始撤销开奖ID {} 的结算，{}", currentUserId, drawId, settlementLockManager.getSystemLoadInfo());

            // 1. 获取开奖记录
            GameDraw gameDraw = gameDrawMapper.selectGameDrawByDrawId(drawId);
            if (gameDraw == null) {
                throw new RuntimeException("未找到开奖记录，drawId: " + drawId);
            }

            // 验证数据权限
            if (!DataPermissionUtils.isAdmin()) {
                DataPermissionUtils.validateDataOwnership(gameDraw.getSysUserId(), "无权撤销该开奖记录的结算");
            }

            // 2. 获取该期号该用户的中奖记录（每个用户只处理自己的记录，包括管理员）
            Map<String, Object> winningParams = new HashMap<>();
            winningParams.put("issueNumber", gameDraw.getQihao());
            winningParams.put("sysUserId", currentUserId);
            List<GameWinning> winningRecords = gameWinningMapper.selectByIssueNumberAndUser(winningParams);
            log.info("期号 {} 用户 {} 共有 {} 条中奖记录", gameDraw.getQihao(), currentUserId, winningRecords.size());

            // 3. 获取该期号该用户的投注记录（每个用户只处理自己的记录，包括管理员）
            Map<String, Object> recordParams = new HashMap<>();
            recordParams.put("issueNumber", gameDraw.getQihao());
            recordParams.put("sysUserId", currentUserId);
            List<GameRecord> records = gameRecordMapper.selectByIssueNumberAndUser(recordParams);
            log.info("期号 {} 用户 {} 共有 {} 条投注记录", gameDraw.getQihao(), currentUserId, records.size());

            // 4. 处理投注记录
            for (GameRecord record : records) {
                // 重置中奖状态
                Map<String, Object> params = new HashMap<>();
                params.put("betId", record.getBetId());
                params.put("shifouzj", 0L); // 未中奖
                params.put("jiesuan", 0L); // 未结算
                params.put("winAmount", BigDecimal.ZERO); // 重置中奖金额
                params.put("winningNumbers", null); // 清空中奖号码

                int updateResult = gameRecordMapper.updateGameRecordByBetId(params);
                if (updateResult <= 0) {
                    throw new RuntimeException("更新投注记录失败，betId: " + record.getBetId());
                }
                log.info("已重置投注记录ID: {}", record.getBetId());
            }

            // 5. 收集受影响的用户ID
            Set<Long> affectedUserIds = new HashSet<>();
            Map<Long, BigDecimal> userWinAmounts = new HashMap<>();
            for (GameWinning winning : winningRecords) {
                // 累加用户中奖金额（用于后续扣除）
                userWinAmounts.merge(winning.getUserId(), winning.getWinAmount(), BigDecimal::add);
                affectedUserIds.add(winning.getUserId());
            }

            // 6. 删除所有中奖记录
            for (GameWinning winning : winningRecords) {
                int deleteResult = gameWinningMapper.deleteGameWinningByWinId(winning.getWinId());
                if (deleteResult <= 0) {
                    throw new RuntimeException("删除中奖记录失败，winId: " + winning.getWinId());
                }
                log.info("已删除中奖记录ID: {}", winning.getWinId());
            }

            // 6.1 获取当前用户的所有玩家ID（确保同步所有玩家）
            List<GameCustomer> customers = gameCustomerMapper.selectGameCustomerList(new GameCustomer() {{
                setSysUserId(currentUserId);
            }});
            for (GameCustomer customer : customers) {
                affectedUserIds.add(customer.getUserId());
            }

            // 6.2 撤销后全量同步中奖金额和福彩体彩分类金额（按用户隔离）
            for (Long userId : affectedUserIds) {
                // 使用带用户隔离的方法同步总中奖金额
                BigDecimal totalWin = gameWinningMapper.sumWinAmountByUserIdAndSysUser(userId, currentUserId);
                gameCustomerMapper.updateTotalWinAmountAbsolute(userId, currentUserId, totalWin);
                log.info("[撤销同步] 玩家ID:{} (系统用户:{}) 总中奖金额已同步为:{}", userId, currentUserId, totalWin);

                // 使用带用户隔离的方法同步福彩中奖金额
                BigDecimal fcZj = gameWinningMapper.sumWinAmountByUserIdAndLotteryIdAndSysUser(userId, 1L, currentUserId);
                gameCustomerMapper.updateFcZj(userId, currentUserId, fcZj);
                log.info("[撤销同步] 玩家ID:{} (系统用户:{}) 福彩中奖金额已同步为:{}", userId, currentUserId, fcZj);

                // 使用带用户隔离的方法同步体彩中奖金额
                BigDecimal tcZj = gameWinningMapper.sumWinAmountByUserIdAndLotteryIdAndSysUser(userId, 2L, currentUserId);
                gameCustomerMapper.updateTcZj(userId, currentUserId, tcZj);
                log.info("[撤销同步] 玩家ID:{} (系统用户:{}) 体彩中奖金额已同步为:{}", userId, currentUserId, tcZj);
            }

            // 7. 重置开奖记录状态
            gameDraw.setJiesuan(0); // 未结算
            gameDraw.setZongjiesuan(BigDecimal.ZERO); // 重置总中奖金额
            gameDraw.setYingli(BigDecimal.ZERO); // 重置盈利值

            int updateResult = gameDrawMapper.updateGameDraw(gameDraw);
            if (updateResult <= 0) {
                throw new RuntimeException("更新开奖记录失败，期号：" + gameDraw.getQihao());
            }

            // 重置流水记录的中奖金额（每个用户只重置自己的流水记录，包括管理员）
            gameSerialMapper.resetSerialWinMoneyByUser(currentUserId);
            log.info("用户 {} 撤销开奖ID {} 时已重置自己的流水中奖金额", currentUserId, drawId);

            log.info("开奖ID {} 的结算撤销完成，用户 {} 相关记录已重置", drawId, currentUserId);
            return updateResult;

        } catch (Exception e) {
            log.error("用户 {} 撤销开奖ID {} 的结算时发生错误: {}", currentUserId, drawId, e.getMessage(), e);
            throw new ServiceException("撤销结算时发生错误: " + e.getMessage());
        } finally {
            // 释放撤销锁
            settlementLockManager.releaseCancelLock(currentUserId);
            log.info("用户 {} 撤销锁已释放，{}", currentUserId, settlementLockManager.getSystemLoadInfo());
        }
    }

    /**
     * 批量撤销结算
     * 
     * @param drawIds 开奖ID数组
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cancelPay(Long[] drawIds) {
        int count = 0;
        for (Long drawId : drawIds) {
            try {
                count += cancelPay(drawId);
            } catch (Exception e) {
                log.error("撤销开奖ID {} 的结算时发生错误: {}", drawId, e.getMessage(), e);
                throw new ServiceException("撤销结算时发生错误: " + e.getMessage());
            }
        }
        return count;
    }

    /**
     * 在服务关闭时关闭线程池
     */
    @PreDestroy
    public void shutdown() {
        try {
            log.info("正在关闭开奖结算线程池...");
            executorService.shutdown();
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
            log.info("开奖结算线程池已关闭");
        } catch (InterruptedException e) {
            log.error("关闭开奖结算线程池时发生错误: {}", e.getMessage(), e);
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 带锁的draw数据统计检查和修复
     *
     * @param currentUserId 当前用户ID
     * @return true-成功，false-获取锁失败
     */
    private boolean performDataConsistencyCheckWithLock(Long currentUserId) {
        // 尝试获取draw数据统计锁
        if (!dataStatisticsLockManager.acquireStatisticsLock(currentUserId)) {
            log.warn("[draw数据统计] 用户{}获取draw数据统计锁失败，{}", currentUserId, dataStatisticsLockManager.getLockStatus());
            return false;
        }

        try {
            log.info("=== 开始开奖前draw数据一致性检查和修复 ===");
            performDataConsistencyCheckAndRepair(currentUserId);
            log.info("=== 开奖前draw数据一致性检查和修复完成 ===");
            return true;
        } finally {
            // 确保释放锁
            dataStatisticsLockManager.releaseStatisticsLock(currentUserId);
        }
    }

    /**
     * 带锁的customer数据统计检查和修复
     *
     * @param currentUserId 当前用户ID
     * @return true-成功，false-获取锁失败
     */
    private boolean performCustomerDataConsistencyCheckWithLock(Long currentUserId) {
        // 尝试获取customer数据统计锁
        if (!dataStatisticsLockManager.acquireCustomerStatisticsLock(currentUserId)) {
            log.warn("[customer数据统计] 用户{}获取customer数据统计锁失败，{}", currentUserId, dataStatisticsLockManager.getLockStatus());
            return false;
        }

        try {
            log.info("=== 开始开奖前customer数据一致性检查和修复 ===");
            performCustomerDataConsistencyCheckAndRepair(currentUserId);
            log.info("=== 开奖前customer数据一致性检查和修复完成 ===");
            return true;
        } finally {
            // 确保释放锁
            dataStatisticsLockManager.releaseCustomerStatisticsLock(currentUserId);
        }
    }

    /**
     * 在事务中处理开奖结算
     */
    @Transactional(rollbackFor = Exception.class)
    private void processDrawSettlementInTransaction(Long currentUserId) {
        // 获取当前用户的未结算开奖记录（每个用户只处理自己的记录，包括管理员）
        List<GameDraw> unsettledDraws = gameDrawMapper.selectUnsettledDrawsByUser(currentUserId);

        if (unsettledDraws == null || unsettledDraws.isEmpty()) {
            log.info("用户 {} 没有未结算的开奖记录", currentUserId);
            return;
        }

        log.info("用户 {} 找到 {} 条未结算开奖记录", currentUserId, unsettledDraws.size());

        // 获取管理员权限标识
        boolean isAdmin = DataPermissionUtils.isAdmin();

        // 【新增】收集所有中奖记录的容器
        List<GameWinning> allWinningRecords = new ArrayList<>();

        // 同步处理开奖记录（在事务中不使用并行处理）
        for (GameDraw draw : unsettledDraws) {
            try {
                // 【修改】传入中奖记录收集器，使用批量处理版本
                processSingleDrawWithBatch(draw, currentUserId, isAdmin, allWinningRecords);
            } catch (Exception e) {
                log.error("处理开奖记录失败: {}", draw.getQihao(), e);
                throw new RuntimeException("处理开奖记录失败", e);
            }
        }

        // 【新增】批量插入所有中奖记录
        if (!allWinningRecords.isEmpty()) {
            log.info("开始批量插入 {} 条中奖记录", allWinningRecords.size());
            try {
                int batchInsertResult = gameWinningMapper.batchInsertGameWinnings(allWinningRecords);
                log.info("批量插入中奖记录完成，成功插入 {} 条记录", batchInsertResult);

                // 【新增】批量插入完成后，执行数据同步
                performBatchDataSynchronization(currentUserId, allWinningRecords);

            } catch (Exception e) {
                log.error("批量插入中奖记录失败", e);
                throw new RuntimeException("批量插入中奖记录失败", e);
            }
        }

        log.info("用户 {} 所有开奖记录处理完成，共处理 {} 条记录，插入 {} 条中奖记录",
                currentUserId, unsettledDraws.size(), allWinningRecords.size());
    }

    /**
     * 开奖前draw数据统计和修复
     * 统计record表中该用户各彩种的总下注金额，并更新到对应的draw表中
     * 使用READ_COMMITTED隔离级别，优先保证其他用户操作record表的安全性
     * 由于开奖结算开始后该用户不再操作record表，数据一致性仍有保障
     */
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    private void performDataConsistencyCheckAndRepair(Long currentUserId) {
        try {
            log.info("[开奖前draw数据统计] 开始统计用户{}的各彩种投注额", currentUserId);

            // 1. 使用SQL直接统计该用户在record表中各彩种的总投注额
            List<Map<String, Object>> lotteryStats = gameRecordMapper.selectUserLotteryTotalAmount(currentUserId);

            if (lotteryStats == null || lotteryStats.isEmpty()) {
                log.info("[开奖前draw数据统计] 用户{}没有投注记录", currentUserId);
                return;
            }

            log.info("[开奖前draw数据统计] 用户{}各彩种投注额统计结果: {}", currentUserId, lotteryStats);

            int totalUpdated = 0;

            // 2. 遍历统计结果，更新对应的draw表记录
            for (Map<String, Object> stat : lotteryStats) {
                // 修复类型转换问题：数据库返回的可能是Integer，需要转换为Long
                Object lotteryIdObj = stat.get("lotteryId");
                Long lotteryId = null;
                if (lotteryIdObj instanceof Integer) {
                    lotteryId = ((Integer) lotteryIdObj).longValue();
                } else if (lotteryIdObj instanceof Long) {
                    lotteryId = (Long) lotteryIdObj;
                }

                BigDecimal totalAmount = (BigDecimal) stat.get("totalAmount");

                if (lotteryId != null && totalAmount != null) {
                    log.info("[开奖前draw数据统计] 准备更新彩种{}, 总投注额: {}", lotteryId, totalAmount);

                    // 3. 更新该用户该彩种的所有draw表记录
                    int updateResult = gameDrawMapper.updateZongtouzhuByLottery(lotteryId, totalAmount, currentUserId);

                    if (updateResult > 0) {
                        totalUpdated += updateResult;
                        log.info("[draw数据更新成功] 用户{}, 彩种{}, 总投注额更新为{}, 影响{}条draw记录",
                            currentUserId, lotteryId, totalAmount, updateResult);
                    } else {
                        log.warn("[draw数据更新失败] 用户{}, 彩种{}, 没有找到对应的draw记录",
                            currentUserId, lotteryId);
                    }
                } else {
                    log.warn("[开奖前draw数据统计] 跳过无效数据: lotteryId={}, totalAmount={}", lotteryIdObj, totalAmount);
                }
            }

            log.info("[开奖前draw数据统计完成] 用户{}, 共更新{}条draw记录", currentUserId, totalUpdated);

        } catch (Exception e) {
            log.error("[开奖前draw数据统计] 用户{}统计失败: {}", currentUserId, e.getMessage(), e);
            throw new RuntimeException("开奖前draw数据统计失败", e);
        }
    }

    /**
     * 开奖前customer数据统计和修复
     * 统计record表中该用户名下各玩家各彩种的总下注金额，并更新到对应的game_customer表中
     * 使用READ_COMMITTED隔离级别，优先保证其他用户操作record表的安全性
     * 由于开奖结算开始后该用户不再操作record表，数据一致性仍有保障
     */
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    private void performCustomerDataConsistencyCheckAndRepair(Long currentUserId) {
        try {
            log.info("[开奖前customer数据统计] 开始统计用户{}名下各玩家各彩种投注额", currentUserId);

            // 1. 使用SQL直接统计该用户名下各玩家各彩种的总投注额
            List<Map<String, Object>> customerLotteryStats = gameRecordMapper.selectUserCustomerLotteryTotalAmount(currentUserId);

            if (customerLotteryStats == null || customerLotteryStats.isEmpty()) {
                log.info("[开奖前customer数据统计] 用户{}名下没有投注记录", currentUserId);
                return;
            }

            log.info("[开奖前customer数据统计] 用户{}名下各玩家各彩种投注额统计结果: {}", currentUserId, customerLotteryStats);

            // 2. 按玩家分组统计
            Map<Long, Map<Long, BigDecimal>> customerStatsMap = new HashMap<>();

            for (Map<String, Object> stat : customerLotteryStats) {
                // 修复类型转换问题
                Object userIdObj = stat.get("userId");
                Object lotteryIdObj = stat.get("lotteryId");
                BigDecimal totalAmount = (BigDecimal) stat.get("totalAmount");

                Long userId = null;
                Long lotteryId = null;

                if (userIdObj instanceof Integer) {
                    userId = ((Integer) userIdObj).longValue();
                } else if (userIdObj instanceof Long) {
                    userId = (Long) userIdObj;
                }

                if (lotteryIdObj instanceof Integer) {
                    lotteryId = ((Integer) lotteryIdObj).longValue();
                } else if (lotteryIdObj instanceof Long) {
                    lotteryId = (Long) lotteryIdObj;
                }

                if (userId != null && lotteryId != null && totalAmount != null) {
                    customerStatsMap.computeIfAbsent(userId, k -> new HashMap<>()).put(lotteryId, totalAmount);
                }
            }

            int totalUpdated = 0;

            // 3. 遍历每个玩家，更新其投注额统计
            for (Map.Entry<Long, Map<Long, BigDecimal>> customerEntry : customerStatsMap.entrySet()) {
                Long userId = customerEntry.getKey();
                Map<Long, BigDecimal> lotteryAmounts = customerEntry.getValue();

                BigDecimal fcTouzhu = lotteryAmounts.getOrDefault(1L, BigDecimal.ZERO);  // 福彩投注额
                BigDecimal tcTouzhu = lotteryAmounts.getOrDefault(2L, BigDecimal.ZERO);  // 体彩投注额
                BigDecimal totalBetAmount = fcTouzhu.add(tcTouzhu);  // 总投注额

                log.info("[开奖前customer数据统计] 准备更新玩家{}, 福彩投注额: {}, 体彩投注额: {}, 总投注额: {}",
                    userId, fcTouzhu, tcTouzhu, totalBetAmount);

                // 4. 更新该玩家的投注额统计
                int updateResult = gameCustomerMapper.updateCustomerBetAmounts(userId, currentUserId, fcTouzhu, tcTouzhu, totalBetAmount);

                if (updateResult > 0) {
                    totalUpdated += updateResult;
                    log.info("[customer数据更新成功] 用户{}, 玩家{}, 福彩投注额更新为{}, 体彩投注额更新为{}, 总投注额更新为{}, 影响{}条customer记录",
                        currentUserId, userId, fcTouzhu, tcTouzhu, totalBetAmount, updateResult);
                } else {
                    log.warn("[customer数据更新失败] 用户{}, 玩家{}, 没有找到对应的customer记录",
                        currentUserId, userId);
                }
            }

            log.info("[开奖前customer数据统计完成] 用户{}, 共更新{}条customer记录", currentUserId, totalUpdated);

        } catch (Exception e) {
            log.error("[开奖前customer数据统计] 用户{}统计失败: {}", currentUserId, e.getMessage(), e);
            throw new RuntimeException("开奖前customer数据统计失败", e);
        }
    }

    private void processWinningNumbers(GameRecord record, String[] numbers, Long methodId, GameDraw draw) {
        try {
            JSONArray numbersArray = null;
            if (record.getBetNumbers() != null) {
                numbersArray = JSON.parseObject(record.getBetNumbers()).getJSONArray("numbers");
            }
            JSONArray winningList = new JSONArray();

            if (methodId == 1) { // 独胆
                if (numbersArray != null) {
                    for (int i = 0; i < numbersArray.size(); i++) {
                        JSONObject betNumber = numbersArray.getJSONObject(i);
                        String betA = betNumber.getString("a");
                        if (numbers[0].equals(betA) || numbers[1].equals(betA) || numbers[2].equals(betA)) {
                            JSONObject winningObj = new JSONObject();
                            winningObj.put("a", betA);
                            winningList.add(winningObj);
                        }
                    }
                }
            } else if (methodId == 2) { // 一码定位
                if (numbersArray != null && record.getDingwei() != null) {
                    int position = record.getDingwei().intValue();
                    if (position >= 1 && position <= 3) {
                        for (int i = 0; i < numbersArray.size(); i++) {
                            JSONObject betNumber = numbersArray.getJSONObject(i);
                            String betA = betNumber.getString("a");
                            if (numbers[position - 1].equals(betA)) {
                                JSONObject winningObj = new JSONObject();
                                winningObj.put("a", betA);
                                winningObj.put("position", position);
                                winningList.add(winningObj);
                            }
                        }
                    }
                }
            } else if (methodId == 3) { // 两码组合
                if (numbersArray != null) {
                    Set<String> winningSet = new HashSet<>(Arrays.asList(numbers));
                    for (int i = 0; i < numbersArray.size(); i++) {
                        JSONObject betNumber = numbersArray.getJSONObject(i);
                        String betA = betNumber.getString("a");
                        String betB = betNumber.getString("b");
                        if (winningSet.contains(betA) && winningSet.contains(betB)) {
                            JSONObject winningObj = new JSONObject();
                            winningObj.put("a", betA);
                            winningObj.put("b", betB);
                            winningList.add(winningObj);
                        }
                    }
                }
            } else if (methodId == 4) { // 两码对子
                if (numbersArray != null) {
                    // 统计开奖号码中每个数字出现的次数
                    Map<String, Integer> numberCount = new HashMap<>();
                    for (String num : numbers) {
                        numberCount.put(num, numberCount.getOrDefault(num, 0) + 1);
                    }
                    for (int i = 0; i < numbersArray.size(); i++) {
                        JSONObject betNumber = numbersArray.getJSONObject(i);
                        String a = betNumber.getString("a");
                        String b = betNumber.getString("b");
                        // 必须a==b，且开奖号码中a出现2次及以上
                        if (a.equals(b) && numberCount.getOrDefault(a, 0) >= 2) {
                            JSONObject winningObj = new JSONObject();
                            winningObj.put("a", a);
                            winningObj.put("b", b);
                            winningList.add(winningObj);
                        }
                    }
                }
            } else if (methodId == 5) { // 三码直选
                if (numbersArray != null) {
                    for (int i = 0; i < numbersArray.size(); i++) {
                        JSONObject betNumber = numbersArray.getJSONObject(i);
                        String betA = betNumber.getString("a");
                        String betB = betNumber.getString("b");
                        String betC = betNumber.getString("c");
                        if (numbers[0].equals(betA) && numbers[1].equals(betB) && numbers[2].equals(betC)) {
                            JSONObject winningObj = new JSONObject();
                            winningObj.put("a", betA);
                            winningObj.put("b", betB);
                            winningObj.put("c", betC);
                            winningList.add(winningObj);
                        }
                    }
                }
            } else if (methodId == 6) { // 三码组选
                if (numbersArray != null) {
                    Set<String> winningSet = new HashSet<>(Arrays.asList(numbers));
                    for (int i = 0; i < numbersArray.size(); i++) {
                        JSONObject betNumber = numbersArray.getJSONObject(i);
                        String betA = betNumber.getString("a");
                        String betB = betNumber.getString("b");
                        String betC = betNumber.getString("c");
                        if (winningSet.contains(betA) && winningSet.contains(betB) && winningSet.contains(betC)) {
                            JSONObject winningObj = new JSONObject();
                            winningObj.put("a", betA);
                            winningObj.put("b", betB);
                            winningObj.put("c", betC);
                            winningList.add(winningObj);
                        }
                    }
                }
            } else if (methodId == 7) { // 三码组三
                if (checkSanMaZuSan(numbersArray, numbers, draw.getDuizi(), record, draw)) {
                    for (int i = 0; i < numbersArray.size(); i++) {
                        JSONObject betNumber = numbersArray.getJSONObject(i);
                        String betA = betNumber.getString("a");
                        String betB = betNumber.getString("b");
                        String betC = betNumber.getString("c");
                        if (Arrays.asList(numbers).contains(betA) &&
                                Arrays.asList(numbers).contains(betB) &&
                                Arrays.asList(numbers).contains(betC)) {
                            JSONObject winningObj = new JSONObject();
                            winningObj.put("a", betA);
                            winningObj.put("b", betB);
                            winningObj.put("c", betC);
                            winningList.add(winningObj);
                        }
                    }
                }
            } else if (methodId == 8) { // 四码组六
                if (checkSiMaZuLiu(numbersArray, numbers, record, draw)) {
                    for (int i = 0; i < numbersArray.size(); i++) {
                        JSONObject betNumber = numbersArray.getJSONObject(i);
                        String betA = betNumber.getString("a");
                        String betB = betNumber.getString("b");
                        String betC = betNumber.getString("c");
                        String betD = betNumber.getString("d");
                        if (Arrays.asList(numbers).contains(betA) &&
                                Arrays.asList(numbers).contains(betB) &&
                                Arrays.asList(numbers).contains(betC) &&
                                Arrays.asList(numbers).contains(betD)) {
                            JSONObject winningObj = new JSONObject();
                            winningObj.put("a", betA);
                            winningObj.put("b", betB);
                            winningObj.put("c", betC);
                            winningObj.put("d", betD);
                            winningList.add(winningObj);
                        }
                    }
                }
            } else if (methodId >= 9 && methodId <= 19) { // 其他多码组三和组六
                if (methodId % 2 == 1) { // 组三
                    if (checkMultiMaZuSan(numbersArray, numbers, draw.getDuizi(), record, draw, methodId)) {
                        for (int i = 0; i < numbersArray.size(); i++) {
                            JSONObject betNumber = numbersArray.getJSONObject(i);
                            JSONObject winningObj = new JSONObject();
                            for (String key : betNumber.keySet()) {
                                String value = betNumber.getString(key);
                                if (Arrays.asList(numbers).contains(value)) {
                                    winningObj.put(key, value);
                                }
                            }
                            if (winningObj.size() > 0) {
                                winningList.add(winningObj);
                            }
                        }
                    }
                } else { // 组六
                    if (checkMultiMaZuLiu(numbersArray, numbers, record, draw, methodId)) {
                        for (int i = 0; i < numbersArray.size(); i++) {
                            JSONObject betNumber = numbersArray.getJSONObject(i);
                            JSONObject winningObj = new JSONObject();
                            for (String key : betNumber.keySet()) {
                                String value = betNumber.getString(key);
                                if (Arrays.asList(numbers).contains(value)) {
                                    winningObj.put(key, value);
                                }
                            }
                            if (winningObj.size() > 0) {
                                winningList.add(winningObj);
                            }
                        }
                    }
                }
            } else if (methodId == 20) { // 包打组六
                if (checkBaoDaZuLiu(numbers, record)) {
                    // 已在 checkBaoDaZuLiu 内部设置中奖号码，这里无需再设置 winningList
                }
            } else if (methodId == 31) { // 包打组三
                if (checkBaoDaZuSan(numbers, record.getDuizi(), record, draw)) {
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", "组三");
                    winningList.add(winningObj);
                }
            } else if (methodId == 33) { // 杀码
                if (numbersArray != null) {
                    for (int i = 0; i < numbersArray.size(); i++) {
                        JSONObject betNumber = numbersArray.getJSONObject(i);
                        String shaMa = betNumber.getString("a");
                        boolean containsShaMa = false;
                        for (String num : numbers) {
                            if (num.equals(shaMa)) {
                                containsShaMa = true;
                                break;
                            }
                        }
                        if (!containsShaMa) {
                            JSONObject winningObj = new JSONObject();
                            winningObj.put("a", "杀" + shaMa);
                            winningList.add(winningObj);
                        }
                    }
                }
            }else if (methodId == 100) { // 三码防对
                // 先判断是否中奖
                if (checkSanMaFangDui(numbersArray, numbers, draw.getDuizi())) {
                    // 使用新的方法获取所有中奖号码
                    JSONArray fangduiWinningList = getSanMaFangDuiWinningNumbers(numbersArray, numbers, draw.getDuizi());
                    winningList.addAll(fangduiWinningList);
                    log.info("[三码防对中奖金额计算] betId={}, 中奖组数={}", record.getBetId(), fangduiWinningList.size());
                }
            } else if (methodId == 29) { // 单双
                if (checkHeZhiDanShuang(record.getDanshuang(), draw.getDanshuang())) {
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", record.getDanshuang() == 200 ? "单" : "双");
                    winningList.add(winningObj);
                }
            } else if (methodId == 30) { // 大小
                if (checkHeZhiDaXiao(record.getDaxiao(), draw.getDaxiao())) {
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", record.getDaxiao() == 300 ? "大" : "小");
                    winningList.add(winningObj);
                }
            } else if (methodId == 34) { // 两码定位
                if (numbersArray != null) {
                    // 逐组判断每个三位数模式
                    for (int i = 0; i < numbersArray.size(); i++) {
                        JSONObject betNumber = numbersArray.getJSONObject(i);
                        String pattern = null;
                        String fieldKey = null;

                        // 获取投注模式（可能在a、b、c等字段中）
                        if (betNumber.containsKey("a")) {
                            pattern = betNumber.getString("a");
                            fieldKey = "a";
                        } else if (betNumber.containsKey("b")) {
                            pattern = betNumber.getString("b");
                            fieldKey = "b";
                        } else if (betNumber.containsKey("c")) {
                            pattern = betNumber.getString("c");
                            fieldKey = "c";
                        }

                        if (pattern != null && checkSinglePattern(pattern, numbers)) {
                            JSONObject winningObj = new JSONObject();
                            winningObj.put(fieldKey, pattern);
                            winningList.add(winningObj);
                        }
                    }
                }
            } else if (methodId == 35) { // 一码单双
                if (record.getDingwei() != null && numbersArray != null) {
                    int position = 0;
                    if (record.getDingwei() == 100) {
                        position = 0; // 百位
                    } else if (record.getDingwei() == 101) {
                        position = 1; // 十位
                    } else if (record.getDingwei() == 102) {
                        position = 2; // 个位
                    }

                    String targetNumber = numbers[position];
                    int targetNum = Integer.parseInt(targetNumber);
                    boolean isDrawOdd = (targetNum % 2 == 1);

                    for (int i = 0; i < numbersArray.size(); i++) {
                        JSONObject betNumber = numbersArray.getJSONObject(i);
                        String betDanShuang = betNumber.getString("a");
                        boolean isBetOdd = "单".equals(betDanShuang);

                        if (isDrawOdd == isBetOdd) {
                            JSONObject winningObj = new JSONObject();
                            winningObj.put("a", betDanShuang);
                            winningList.add(winningObj);
                        }
                    }
                }
            } else if (methodId == 36) { // 一码大小
                if (record.getDingwei() != null && numbersArray != null) {
                    int position = 0;
                    if (record.getDingwei() == 100) {
                        position = 0; // 百位
                    } else if (record.getDingwei() == 101) {
                        position = 1; // 十位
                    } else if (record.getDingwei() == 102) {
                        position = 2; // 个位
                    }

                    String targetNumber = numbers[position];
                    int targetNum = Integer.parseInt(targetNumber);
                    boolean isDrawBig = (targetNum >= 5);

                    for (int i = 0; i < numbersArray.size(); i++) {
                        JSONObject betNumber = numbersArray.getJSONObject(i);
                        String betDaXiao = betNumber.getString("a");
                        boolean isBetBig = "大".equals(betDaXiao);

                        if (isDrawBig == isBetBig) {
                            JSONObject winningObj = new JSONObject();
                            winningObj.put("a", betDaXiao);
                            winningList.add(winningObj);
                        }
                    }
                }
            } else if (methodId >= 21 && methodId <= 27) { // 和值
                if (record.getHezhi() == null) {
                    log.error("和值参数为空 - 投注记录ID: {}", record.getBetId());
                    return;
                }
                if (checkHeZhi(record.getHezhi(), draw.getHezhi())) {
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", "和" + record.getHezhi());
                    winningList.add(winningObj);
                }
            } else {
                // 其他未知玩法，直接写开奖号码
                JSONObject numberMap = new JSONObject();
                numberMap.put("a", numbers[0]);
                numberMap.put("b", numbers[1]);
                numberMap.put("c", numbers[2]);
                winningList.add(numberMap);
            }

            if (!winningList.isEmpty()) {
                JSONObject winningObj = new JSONObject();
                winningObj.put("winning", winningList);
                record.setWinningNumbers(winningObj.toJSONString());
            }
        } catch (Exception e) {
            log.error("处理中奖号码时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查多码组三
     */
    private boolean checkMultiMaZuSan(JSONArray numbersArray, String[] numbers, Integer duizi, GameRecord record,
            GameDraw draw, Long methodId) {
        try {
            if (numbersArray == null || numbers == null || numbers.length != 3) {
                return false;
            }

            // 如果duizi为null或不为1，直接返回false
            if (duizi == null || duizi != 1) {
                log.info("对子参数无效: {}", duizi);
                return false;
            }

            boolean hasWinning = false;
            JSONArray winningCombinations = new JSONArray();

            // 遍历每组投注号码
            for (int i = 0; i < numbersArray.size(); i++) {
                JSONObject betNumber = numbersArray.getJSONObject(i);
                Set<String> betSet = new HashSet<>();
                for (String key : betNumber.keySet()) {
                    betSet.add(betNumber.getString(key));
                }

                // 检查投注号码是否包含开奖号码中的所有数字
                boolean isMatch = true;
                for (String number : numbers) {
                    if (!betSet.contains(number)) {
                        isMatch = false;
                        break;
                    }
                }

                if (isMatch) {
                    hasWinning = true;
                    // 创建中奖组合
                    JSONObject winningCombination = new JSONObject();
                    winningCombination.put("a", numbers[0]);
                    winningCombination.put("b", numbers[1]);
                    winningCombination.put("c", numbers[2]);
                    winningCombinations.add(winningCombination);
                }
            }

            if (hasWinning) {
                // 设置中奖号码
                JSONObject winningNumbers = new JSONObject();
                winningNumbers.put("winning", winningCombinations);
                record.setWinningNumbers(winningNumbers.toJSONString());
                log.info("多码组三中奖 - 投注号码: {}, 中奖号码: {}", record.getBetNumbers(), record.getWinningNumbers());
            }

            return hasWinning;
        } catch (Exception e) {
            log.error("检查多码组三中奖时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /// 多码组六
    private boolean checkMultiMaZuLiu(JSONArray numbersArray, String[] numbers, GameRecord record, GameDraw draw,
            Long methodId) {
        try {
            if (numbersArray == null || numbers == null || numbers.length != 3) {
                return false;
            }

            // 检查开奖号码是否为组六（三个不同的数字）
            Set<String> winningSet = new HashSet<>();
            for (String number : numbers) {
                winningSet.add(number);
            }
            // 如果开奖号码有重复，则不是组六
            if (winningSet.size() != 3) {
                log.info("开奖号码不是组六: {}", Arrays.toString(numbers));
                return false;
            }

            boolean hasWinning = false;
            JSONArray winningCombinations = new JSONArray();

            // 遍历每组投注号码
            for (int i = 0; i < numbersArray.size(); i++) {
                JSONObject betNumber = numbersArray.getJSONObject(i);
                Set<String> betSet = new HashSet<>();
                for (String key : betNumber.keySet()) {
                    betSet.add(betNumber.getString(key));
                }

                // 检查投注号码是否包含开奖号码中的所有数字
                boolean isMatch = true;
                for (String number : winningSet) {
                    if (!betSet.contains(number)) {
                        isMatch = false;
                        break;
                    }
                }

                if (isMatch) {
                    hasWinning = true;
                    // 创建中奖组合
                    JSONObject winningCombination = new JSONObject();
                    winningCombination.put("a", numbers[0]);
                    winningCombination.put("b", numbers[1]);
                    winningCombination.put("c", numbers[2]);
                    winningCombinations.add(winningCombination);
                }
            }

            if (hasWinning) {
                // 设置中奖号码
                JSONObject winningNumbers = new JSONObject();
                winningNumbers.put("winning", winningCombinations);
                record.setWinningNumbers(winningNumbers.toJSONString());
                log.info("多码组六中奖 - 投注号码: {}, 中奖号码: {}", record.getBetNumbers(), record.getWinningNumbers());
            }

            return hasWinning;
        } catch (Exception e) {
            log.error("检查多码组六中奖时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int decreaseAllTotalBets(String qihao, Long lotteryId, Integer totalBets) {
        Long currentUserId = GameUserUtils.getCurrentSysUserId();
        return gameDrawMapper.decreaseAllTotalBets(qihao, lotteryId, totalBets, currentUserId);
    }

    @Override
    public GameDrawStats getTodayStats() {
        // 根据用户权限返回不同的统计数据
        if (DataPermissionUtils.isAdmin()) {
           
            GameDrawStats stats = gameDrawMapper.selectTodayStats();
            
            return stats;
        } else {
            Long currentUserId = GameUserUtils.getCurrentSysUserId();
            
            GameDrawStats stats = gameDrawMapper.selectTodayStatsByUser(currentUserId);
           
            return stats;
        }
    }

    @Override
    public GameDrawStats getMonthStats() {
        // 根据用户权限返回不同的统计数据
        if (DataPermissionUtils.isAdmin()) {
        
            GameDrawStats stats = gameDrawMapper.selectMonthStats();
           
            return stats;
        } else {
            Long currentUserId = GameUserUtils.getCurrentSysUserId();
         
            GameDrawStats stats = gameDrawMapper.selectMonthStatsByUser(currentUserId);
          
            return stats;
        }
    }

    @Override
    public GameDrawStats getTotalStats() {
        // 根据用户权限返回不同的统计数据
        if (DataPermissionUtils.isAdmin()) {
     
            GameDrawStats stats = gameDrawMapper.selectTotalStats();
          
            return stats;
        } else {
            Long currentUserId = GameUserUtils.getCurrentSysUserId();
           
            GameDrawStats stats = gameDrawMapper.selectTotalStatsByUser(currentUserId);
            
            return stats;
        }
    }

    /**
     * 包打组六判断
     * 判断开奖号码是否没有对子
     */
    private boolean checkBaoDaZuLiu(String[] winningArray, GameRecord gameRecord) {
        int winA = Integer.parseInt(winningArray[0]);
        int winB = Integer.parseInt(winningArray[1]);
        int winC = Integer.parseInt(winningArray[2]);
        boolean noPair = (winA != winB) && (winA != winC) && (winB != winC);
        if (noPair) {
            JSONObject winningObj = new JSONObject();
            JSONArray winningNumbers = new JSONArray();
            JSONObject winningNumber = new JSONObject();
            winningNumber.put("a", "包打组六");
            winningNumbers.add(winningNumber);
            winningObj.put("winning", winningNumbers);
            gameRecord.setWinningNumbers(winningObj.toJSONString());
            return true;
        }
        return false;
    }

    /**
     * 和值判断
     */
    private boolean checkHeZhi(Long betHezhi, Integer drawHezhi) {
        try {
            if (betHezhi == null || drawHezhi == null) {
                return false;
            }
            // 简单判断和值是否相等
            return betHezhi.intValue() == drawHezhi;
        } catch (Exception e) {
            log.error("检查和值中奖时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 和值单双判断
     */
    private boolean checkHeZhiDanShuang(Long betDanshuang, Integer drawDanshuang) {
        return betDanshuang != null && drawDanshuang != null &&
                betDanshuang.longValue() == drawDanshuang.longValue();
    }

    /**
     * 和值大小判断
     */
    private boolean checkHeZhiDaXiao(Long betDaxiao, Integer drawDaxiao) {
        return betDaxiao != null && drawDaxiao != null &&
                betDaxiao.longValue() == drawDaxiao.longValue();
    }





    /**
     * 包打组三判断
     */
    private boolean checkBaoDaZuSan(String[] winningArray, Long duizi, GameRecord gameRecord, GameDraw draw) {
        try {
            // 统计开奖号码中每个数字出现的次数
            Map<String, Integer> winningCounter = new HashMap<>();
            for (String number : winningArray) {
                winningCounter.put(number, winningCounter.getOrDefault(number, 0) + 1);
            }

            // 检查开奖号码是否为组三（一个对子一个单数）
            boolean isZuSan = false;
            String pairNumber = null;
            String singleNumber = null;
            for (Map.Entry<String, Integer> entry : winningCounter.entrySet()) {
                if (entry.getValue() == 2) {
                    isZuSan = true;
                    pairNumber = entry.getKey();
                } else if (entry.getValue() == 1) {
                    singleNumber = entry.getKey();
                }
            }

            // 如果是组三且duizi为1，则中奖
            if (isZuSan && duizi != null && duizi == 1) {
                // 设置中奖号码
                JSONObject winningObj = new JSONObject();
                JSONArray winningNumbers = new JSONArray();
                JSONObject winningNumber = new JSONObject();
                winningNumber.put("a", "包打组三");
                winningNumbers.add(winningNumber);
                winningObj.put("winning", winningNumbers);
                gameRecord.setWinningNumbers(winningObj.toJSONString());
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("检查包打组三中奖时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 杀码判断
     * 判断开奖号码中是否不包含投注的杀码号码
     * 投注号码格式：{"numbers": [{"a": "杀码号码"}, ...]}
     */
    private boolean checkShaMa(JSONArray numbersArray, String[] winningArray) {
        try {
            if (numbersArray == null || numbersArray.isEmpty()) {
                return false;
            }

            // 获取投注的杀码号码
            JSONObject firstNumber = numbersArray.getJSONObject(0);
            String killNumber = firstNumber.getString("a");

            // 检查开奖号码中是否包含杀码号码
            for (String winningNumber : winningArray) {
                if (winningNumber.equals(killNumber)) {
                    return false; // 如果开奖号码中包含杀码号码，则不中奖
                }
            }
            return true; // 如果开奖号码中不包含杀码号码，则中奖

        } catch (Exception e) {
            log.error("杀码判断出错", e);
            return false;
        }
    }

    /**
     * 豹子判断
     */
    private boolean checkBaoZi(String[] winningArray) {
        try {
            if (winningArray == null || winningArray.length != 3) {
                return false;
            }
            // 检查三个数字是否相同
            return winningArray[0].equals(winningArray[1]) && winningArray[1].equals(winningArray[2]);
        } catch (Exception e) {
            log.error("检查豹子中奖时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertGameRecord(GameRecord gameRecord) {
        // 对于不需要投注号码的玩法，设置一个默认的JSON
        if (gameRecord.getMethodId() == 20 || gameRecord.getMethodId() == 31) { // 包打组六或包打组三
            gameRecord.setBetNumbers("{\"numbers\":[]}");
        }
        return gameRecordMapper.insertGameRecord(gameRecord);
    }

    /**
     * 查询当前最大期号
     * 
     * @return 最大期号
     */
    @Override
    public String selectMaxQihao() {
        // 根据用户权限返回不同的最大期号
        if (DataPermissionUtils.isAdmin()) {
            return gameDrawMapper.selectMaxQihao();
        } else {
            Long currentUserId = GameUserUtils.getCurrentSysUserId();
            return gameDrawMapper.selectMaxQihaoByUser(currentUserId);
        }
    }

    /**
     * 新增下一期开奖记录
     * 
     * @param gameDraw 开奖信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertNextDraw(GameDraw gameDraw) {
        // 设置默认值
        gameDraw.setZongtouzhu(BigDecimal.ZERO);

        gameDraw.setZongjiesuan(BigDecimal.ZERO);
        gameDraw.setYingli(BigDecimal.ZERO);
        gameDraw.setJiesuan(0);
        gameDraw.setAllTotalBets(0);

        // 插入开奖记录
        int rows = insertGameDraw(gameDraw);

        if (rows > 0) {
            // 更新game_qihao表
            GameQihao gameQihao = gameQihaoMapper.selectGameQihaoByLotteryId(gameDraw.getLotteryId());
            if (gameQihao != null) {
                gameQihao.setQihao(gameDraw.getQihao());
                gameQihaoMapper.updateGameQihao(gameQihao);
            } else {
                // 如果不存在记录，则新增
                gameQihao = new GameQihao();
                gameQihao.setLotteryId(gameDraw.getLotteryId());
                gameQihao.setQihao(gameDraw.getQihao());
                gameQihaoMapper.insertGameQihao(gameQihao);
            }
        }

        return rows;
    }

    @Transactional(rollbackFor = Exception.class)
    public void clearAllGameData() {
        Long currentUserId = SecurityUtils.getUserId();
        boolean isAdmin = SecurityUtils.isAdmin(currentUserId);

        log.info("一键清空数据：用户ID={}, 是否管理员={}", currentUserId, isAdmin);

        // 防止同一用户多次点击清空按钮
        if (!settlementLockManager.acquireCancelLock(currentUserId, 30)) {
            throw new ServiceException("您的清空操作正在进行中，请稍后再试");
        }

        try {

        if (isAdmin) {
            // 超级管理员：清空所有数据
            log.info("超级管理员清空所有数据");

            // 清空中奖表
            gameWinningMapper.deleteAll();
            // 清空下注表
            gameRecordMapper.deleteAll();
            // 清空流水表
            gameSerialMapper.deleteAll();
            

            // 重置自增ID
            gameRecordMapper.resetAutoIncrement();
            gameWinningMapper.resetAutoIncrement();
            gameSerialMapper.resetAutoIncrement();
            
            // 重置玩家表金额
            gameCustomerMapper.resetAllAmounts();
            // 重置玩家表fc_touzhu、tc_touzhu、fc_zj、tc_zj字段
            gameCustomerMapper.resetFcTcZjAmounts();
            // 清空开奖表部分字段
            gameDrawMapper.clearDrawFields();
            // 重置开奖表状态字段为0
            gameDrawMapper.resetDrawStatus();

            // 超级管理员清空系统日志
            sysOperLogMapper.cleanOperLog();
           

            sysLogininforMapper.cleanLogininfor();
            

            sysUserMapper.clearUserLoginAndTimeFields();
            

            // 清空工单系统数据
            clearAllTicketData();
            

            log.info("超级管理员清空所有数据完成");
        } else {


            // 清空当前用户的中奖表数据
            int winningDeleted = gameWinningMapper.deleteBySysUserId(currentUserId);


            // 清空当前用户的下注表数据
            int recordDeleted = gameRecordMapper.deleteBySysUserId(currentUserId);


            // 清空当前用户的流水表数据
            int serialDeleted = gameSerialMapper.deleteBySysUserId(currentUserId);


            // 重置当前用户的玩家表金额
            int customerUpdated = gameCustomerMapper.resetAmountsBySysUserId(currentUserId);


            // 重置当前用户的开奖表部分字段
            gameDrawMapper.clearDrawFieldsBySysUserId(currentUserId);


            // 重置当前用户的开奖表状态字段为0
            gameDrawMapper.resetDrawStatusBySysUserId(currentUserId);




        }
        } finally {
            // 释放清空操作锁
            settlementLockManager.releaseCancelLock(currentUserId);
        }
    }

    /**
     * 一码单双判断
     * 判断指定位置的开奖号码单双性与投注的单双是否匹配
     *
     * @param numbersArray 投注号码数组
     * @param winningArray 开奖号码数组
     * @param dingwei 定位参数 (100-百位, 101-十位, 102-个位)
     * @param gameRecord 投注记录
     * @return 是否中奖
     */
    private boolean checkYiMaDanShuang(JSONArray numbersArray, String[] winningArray, Long dingwei, GameRecord gameRecord) {
        try {
            if (numbersArray == null || numbersArray.isEmpty() || winningArray == null || winningArray.length != 3 || dingwei == null) {
                return false;
            }

            // 根据dingwei值选择对应的开奖号码位置
            int position = 0;
            if (dingwei == 100) {
                position = 0; // 百位
            } else if (dingwei == 101) {
                position = 1; // 十位
            } else if (dingwei == 102) {
                position = 2; // 个位
            } else {
                log.error("无效的定位参数: {} - 投注记录ID: {}", dingwei, gameRecord.getBetId());
                return false;
            }

            // 获取指定位置的开奖号码
            String targetNumber = winningArray[position];
            int targetNum = Integer.parseInt(targetNumber);

            // 判断开奖号码的单双性
            // 0、2、4、6、8是双，1、3、5、7、9是单
            boolean isDrawOdd = (targetNum % 2 == 1); // true为单，false为双

            boolean hasWinning = false;
            JSONArray winningList = new JSONArray();

            // 遍历投注号码，检查是否与开奖号码的单双性匹配
            for (int i = 0; i < numbersArray.size(); i++) {
                JSONObject betNumber = numbersArray.getJSONObject(i);
                String betDanShuang = betNumber.getString("a");

                // 判断投注的单双
                boolean isBetOdd = "单".equals(betDanShuang);

                if (isDrawOdd == isBetOdd) {
                    hasWinning = true;
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", betDanShuang);
                    winningList.add(winningObj);
                    log.info("一码单双中奖 - 位置: {}, 开奖号码: {}, 投注: {}",
                            dingwei == 100 ? "百位" : (dingwei == 101 ? "十位" : "个位"),
                            targetNumber, betDanShuang);
                }
            }

            if (hasWinning) {
                JSONObject winningObj = new JSONObject();
                winningObj.put("winning", winningList);
                gameRecord.setWinningNumbers(winningObj.toJSONString());
            }

            return hasWinning;
        } catch (Exception e) {
            log.error("一码单双判断出错", e);
            return false;
        }
    }

    /**
     * 一码大小判断
     * 判断指定位置的开奖号码大小与投注的大小是否匹配
     *
     * @param numbersArray 投注号码数组
     * @param winningArray 开奖号码数组
     * @param dingwei 定位参数 (100-百位, 101-十位, 102-个位)
     * @param gameRecord 投注记录
     * @return 是否中奖
     */
    private boolean checkYiMaDaXiao(JSONArray numbersArray, String[] winningArray, Long dingwei, GameRecord gameRecord) {
        try {
            if (numbersArray == null || numbersArray.isEmpty() || winningArray == null || winningArray.length != 3 || dingwei == null) {
                return false;
            }

            // 根据dingwei值选择对应的开奖号码位置
            int position = 0;
            if (dingwei == 100) {
                position = 0; // 百位
            } else if (dingwei == 101) {
                position = 1; // 十位
            } else if (dingwei == 102) {
                position = 2; // 个位
            } else {
                log.error("无效的定位参数: {} - 投注记录ID: {}", dingwei, gameRecord.getBetId());
                return false;
            }

            // 获取指定位置的开奖号码
            String targetNumber = winningArray[position];
            int targetNum = Integer.parseInt(targetNumber);

            // 判断开奖号码的大小
            // 01234是小，56789是大
            boolean isDrawBig = (targetNum >= 5); // true为大，false为小

            boolean hasWinning = false;
            JSONArray winningList = new JSONArray();

            // 遍历投注号码，检查是否与开奖号码的大小匹配
            for (int i = 0; i < numbersArray.size(); i++) {
                JSONObject betNumber = numbersArray.getJSONObject(i);
                String betDaXiao = betNumber.getString("a");

                // 判断投注的大小
                boolean isBetBig = "大".equals(betDaXiao);

                if (isDrawBig == isBetBig) {
                    hasWinning = true;
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", betDaXiao);
                    winningList.add(winningObj);
                    log.info("一码大小中奖 - 位置: {}, 开奖号码: {}, 投注: {}",
                            dingwei == 100 ? "百位" : (dingwei == 101 ? "十位" : "个位"),
                            targetNumber, betDaXiao);
                }
            }

            if (hasWinning) {
                JSONObject winningObj = new JSONObject();
                winningObj.put("winning", winningList);
                gameRecord.setWinningNumbers(winningObj.toJSONString());
            }

            return hasWinning;
        } catch (Exception e) {
            log.error("一码大小判断出错", e);
            return false;
        }
    }

    /**
     * 两码定位判断
     * 判断指定两个位置的开奖号码是否与投注号码匹配
     *
     * @param numbersArray 投注号码数组
     * @param winningArray 开奖号码数组
     * @param dingwei 定位参数 (200-十位+个位, 201-百位+个位, 202-百位+十位)
     * @param gameRecord 投注记录
     * @return 是否中奖
     */
    private boolean checkLiangMaDingWei(JSONArray numbersArray, String[] winningArray, Long dingwei, GameRecord gameRecord) {
        try {
            if (numbersArray == null || numbersArray.isEmpty() || winningArray == null || winningArray.length != 3 || dingwei == null) {
                return false;
            }

            // 根据dingwei值确定检查的两个位置
            int position1 = 0, position2 = 0;
            String positionDesc = "";

            if (dingwei == 200) {
                // 十位+个位组合定位 (*96格式)
                position1 = 1; // 十位
                position2 = 2; // 个位
                positionDesc = "十位+个位";
            } else if (dingwei == 201) {
                // 百位+个位组合定位 (9*6格式)
                position1 = 0; // 百位
                position2 = 2; // 个位
                positionDesc = "百位+个位";
            } else if (dingwei == 202) {
                // 百位+十位组合定位 (96*格式)
                position1 = 0; // 百位
                position2 = 1; // 十位
                positionDesc = "百位+十位";
            } else {
                log.error("无效的两码定位参数: {} - 投注记录ID: {}", dingwei, gameRecord.getBetId());
                return false;
            }

            // 获取指定位置的开奖号码
            String targetNumber1 = winningArray[position1];
            String targetNumber2 = winningArray[position2];
            String targetCombination = targetNumber1 + targetNumber2;

            boolean hasWinning = false;
            JSONArray winningList = new JSONArray();

            // 遍历投注号码，检查是否与开奖号码的两个位置匹配
            for (int i = 0; i < numbersArray.size(); i++) {
                JSONObject betNumber = numbersArray.getJSONObject(i);
                String betPattern = betNumber.getString("a"); // 投注的号码模式，如"9*6"

                // 从投注模式中提取两个数字进行匹配
                String betCombination = extractDigitsFromPattern(betPattern, dingwei);

                if (targetCombination.equals(betCombination)) {
                    hasWinning = true;
                    JSONObject winningObj = new JSONObject();
                    winningObj.put("a", betPattern); // 记录原始投注模式
                    winningList.add(winningObj);
                    log.info("两码定位中奖 - 位置: {}, 开奖号码: {}, 投注模式: {}, 匹配数字: {}",
                            positionDesc, targetCombination, betPattern, betCombination);
                }
            }

            if (hasWinning) {
                JSONObject winningObj = new JSONObject();
                winningObj.put("winning", winningList);
                gameRecord.setWinningNumbers(winningObj.toJSONString());
            }

            return hasWinning;
        } catch (Exception e) {
            log.error("两码定位判断出错", e);
            return false;
        }
    }

    /**
     * 从投注模式中提取数字组合
     *
     * @param pattern 投注模式，如"9*6"、"*96"、"96*"
     * @param dingwei 定位参数
     * @return 提取的数字组合
     */
    private String extractDigitsFromPattern(String pattern, Long dingwei) {
        if (pattern == null || pattern.length() != 3) {
            return "";
        }

        // 移除占位符，获取两个数字
        String digits = pattern.replace("*", "").replace("X", "").replace("x", "");

        if (digits.length() != 2) {
            return "";
        }

        // 根据dingwei确定数字的顺序
        if (dingwei == 200) {
            // *96格式：十位+个位，digits="96"，返回"96"
            return digits;
        } else if (dingwei == 201) {
            // 9*6格式：百位+个位，digits="96"，返回"96"
            return digits;
        } else if (dingwei == 202) {
            // 96*格式：百位+十位，digits="96"，返回"96"
            return digits;
        }

        return digits;
    }

    /**
     * 两码定位按模式判断
     * 逐组判断每个三位数模式是否中奖
     *
     * @param numbersArray 投注号码数组，如[{"a": "9*6"},{"b": "*96"},{"c": "36*"}]
     * @param winningArray 开奖号码数组
     * @param gameRecord 投注记录
     * @return 是否中奖
     */
    private boolean checkLiangMaDingWeiByPattern(JSONArray numbersArray, String[] winningArray, GameRecord gameRecord) {
        try {
            log.info("两码定位结算开始 - 投注记录ID: {}, 开奖号码: {}, 投注数据: {}",
                    gameRecord.getBetId(), String.join(",", winningArray), numbersArray.toJSONString());

            if (numbersArray == null || numbersArray.isEmpty() || winningArray == null || winningArray.length != 3) {
                log.error("两码定位参数验证失败 - numbersArray: {}, winningArray: {}",
                        numbersArray, winningArray != null ? String.join(",", winningArray) : "null");
                return false;
            }

            boolean hasWinning = false;
            JSONArray winningList = new JSONArray();

            // 逐组判断每个三位数模式
            for (int i = 0; i < numbersArray.size(); i++) {
                JSONObject betNumber = numbersArray.getJSONObject(i);
                String pattern = null;
                String fieldKey = null;

                // 获取投注模式（可能在a、b、c等字段中）
                if (betNumber.containsKey("a")) {
                    pattern = betNumber.getString("a");
                    fieldKey = "a";
                } else if (betNumber.containsKey("b")) {
                    pattern = betNumber.getString("b");
                    fieldKey = "b";
                } else if (betNumber.containsKey("c")) {
                    pattern = betNumber.getString("c");
                    fieldKey = "c";
                }

                log.info("检查投注模式 - 第{}组: {}, 字段: {}", i+1, pattern, fieldKey);

                if (pattern == null || pattern.length() != 3) {
                    log.warn("投注模式无效 - 第{}组: {}", i+1, pattern);
                    continue;
                }

                // 检查这个模式是否中奖
                boolean isPatternWinning = checkSinglePattern(pattern, winningArray);
                log.info("模式匹配结果 - 模式: {}, 开奖: {}, 结果: {}",
                        pattern, String.join("", winningArray), isPatternWinning ? "中奖" : "未中奖");

                if (isPatternWinning) {
                    hasWinning = true;
                    JSONObject winningObj = new JSONObject();
                    winningObj.put(fieldKey, pattern);
                    winningList.add(winningObj);
                    log.info("两码定位中奖 - 投注模式: {}, 开奖号码: {}", pattern, String.join("", winningArray));
                }
            }

            if (hasWinning) {
                JSONObject winningObj = new JSONObject();
                winningObj.put("winning", winningList);
                gameRecord.setWinningNumbers(winningObj.toJSONString());
                log.info("两码定位最终中奖结果 - 投注记录ID: {}, 中奖模式数: {}",
                        gameRecord.getBetId(), winningList.size());
            } else {
                log.info("两码定位未中奖 - 投注记录ID: {}", gameRecord.getBetId());
            }

            return hasWinning;
        } catch (Exception e) {
            log.error("两码定位按模式判断出错 - 投注记录ID: {}", gameRecord.getBetId(), e);
            return false;
        }
    }

    /**
     * 检查单个三位数模式是否中奖
     *
     * @param pattern 投注模式，如"9*6"、"*96"、"36*"
     * @param winningArray 开奖号码数组
     * @return 是否中奖
     */
    private boolean checkSinglePattern(String pattern, String[] winningArray) {
        try {
            if (pattern == null || pattern.length() != 3 || winningArray == null || winningArray.length != 3) {
                log.warn("单个模式检查参数无效 - pattern: {}, winningArray: {}",
                        pattern, winningArray != null ? String.join(",", winningArray) : "null");
                return false;
            }

            log.info("开始检查单个模式 - 投注模式: {}, 开奖号码: {}", pattern, String.join(",", winningArray));

            // 逐位比较
            for (int i = 0; i < 3; i++) {
                char patternChar = pattern.charAt(i);
                String winningDigit = winningArray[i];

                log.info("位置{}比较 - 投注: {}, 开奖: {}", i, patternChar, winningDigit);

                // 如果是占位符，跳过
                if (patternChar == '*' || patternChar == 'X' || patternChar == 'x') {
                    log.info("位置{}是占位符，跳过", i);
                    continue;
                }

                // 如果不是占位符，必须完全匹配
                if (!String.valueOf(patternChar).equals(winningDigit)) {
                    log.info("位置{}不匹配 - 投注: {}, 开奖: {}, 返回false", i, patternChar, winningDigit);
                    return false;
                }

                log.info("位置{}匹配成功 - 投注: {}, 开奖: {}", i, patternChar, winningDigit);
            }

            log.info("模式完全匹配 - 投注模式: {}, 开奖号码: {}", pattern, String.join(",", winningArray));
            return true;
        } catch (Exception e) {
            log.error("检查单个模式出错: {}", pattern, e);
            return false;
        }
    }

    /**
     * 手动同步game_customer表的金额字段
     * 用于修复已结算但未同步的数据
     */
    @Override
    @Transactional
    public void syncCustomerAmounts() {
        Long currentUserId = GameUserUtils.getCurrentSysUserId();
        log.info("用户 {} 开始手动同步game_customer金额", currentUserId);

        try {
            // 获取当前用户的所有玩家
            List<GameCustomer> customers = gameCustomerMapper.selectGameCustomerList(new GameCustomer() {{
                setSysUserId(currentUserId);
            }});

            for (GameCustomer customer : customers) {
                Long userId = customer.getUserId();

                // 同步总中奖金额
                BigDecimal totalWin = gameWinningMapper.sumWinAmountByUserIdAndSysUser(userId, currentUserId);
                gameCustomerMapper.updateTotalWinAmountAbsolute(userId, currentUserId, totalWin);
                log.info("[手动同步] 玩家ID:{} (系统用户:{}) 总中奖金额已同步为:{}", userId, currentUserId, totalWin);

                // 同步福彩中奖金额
                BigDecimal fcZj = gameWinningMapper.sumWinAmountByUserIdAndLotteryIdAndSysUser(userId, 1L, currentUserId);
                gameCustomerMapper.updateFcZj(userId, currentUserId, fcZj);
                log.info("[手动同步] 玩家ID:{} (系统用户:{}) 福彩中奖金额已同步为:{}", userId, currentUserId, fcZj);

                // 同步体彩中奖金额
                BigDecimal tcZj = gameWinningMapper.sumWinAmountByUserIdAndLotteryIdAndSysUser(userId, 2L, currentUserId);
                gameCustomerMapper.updateTcZj(userId, currentUserId, tcZj);
                log.info("[手动同步] 玩家ID:{} (系统用户:{}) 体彩中奖金额已同步为:{}", userId, currentUserId, tcZj);
            }

            log.info("用户 {} 手动同步game_customer金额完成，共同步{}个玩家", currentUserId, customers.size());

        } catch (Exception e) {
            log.error("用户 {} 手动同步game_customer金额时发生错误: {}", currentUserId, e.getMessage(), e);
            throw new ServiceException("手动同步金额时发生错误: " + e.getMessage());
        }
    }

    /**
     * 清空工单系统数据
     */
    private void clearAllTicketData() {
        try {
            // 清空工单回复表
            int replyCount = sysTicketReplyMapper.deleteAllReplies();
            

            // 清空工单表
            int ticketCount = sysTicketMapper.deleteAllTickets();
            

            // 重置自增ID
            sysTicketReplyMapper.resetAutoIncrement();
            sysTicketMapper.resetAutoIncrement();
            

        } catch (Exception e) {
            log.error("清空工单系统数据时发生错误: {}", e.getMessage(), e);
            throw new ServiceException("清空工单系统数据失败: " + e.getMessage());
        }
    }

    /**
     * 胆拖中奖判断
     *
     * @param gameRecord 投注记录
     * @param winningArray 开奖号码数组
     * @return 是否中奖
     */
    private boolean checkDantuoWinning(GameRecord gameRecord, String[] winningArray) {
        try {
            JSONObject betNumbersJson = JSON.parseObject(gameRecord.getBetNumbers());
            JSONArray numbersArray = betNumbersJson.getJSONArray("numbers");

            if (numbersArray == null || numbersArray.isEmpty()) {
                log.error("胆拖投注号码为空 - 投注记录ID: {}", gameRecord.getBetId());
                return false;
            }

            // 获取胆码和拖码
            JSONObject dantuoInfo = numbersArray.getJSONObject(0);
            String danma = dantuoInfo.getString("danma");
            String tuomaStr = dantuoInfo.getString("tuoma");

            if (danma == null || tuomaStr == null) {
                log.error("胆码或拖码为空 - 投注记录ID: {}, danma: {}, tuoma: {}",
                    gameRecord.getBetId(), danma, tuomaStr);
                return false;
            }

            String[] tuoma = tuomaStr.split(",");
            Long methodId = gameRecord.getMethodId();
            boolean isZusan = methodId >= 44 && methodId <= 51;

            // 检查胆码是否在开奖号码中
            boolean danmaExists = Arrays.asList(winningArray).contains(danma);
            if (!danmaExists) {
                log.debug("胆码不在开奖号码中 - 投注记录ID: {}, 胆码: {}, 开奖号码: {}",
                    gameRecord.getBetId(), danma, Arrays.toString(winningArray));
                return false;
            }

            // 根据玩法类型判断中奖
            if (isZusan) {
                return checkDantuoZusanWinning(danma, tuoma, winningArray);
            } else {
                return checkDantuoZuliuWinning(danma, tuoma, winningArray);
            }

        } catch (Exception e) {
            log.error("胆拖中奖判断出错 - 投注记录ID: {}, 错误: {}", gameRecord.getBetId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 胆拖组三中奖判断
     *
     * @param danma 胆码
     * @param tuoma 拖码数组
     * @param winningArray 开奖号码数组
     * @return 是否中奖
     */
    private boolean checkDantuoZusanWinning(String danma, String[] tuoma, String[] winningArray) {
        try {
            // 统计开奖号码中各数字出现次数
            Map<String, Integer> countMap = new HashMap<>();
            for (String num : winningArray) {
                countMap.put(num, countMap.getOrDefault(num, 0) + 1);
            }

            // 检查是否为组三形态（有数字出现2次）
            boolean hasDouble = countMap.values().stream().anyMatch(count -> count == 2);
            if (!hasDouble) {
                log.debug("开奖号码不是组三形态 - 开奖号码: {}", Arrays.toString(winningArray));
                return false;
            }

            // 检查开奖号码是否包含胆码
            boolean containsDanma = Arrays.asList(winningArray).contains(danma);
            if (!containsDanma) {
                log.debug("开奖号码不包含胆码 - 胆码: {}, 开奖号码: {}",
                    danma, Arrays.toString(winningArray));
                return false;
            }

            // 检查除胆码外的其他开奖号码是否都在拖码范围内
            Set<String> tuomaSet = new HashSet<>(Arrays.asList(tuoma));
            for (String num : winningArray) {
                if (!num.equals(danma) && !tuomaSet.contains(num)) {
                    log.debug("开奖号码包含不在拖码范围内的数字 - 数字: {}, 拖码范围: {}, 开奖号码: {}",
                        num, Arrays.toString(tuoma), Arrays.toString(winningArray));
                    return false;
                }
            }

            log.debug("胆拖组三中奖 - 胆码: {}, 拖码: {}, 开奖号码: {}",
                danma, Arrays.toString(tuoma), Arrays.toString(winningArray));
            return true;

        } catch (Exception e) {
            log.error("胆拖组三中奖判断出错: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 胆拖组六中奖判断
     *
     * @param danma 胆码
     * @param tuoma 拖码数组
     * @param winningArray 开奖号码数组
     * @return 是否中奖
     */
    private boolean checkDantuoZuliuWinning(String danma, String[] tuoma, String[] winningArray) {
        try {
            // 检查是否为组六形态（三个数字都不同）
            Set<String> uniqueNumbers = new HashSet<>(Arrays.asList(winningArray));
            if (uniqueNumbers.size() != 3) {
                log.debug("开奖号码不是组六形态 - 开奖号码: {}", Arrays.toString(winningArray));
                return false;
            }

            // 检查开奖号码是否包含胆码
            boolean containsDanma = Arrays.asList(winningArray).contains(danma);
            if (!containsDanma) {
                log.debug("开奖号码不包含胆码 - 胆码: {}, 开奖号码: {}",
                    danma, Arrays.toString(winningArray));
                return false;
            }

            // 检查除胆码外的其他开奖号码是否都在拖码范围内
            Set<String> tuomaSet = new HashSet<>(Arrays.asList(tuoma));
            for (String num : winningArray) {
                if (!num.equals(danma) && !tuomaSet.contains(num)) {
                    log.debug("开奖号码包含不在拖码范围内的数字 - 数字: {}, 拖码范围: {}, 开奖号码: {}",
                        num, Arrays.toString(tuoma), Arrays.toString(winningArray));
                    return false;
                }
            }

            log.debug("胆拖组六中奖 - 胆码: {}, 拖码: {}, 开奖号码: {}",
                danma, Arrays.toString(tuoma), Arrays.toString(winningArray));
            return true;

        } catch (Exception e) {
            log.error("胆拖组六中奖判断出错: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查跨度玩法中奖
     * 跨度 = 三个开奖号码中的最大值 - 最小值
     *
     * @param gameRecord 投注记录
     * @param winningArray 开奖号码数组
     * @return 是否中奖
     */
    private boolean checkKuaduWinning(GameRecord gameRecord, String[] winningArray) {
        try {
            if (winningArray == null || winningArray.length != 3) {
                log.error("开奖号码格式错误 - 投注记录ID: {}", gameRecord.getBetId());
                return false;
            }

            // 获取三个位置的数字
            int baiwei = Integer.parseInt(winningArray[0]);
            int shiwei = Integer.parseInt(winningArray[1]);
            int gewei = Integer.parseInt(winningArray[2]);

            // 找出最大值和最小值
            int maxValue = Math.max(Math.max(baiwei, shiwei), gewei);
            int minValue = Math.min(Math.min(baiwei, shiwei), gewei);

            // 计算跨度值：最大值 - 最小值
            int actualKuadu = maxValue - minValue;

            // 获取投注的跨度值
            Long methodId = gameRecord.getMethodId();
            int betKuadu = (int) (methodId - 60);

            // 验证跨度值范围
            if (betKuadu < 0 || betKuadu > 9) {
                log.error("跨度值超出范围 - 投注记录ID: {}, method_id: {}, 跨度值: {}",
                    gameRecord.getBetId(), methodId, betKuadu);
                return false;
            }

            boolean isWinning = (actualKuadu == betKuadu);

            log.debug("跨度玩法判断 - 投注记录ID: {}, 开奖号码: {}, 百位: {}, 十位: {}, 个位: {}, 最大值: {}, 最小值: {}, 实际跨度: {}, 投注跨度: {}, 中奖: {}",
                gameRecord.getBetId(), Arrays.toString(winningArray), baiwei, shiwei, gewei, maxValue, minValue, actualKuadu, betKuadu, isWinning);

            return isWinning;

        } catch (NumberFormatException e) {
            log.error("开奖号码格式错误 - 投注记录ID: {}, 开奖号码: {}, 错误: {}",
                gameRecord.getBetId(), Arrays.toString(winningArray), e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("跨度玩法中奖判断出错 - 投注记录ID: {}: {}", gameRecord.getBetId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 数据预加载到内存缓存
     *
     * @param currentUserId 当前用户ID
     * @return true-成功，false-失败
     */
    private boolean loadDataToMemoryCache(Long currentUserId) {
        try {
            log.info("=== 开始数据预加载到内存缓存 ===");
            recordDataCacheManager.loadUserData(currentUserId);
            log.info("=== 数据预加载到内存缓存完成 ===");
            return true;
        } catch (Exception e) {
            log.error("[内存缓存] 用户{}数据预加载失败: {}", currentUserId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 使用内存缓存进行draw数据一致性检查和修复
     *
     * @param currentUserId 当前用户ID
     * @return true-成功，false-获取锁失败
     */
    private boolean performDataConsistencyCheckWithMemoryCache(Long currentUserId) {
        // 尝试从锁池获取draw数据统计锁
        Integer lockIndex = dataStatisticsLockManager.acquireDrawStatisticsLock(currentUserId);
        if (lockIndex == null) {
            log.warn("[draw数据统计] 用户{}获取draw数据统计锁失败，{}", currentUserId, dataStatisticsLockManager.getLockStatus());
            return false;
        }

        try {
            log.info("=== 开始开奖前draw数据一致性检查和修复（使用内存缓存，锁#{}） ===", lockIndex + 1);
            performDrawDataConsistencyCheckAndRepairWithCache(currentUserId);
            log.info("=== 开奖前draw数据一致性检查和修复完成（使用内存缓存，锁#{}） ===", lockIndex + 1);
            return true;
        } finally {
            // 确保释放锁
            dataStatisticsLockManager.releaseDrawStatisticsLock(lockIndex, currentUserId);
        }
    }

    /**
     * 使用内存缓存进行customer数据一致性检查和修复
     *
     * @param currentUserId 当前用户ID
     * @return true-成功，false-获取锁失败
     */
    private boolean performCustomerDataConsistencyCheckWithMemoryCache(Long currentUserId) {
        // 尝试从锁池获取customer数据统计锁
        Integer lockIndex = dataStatisticsLockManager.acquireCustomerStatisticsLockFromPool(currentUserId);
        if (lockIndex == null) {
            log.warn("[customer数据统计] 用户{}获取customer数据统计锁失败，{}", currentUserId, dataStatisticsLockManager.getLockStatus());
            return false;
        }

        try {
            log.info("=== 开始开奖前customer数据一致性检查和修复（使用内存缓存，锁#{}） ===", lockIndex + 1);
            performCustomerDataConsistencyCheckAndRepairWithCache(currentUserId);
            log.info("=== 开奖前customer数据一致性检查和修复完成（使用内存缓存，锁#{}） ===", lockIndex + 1);
            return true;
        } finally {
            // 确保释放锁
            dataStatisticsLockManager.releaseCustomerStatisticsLockByIndex(lockIndex, currentUserId);
        }
    }

    /**
     * 使用内存缓存进行draw数据统计和修复
     *
     * @param currentUserId 当前用户ID
     */
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    private void performDrawDataConsistencyCheckAndRepairWithCache(Long currentUserId) {
        try {
            log.info("[内存缓存draw统计] 开始统计用户{}的各彩种投注额", currentUserId);

            // 从内存缓存计算各彩种投注总额
            Map<Long, BigDecimal> lotteryTotals = recordDataCacheManager.calculateLotteryTotals(currentUserId);

            if (lotteryTotals == null || lotteryTotals.isEmpty()) {
                log.info("[内存缓存draw统计] 用户{}没有投注记录", currentUserId);
                return;
            }

            log.info("[内存缓存draw统计] 用户{}各彩种投注额统计结果: {}", currentUserId, lotteryTotals);

            int totalUpdated = 0;

            // 更新draw表记录
            for (Map.Entry<Long, BigDecimal> entry : lotteryTotals.entrySet()) {
                Long lotteryId = entry.getKey();
                BigDecimal totalAmount = entry.getValue();

                if (lotteryId != null && totalAmount != null) {
                    log.info("[内存缓存draw统计] 准备更新彩种{}, 总投注额: {}", lotteryId, totalAmount);

                    // 更新该用户该彩种的所有draw表记录
                    int updateResult = gameDrawMapper.updateZongtouzhuByLottery(lotteryId, totalAmount, currentUserId);

                    if (updateResult > 0) {
                        totalUpdated += updateResult;
                        log.info("[内存缓存draw更新成功] 用户{}, 彩种{}, 总投注额更新为{}, 影响{}条draw记录",
                            currentUserId, lotteryId, totalAmount, updateResult);
                    } else {
                        log.warn("[内存缓存draw更新失败] 用户{}, 彩种{}, 没有找到对应的draw记录",
                            currentUserId, lotteryId);
                    }
                }
            }

            log.info("[内存缓存draw统计完成] 用户{}, 共更新{}条draw记录", currentUserId, totalUpdated);

        } catch (Exception e) {
            log.error("[内存缓存draw统计] 用户{}统计失败: {}", currentUserId, e.getMessage(), e);
            throw new RuntimeException("内存缓存draw数据统计失败", e);
        }
    }

    /**
     * 使用内存缓存进行customer数据统计和修复
     *
     * @param currentUserId 当前用户ID
     */
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    private void performCustomerDataConsistencyCheckAndRepairWithCache(Long currentUserId) {
        try {
            log.info("[内存缓存customer统计] 开始统计用户{}名下各玩家各彩种投注额", currentUserId);

            // 从内存缓存计算各玩家各彩种投注总额
            Map<Long, Map<Long, BigDecimal>> customerLotteryTotals = recordDataCacheManager.calculateCustomerLotteryTotals(currentUserId);

            if (customerLotteryTotals == null || customerLotteryTotals.isEmpty()) {
                log.info("[内存缓存customer统计] 用户{}名下没有投注记录", currentUserId);
                return;
            }

            log.info("[内存缓存customer统计] 用户{}名下各玩家各彩种投注额统计结果，玩家数: {}", currentUserId, customerLotteryTotals.size());

            int totalUpdated = 0;

            // 遍历每个玩家，更新其投注额统计
            for (Map.Entry<Long, Map<Long, BigDecimal>> customerEntry : customerLotteryTotals.entrySet()) {
                Long userId = customerEntry.getKey();
                Map<Long, BigDecimal> lotteryAmounts = customerEntry.getValue();

                BigDecimal fcTouzhu = lotteryAmounts.getOrDefault(1L, BigDecimal.ZERO);  // 福彩投注额
                BigDecimal tcTouzhu = lotteryAmounts.getOrDefault(2L, BigDecimal.ZERO);  // 体彩投注额
                BigDecimal totalBetAmount = fcTouzhu.add(tcTouzhu);  // 总投注额

                log.info("[内存缓存customer统计] 准备更新玩家{}, 福彩投注额: {}, 体彩投注额: {}, 总投注额: {}",
                    userId, fcTouzhu, tcTouzhu, totalBetAmount);

                // 更新该玩家的投注额统计
                int updateResult = gameCustomerMapper.updateCustomerBetAmounts(userId, currentUserId, fcTouzhu, tcTouzhu, totalBetAmount);

                if (updateResult > 0) {
                    totalUpdated += updateResult;
                    log.info("[内存缓存customer更新成功] 用户{}, 玩家{}, 福彩投注额更新为{}, 体彩投注额更新为{}, 总投注额更新为{}, 影响{}条customer记录",
                        currentUserId, userId, fcTouzhu, tcTouzhu, totalBetAmount, updateResult);
                } else {
                    log.warn("[内存缓存customer更新失败] 用户{}, 玩家{}, 没有找到对应的customer记录",
                        currentUserId, userId);
                }
            }

            log.info("[内存缓存customer统计完成] 用户{}, 共更新{}条customer记录", currentUserId, totalUpdated);

        } catch (Exception e) {
            log.error("[内存缓存customer统计] 用户{}统计失败: {}", currentUserId, e.getMessage(), e);
            throw new RuntimeException("内存缓存customer数据统计失败", e);
        }
    }

    /**
     * 优化版本的中奖金额计算（使用缓存）
     */
    private BigDecimal calculateWinningAmountOptimized(GameRecord gameRecord, Long methodId, Long currentUserId) {
        try {
            // 从缓存获取赔率，避免数据库查询
            BigDecimal odds = oddsCacheManager.getOdds(currentUserId, methodId);
            if (odds == null) {
                log.error("未找到用户 {} 的玩法 {} 赔率配置", currentUserId, methodId);
                return BigDecimal.ZERO;
            }

            // 获取中奖号码
            String winningNumbers = gameRecord.getWinningNumbers();
            if (StringUtils.isEmpty(winningNumbers)) {
                return BigDecimal.ZERO;
            }

            // 解析中奖号码
            JSONObject winningJson = JSON.parseObject(winningNumbers);
            JSONArray winningArray = winningJson.getJSONArray("winning");
            if (winningArray == null || winningArray.isEmpty()) {
                return BigDecimal.ZERO;
            }

            // 计算中奖组数
            int winningGroups = winningArray.size();

            // 获取投注金额
            BigDecimal betAmount = gameRecord.getMoney();

            // 计算中奖金额
            BigDecimal winAmount = betAmount.multiply(odds).multiply(new BigDecimal(winningGroups));

            return winAmount;
        } catch (Exception e) {
            log.error("计算中奖金额时发生错误: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }
}
