{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetDialog.vue", "mtime": 1756432531741}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750942928175}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBoYW5kbGVOdW1iZXJSZWNvZ25pdGlvbiwgaGFuZGxlU21hcnRSZWNvZ25pdGlvbiB9IGZyb20gJy4vbnVtYmVyUmVjb2duaXRpb24uanMnOw0KaW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0JzsNCmltcG9ydCB7IGdldE1heFNlcmlhbE51bWJlciB9IGZyb20gJ0AvYXBpL2dhbWUvc2VyaWFsJw0KaW1wb3J0IHsgZ2VuZXJhdGVTZXJpYWxOdW1iZXIgfSBmcm9tICdAL2FwaS9nYW1lL3JlY29yZCcNCmltcG9ydCB7IGdldEN1cnJlbnRRaWhhbyB9IGZyb20gJ0AvYXBpL2dhbWUvcWloYW8nDQppbXBvcnQgeyBjaGVja0FuZFNldERlZmF1bHRQbGF5ZXIgfSBmcm9tICdAL2FwaS9nYW1lL2N1c3RvbWVyJw0KaW1wb3J0IHsgZ2V0U3RhdGlzdGljc1Blcm1pc3Npb24gfSBmcm9tICdAL2FwaS9zeXN0ZW0vdXNlcicNCmltcG9ydCBCZXRTdGF0aXN0aWNzIGZyb20gJy4vQmV0U3RhdGlzdGljcy52dWUnDQppbXBvcnQgQmV0Rm9ybWF0RGlhbG9nIGZyb20gJy4vQmV0Rm9ybWF0RGlhbG9nLnZ1ZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnQmV0RGlhbG9nJywNCiAgY29tcG9uZW50czogew0KICAgIEJldFN0YXRpc3RpY3MsDQogICAgQmV0Rm9ybWF0RGlhbG9nDQogIH0sDQogIHByb3BzOiB7DQogICAgdmlzaWJsZTogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICB0aXRsZTogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJ+aWsOWinuS4i+azqCcNCiAgICB9LA0KICAgIHJvdzogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogbnVsbA0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBkaWFsb2dUaXRsZTogJycsDQogICAgICB0b3RhbEFtb3VudDogMCwNCiAgICAgIHRvdGFsQmV0czogMCwNCiAgICAgIHBlbmRpbmdSZXF1ZXN0czogW10sDQogICAgICBnYW1lTWV0aG9kc0RhdGE6IFtdLCAvLyDnjqnms5XmlbDmja4NCiAgICAgIGZpbHRlcmVkR2FtZU1ldGhvZHM6IFtdLCAvLyDnlKjkuo4gcmVtb3RlIOaQnOe0oueahOeOqeazleaVsOaNrg0KICAgICAgbG9hZGluZ0dhbWVNZXRob2RzOiBmYWxzZSwgLy8g546p5rOV5Yqg6L2954q25oCBDQogICAgICBmb3JtRGF0YTogew0KICAgICAgICBmYzNkSXNzdWVOdW1iZXI6ICcnLCAgLy8g56aP5b2pM0TmnJ/lj7cNCiAgICAgICAgdGNJc3N1ZU51bWJlcjogJycsICAgIC8vIOS9k+W9qeaOkuS4ieacn+WPtw0KICAgICAgICBzaGliaWU6ICcnLA0KICAgICAgICBiZXRHcm91cHM6IFt7DQogICAgICAgICAgbWV0aG9kSWQ6IHVuZGVmaW5lZCwNCiAgICAgICAgICBtb25leTogJycsDQogICAgICAgICAgYmV0TnVtYmVyczogJycsDQogICAgICAgICAgZGFuc2h1YW5nOiBudWxsLA0KICAgICAgICAgIGRheGlhbzogbnVsbCwNCiAgICAgICAgICBkaW5nd2VpOiBudWxsLA0KICAgICAgICAgIGhlemhpOiBudWxsLA0KICAgICAgICAgIGxvdHRlcnlJZDogMQ0KICAgICAgICB9XQ0KICAgICAgfSwNCiAgICAgIC8vIOaWsOWinu+8muWPt+eggeaVsOmHj+mZkOWItuebuOWFsw0KICAgICAgbnVtYmVyQ291bnQ6IDAsDQogICAgICBpc051bWJlckNvdW50RXhjZWVkZWQ6IGZhbHNlLA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgYmV0R3JvdXBzOiB7DQogICAgICAgICAgbWV0aG9kSWQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup546p5rOVJywgdHJpZ2dlcjogJ2NoYW5nZScgfV0sDQogICAgICAgICAgbW9uZXk6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl6YeR6aKdJywgdHJpZ2dlcjogJ2JsdXInIH1dDQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICBsb2NhbEZvcm06IHsNCiAgICAgICAgZmMzZElzc3VlTnVtYmVyOiAnJywgIC8vIOemj+W9qTNE5pyf5Y+3DQogICAgICAgIHRjSXNzdWVOdW1iZXI6ICcnLCAgICAvLyDkvZPlvanmjpLkuInmnJ/lj7cNCiAgICAgICAgc2hpYmllOiAnJywNCiAgICAgICAgYmV0R3JvdXBzOiBbXQ0KICAgICAgfSwNCiAgICAgIGN1cnJlbnRVc2VyOiB7IG5hbWU6ICcnIH0sDQogICAgICB1c2VyTGlzdDogW10sDQogICAgICBzZWxlY3RlZFVzZXJJZDogbnVsbCwNCiAgICAgIGZ1dGlTd2l0Y2g6IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ2Z1dGlTd2l0Y2gnKSB8fCAnYWxsJywNCiAgICAgIGZvcm1hdERpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgZm9ybWF0SW5wdXQ6ICcnLA0KICAgICAgZm9ybWF0T3V0cHV0OiAnJywNCiAgICAgIGZvcm1hdFRhYjogJ3VuaWZpZWQnLA0KICAgICAgY29sb25Gb3JtYXRJbnB1dDogJycsDQogICAgICBjb2xvbkZvcm1hdE91dHB1dDogJycsDQogICAgICBtb25leUZvcm1hdElucHV0OiAnJywNCiAgICAgIG1vbmV5Rm9ybWF0T3V0cHV0OiAnJywNCg0KICAgICAgdW5kZXJzY29yZUZvcm1hdElucHV0OiAnJywNCiAgICAgIHVuZGVyc2NvcmVGb3JtYXRPdXRwdXQ6ICcnLA0KICAgICAgdW5pZmllZEZvcm1hdElucHV0OiAnJywNCiAgICAgIHVuaWZpZWRGb3JtYXRPdXRwdXQ6ICcnLA0KICAgICAgY2hhaW5Gb3JtYXRJbnB1dDogJycsDQogICAgICBjaGFpbkZvcm1hdE91dHB1dDogJycsDQogICAgICBhbW91bnRGb3JtYXRJbnB1dDogJycsDQogICAgICBhbW91bnRGb3JtYXRPdXRwdXQ6ICcnLA0KICAgICAgcmVtb3ZlRm9ybWF0SW5wdXQ6ICcnLA0KICAgICAgcmVtb3ZlRm9ybWF0T3V0cHV0OiAnJywNCiAgICAgIHNlcmlhbE51bWJlcjogMSwgLy8g5paw5aKe5pys57uE5bqP5Y+3DQogICAgICBtYXhTZXJpYWxOdW1iZXI6IDEsIC8vIOaWsOWinuacgOWkp+a1geawtOWPtw0KICAgICAgZ2VuZXJhdGVMb2FkaW5nOiBmYWxzZSwgLy8g55Sf5oiQ5rWB5rC05Y+35Yqg6L2954q25oCBDQogICAgICBvcmlnaW5hbFVzZXJJZDogbnVsbCwgLy8g5Y6f5aeL55So5oi3SUQNCiAgICAgIG9yaWdpbmFsU2VyaWFsTnVtYmVyOiBudWxsLCAvLyDljp/lp4vmtYHmsLTlj7cNCiAgICAgIHN0YXRpc3RpY3NWaXNpYmxlOiBmYWxzZSwgLy8g57uf6K6h5a+56K+d5qGG5pi+56S654q25oCBDQogICAgICBoYXNTdGF0aXN0aWNzUGVybWlzc2lvbjogZmFsc2UgLy8g5piv5ZCm5pyJ57uf6K6h5p2D6ZmQDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC8qKiDmmK/lkKbmmL7npLrnu5/orqHmjInpkq4gKi8NCiAgICBzaG93U3RhdGlzdGljc0J1dHRvbigpIHsNCiAgICAgIHJldHVybiB0aGlzLmhhc1N0YXRpc3RpY3NQZXJtaXNzaW9uOw0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICB2aXNpYmxlKHZhbCkgew0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdmFsOw0KICAgICAgaWYgKHZhbCkgew0KICAgICAgICAvLyDmmL7npLrlr7nor53moYbml7blhYjmo4Dmn6XnjqnlrrbnirbmgIENCiAgICAgICAgdGhpcy5jaGVja1BsYXllclN0YXR1cygpOw0KICAgICAgICB0aGlzLmdldEFsbFVzZXJzKCk7DQogICAgICAgIC8vIOajgOafpee7n+iuoeadg+mZkA0KICAgICAgICB0aGlzLmNoZWNrU3RhdGlzdGljc1Blcm1pc3Npb24oKTsNCiAgICAgICAgLy8g5paw5aKe77ya5Yy65YiG5paw5aKe5ZKM5L+u5pS577yM6K6+572uIHNlbGVjdGVkVXNlcklkDQogICAgICAgIGlmICh0aGlzLnJvdyAmJiB0aGlzLnJvdy51c2VySWQpIHsNCiAgICAgICAgICAvLyDkv67mlLnml7YNCiAgICAgICAgICB0aGlzLnNlbGVjdGVkVXNlcklkID0gdGhpcy5yb3cudXNlcklkOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOaWsOWinuaXtu+8jOWFiOajgOafpXNlc3Npb27kuK3mmK/lkKbmnIlVc2VyX0lkDQogICAgICAgICAgbGV0IHVzZXJJZCA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ1VzZXJfSWQnKTsNCiAgICAgICAgICBpZiAodXNlcklkKSB7DQogICAgICAgICAgICB0aGlzLnNlbGVjdGVkVXNlcklkID0gTnVtYmVyKHVzZXJJZCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOWmguaenOayoeacie+8jOetieW+hWluaXRpYWxpemVGaXJzdFVzZXIoKeiuvue9rg0KICAgICAgICAgICAgdGhpcy5zZWxlY3RlZFVzZXJJZCA9IG51bGw7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIC8vIOWFiOWIneWni+WMlua1geawtOWPt++8jOWGjeiuvue9ruihqOWNleaVsOaNrg0KICAgICAgDQogICAgICAgIHRoaXMuaW5pdFNlcmlhbE51bWJlcigpOyAvLyDlvLnnqpfmiZPlvIDml7bliJ3lp4vljJbmnKznu4Tluo/lj7cNCiAgICAgICAgLy8g5aaC5p6c5piv5L+u5pS55pON5L2c77yM5Yid5aeL5YyW6KGo5Y2V5pWw5o2uDQogICAgICAgIGlmICh0aGlzLnJvdykgew0KICAgICAgIA0KICAgICAgICAgIHRoaXMuaW5pdEZvcm1EYXRhKCk7DQogICAgICAgIH0NCiAgICAgICAgLy8g5omT5byA5by556qX5pe25qC55o2u5byA5YWz54q25oCB5aSE55CGDQogICAgICAgIHRoaXMub25GdXRpU3dpdGNoQ2hhbmdlKHRoaXMuZnV0aVN3aXRjaCk7DQogICAgICB9DQogICAgfSwNCiAgICBkaWFsb2dWaXNpYmxlKHZhbCkgew0KICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnZpc2libGUnLCB2YWwpOw0KICAgICAgaWYgKCF2YWwpIHsNCiAgICAgICAgdGhpcy5yZXNldEZvcm0oKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOebkeWQrOeUqOaIt0lE5Y+Y5YyW77yM6Ieq5Yqo5aSE55CG5rWB5rC05Y+3DQogICAgc2VsZWN0ZWRVc2VySWQ6IHsNCiAgICAgIGhhbmRsZXIobmV3VXNlcklkLCBvbGRVc2VySWQpIHsNCiAgICAgICAgLy8g5Y+q5Zyo5L+u5pS55qih5byP5LiL5aSE55CG77yM5LiU56Gu5L+d5pyJ5Y6f5aeL5pWw5o2uDQogICAgICAgIGlmICh0aGlzLnJvdyAmJiB0aGlzLnJvdy5iZXRJZCAmJiB0aGlzLm9yaWdpbmFsVXNlcklkICE9PSBudWxsICYmIG9sZFVzZXJJZCAhPT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgdGhpcy5oYW5kbGVVc2VySWRDaGFuZ2UobmV3VXNlcklkLCBvbGRVc2VySWQpOw0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgaW1tZWRpYXRlOiBmYWxzZQ0KICAgIH0sDQogICAgZm9ybWF0RGlhbG9nVmlzaWJsZSh2YWwpIHsNCiAgICAgIGlmICghdmFsKSB7DQogICAgICAgIC8vIOW8ueeql+WFs+mXreaXtua4heepuuaJgOacieagvOW8j+WGheWuuQ0KICAgICAgICB0aGlzLmZvcm1hdElucHV0ID0gJyc7DQogICAgICAgIHRoaXMuZm9ybWF0T3V0cHV0ID0gJyc7DQogICAgICAgIHRoaXMuY29sb25Gb3JtYXRJbnB1dCA9ICcnOw0KICAgICAgICB0aGlzLmNvbG9uRm9ybWF0T3V0cHV0ID0gJyc7DQogICAgICAgIHRoaXMubW9uZXlGb3JtYXRJbnB1dCA9ICcnOw0KICAgICAgICB0aGlzLm1vbmV5Rm9ybWF0T3V0cHV0ID0gJyc7DQoNCiAgICAgICAgdGhpcy51bmRlcnNjb3JlRm9ybWF0SW5wdXQgPSAnJzsNCiAgICAgICAgdGhpcy51bmRlcnNjb3JlRm9ybWF0T3V0cHV0ID0gJyc7DQogICAgICAgIHRoaXMudW5pZmllZEZvcm1hdElucHV0ID0gJyc7DQogICAgICAgIHRoaXMudW5pZmllZEZvcm1hdE91dHB1dCA9ICcnOw0KICAgICAgICB0aGlzLmNoYWluRm9ybWF0SW5wdXQgPSAnJzsNCiAgICAgICAgdGhpcy5jaGFpbkZvcm1hdE91dHB1dCA9ICcnOw0KICAgICAgICB0aGlzLmFtb3VudEZvcm1hdElucHV0ID0gJyc7DQogICAgICAgIHRoaXMuYW1vdW50Rm9ybWF0T3V0cHV0ID0gJyc7DQogICAgICAgIHRoaXMucmVtb3ZlRm9ybWF0SW5wdXQgPSAnJzsNCiAgICAgICAgdGhpcy5yZW1vdmVGb3JtYXRPdXRwdXQgPSAnJzsNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgLy8g5Yid5aeL5YyW5pe26I635Y+W546p5rOV5YiX6KGoDQogICAgdGhpcy5nZXRHYW1lTWV0aG9kcygpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy8g5pi+56S6546p5rOV5qC85byP5by556qXDQogICAgc2hvd0JldEZvcm1hdERpYWxvZygpIHsNCiAgICAgIHRoaXMuJHJlZnMuYmV0Rm9ybWF0RGlhbG9nLnNob3coKTsNCiAgICB9LA0KICAgIC8vIOajgOafpeeOqeWutueKtuaAgQ0KICAgIGFzeW5jIGNoZWNrUGxheWVyU3RhdHVzKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjaGVja0FuZFNldERlZmF1bHRQbGF5ZXIoKTsNCg0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gNTAwICYmIHJlc3BvbnNlLm5lZWRDcmVhdGVQbGF5ZXIpIHsNCiAgICAgICAgICAvLyDmsqHmnInnjqnlrrbvvIzmmL7npLrmj5DnpLrlubbot7PovawNCiAgICAgICAgICB0aGlzLiRjb25maXJtKHJlc3BvbnNlLm1zZyArICcg5piv5ZCm56uL5Y2z5YmN5b6A5Yib5bu677yfJywgJ+aPkOekuicsIHsNCiAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn5YmN5b6A5Yib5bu6JywNCiAgICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLA0KICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnDQogICAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgICAvLyDlhbPpl63lvZPliY3lr7nor53moYYNCiAgICAgICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnZpc2libGUnLCBmYWxzZSk7DQogICAgICAgICAgICAvLyDot7PovazliLDnjqnlrrbpobXpnaINCiAgICAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCcvZ2FtZS9jdXN0b21lcicpOw0KICAgICAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICAgIC8vIOeUqOaIt+WPlua2iO+8jOWFs+mXreWvueivneahhg0KICAgICAgICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6dmlzaWJsZScsIGZhbHNlKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCg0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwICYmIHJlc3BvbnNlLmhhc1BsYXllcnMpIHsNCiAgICAgICAgICAvLyDmnInnjqnlrrbvvIznu6fnu63liJ3lp4vljJYNCiAgICAgICAgICANCiAgICAgICAgICBpZiAocmVzcG9uc2UuZGVmYXVsdFVzZXJJZCkgew0KICAgICAgICAgICAgDQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g57un57ut5Yid5aeL5YyW5a+56K+d5qGGDQogICAgICAgICAgdGhpcy5pbml0aWFsaXplRGlhbG9nKCk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ajgOafpeeOqeWutueKtuaAgeWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ajgOafpeeOqeWutueKtuaAgeWksei0pe+8jOivt+mHjeivlScpOw0KICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnZpc2libGUnLCBmYWxzZSk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWIneWni+WMluWvueivneahhg0KICAgIGluaXRpYWxpemVEaWFsb2coKSB7DQogICAgICAvLyDmmL7npLrlr7nor53moYbml7bojrflj5bmnJ/lj7flkoznjqnms5XliJfooagNCiAgICAgIHRoaXMuZ2V0Q3VycmVudElzc3VlTnVtYmVyKCk7DQogICAgICB0aGlzLmdldEdhbWVNZXRob2RzKCk7DQogICAgICB0aGlzLmdldEN1cnJlbnRVc2VyKCk7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPlueOqeazleWIl+ihqA0KICAgIGdldEdhbWVNZXRob2RzKCkgew0KICAgICAgdGhpcy5sb2FkaW5nR2FtZU1ldGhvZHMgPSB0cnVlOw0KICAgICAgcmV0dXJuIHJlcXVlc3Qoew0KICAgICAgICB1cmw6ICcvZ2FtZS9tZXRob2QvbGlzdCcsDQogICAgICAgIG1ldGhvZDogJ2dldCcsDQogICAgICAgIHBhcmFtczogew0KICAgICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgICAgcGFnZVNpemU6IDEwMDAgIC8vIOiuvue9rui2s+Wkn+Wkp+eahOmhtemdouWkp+Wwj+S7peiOt+WPluaJgOacieaVsOaNrg0KICAgICAgICB9DQogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgbGV0IGRhdGEgPSBbXTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7DQogICAgICAgICAgICBkYXRhID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgICB9IGVsc2UgaWYgKHJlc3BvbnNlLnJvd3MgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5yb3dzKSkgew0KICAgICAgICAgICAgZGF0YSA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgfSBlbHNlIGlmICh0eXBlb2YgcmVzcG9uc2UuZGF0YSA9PT0gJ3N0cmluZycpIHsNCiAgICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICAgIGNvbnN0IHBhcnNlZERhdGEgPSBKU09OLnBhcnNlKHJlc3BvbnNlLmRhdGEpOw0KICAgICAgICAgICAgICBkYXRhID0gQXJyYXkuaXNBcnJheShwYXJzZWREYXRhKSA/IHBhcnNlZERhdGEgOiBbXTsNCiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICAgICAgZGF0YSA9IFtdOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmdhbWVNZXRob2RzRGF0YSA9IGRhdGE7DQogICAgICAgIHRoaXMuZmlsdGVyZWRHYW1lTWV0aG9kcyA9IGRhdGE7DQogICAgICAgIHRoaXMubG9hZGluZ0dhbWVNZXRob2RzID0gZmFsc2U7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuZ2FtZU1ldGhvZHNEYXRhID0gW107DQogICAgICAgIHRoaXMuZmlsdGVyZWRHYW1lTWV0aG9kcyA9IFtdOw0KICAgICAgICB0aGlzLmxvYWRpbmdHYW1lTWV0aG9kcyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDmiZPlvIDlr7nor53moYYNCiAgICBzaG93KHRpdGxlID0gJ+aWsOWinuS4i+azqCcpIHsNCiAgICAgIHRoaXMuZGlhbG9nVGl0bGUgPSB0aXRsZTsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCiAgICAvLyDlhbPpl63lr7nor53moYYNCiAgICBjbG9zZSgpIHsNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgdGhpcy4kZW1pdCgndXBkYXRlOnZpc2libGUnLCBmYWxzZSk7DQogICAgICB0aGlzLiRlbWl0KCdjYW5jZWwnKTsgIC8vIOS/neaMgeWQkeWQjuWFvOWuuQ0KICAgIH0sDQogICAgLy8g6YeN572u6KGo5Y2VDQogICAgcmVzZXRGb3JtKCkgew0KICAgICAgdGhpcy5mb3JtRGF0YSA9IHsNCiAgICAgICAgZmMzZElzc3VlTnVtYmVyOiAnJywNCiAgICAgICAgdGNJc3N1ZU51bWJlcjogJycsDQogICAgICAgIHNoaWJpZTogJycsDQogICAgICAgIGJldEdyb3VwczogW3sNCiAgICAgICAgICBtZXRob2RJZDogdW5kZWZpbmVkLA0KICAgICAgICAgIG1vbmV5OiAnJywNCiAgICAgICAgICBiZXROdW1iZXJzOiAnJywNCiAgICAgICAgICBkYW5zaHVhbmc6IG51bGwsDQogICAgICAgICAgZGF4aWFvOiBudWxsLA0KICAgICAgICAgIGRpbmd3ZWk6IG51bGwsDQogICAgICAgICAgaGV6aGk6IG51bGwsDQogICAgICAgICAgbG90dGVyeUlkOiAxDQogICAgICAgIH1dDQogICAgICB9Ow0KICAgICAgdGhpcy50b3RhbEFtb3VudCA9IDA7DQogICAgICB0aGlzLnRvdGFsQmV0cyA9IDA7DQogICAgICAvLyDph43nva7lj7fnoIHmlbDph4/nirbmgIENCiAgICAgIHRoaXMubnVtYmVyQ291bnQgPSAwOw0KICAgICAgdGhpcy5pc051bWJlckNvdW50RXhjZWVkZWQgPSBmYWxzZTsNCiAgICB9LA0KICAgIGFkZEJldEdyb3VwKCkgew0KICAgICAgY29uc3QgbmV3R3JvdXAgPSB7DQogICAgICAgIG1ldGhvZElkOiB1bmRlZmluZWQsDQogICAgICAgIG1vbmV5OiAnJywNCiAgICAgICAgYmV0TnVtYmVyczogJycsDQogICAgICAgIGRhbnNodWFuZzogbnVsbCwNCiAgICAgICAgZGF4aWFvOiBudWxsLA0KICAgICAgICBkaW5nd2VpOiBudWxsLA0KICAgICAgICBoZXpoaTogbnVsbCwNCiAgICAgICAgbG90dGVyeUlkOiB0aGlzLmZ1dGlTd2l0Y2ggPT09ICd0YycgPyAyIDogMQ0KICAgICAgfTsNCg0KICAgICAgLy8g55u05o6l5pON5L2cZm9ybURhdGENCiAgICAgIGlmICghdGhpcy5mb3JtRGF0YS5iZXRHcm91cHMpIHsNCiAgICAgICAgdGhpcy5mb3JtRGF0YS5iZXRHcm91cHMgPSBbXTsNCiAgICAgIH0NCiAgICAgIHRoaXMuZm9ybURhdGEuYmV0R3JvdXBzLnB1c2goeyAuLi5uZXdHcm91cCB9KTsNCg0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLnVwZGF0ZVRvdGFscygpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICByZW1vdmVCZXRHcm91cChpZHgpIHsNCiAgICAgIGlmICh0aGlzLmZvcm1EYXRhLmJldEdyb3Vwcy5sZW5ndGggPD0gMSkgew0KICAgICAgICAvLyDlpoLmnpzlj6rliankuIDkuKrmipXms6jnu4TvvIzliJnmuIXnqbrlroPogIzkuI3mmK/liKDpmaQNCiAgICAgICAgdGhpcy5mb3JtRGF0YS5iZXRHcm91cHMgPSBbew0KICAgICAgICAgIG1ldGhvZElkOiB1bmRlZmluZWQsDQogICAgICAgICAgbW9uZXk6ICcnLA0KICAgICAgICAgIGJldE51bWJlcnM6ICcnLA0KICAgICAgICAgIGRhbnNodWFuZzogbnVsbCwNCiAgICAgICAgICBkYXhpYW86IG51bGwsDQogICAgICAgICAgZGluZ3dlaTogbnVsbCwNCiAgICAgICAgICBoZXpoaTogbnVsbCwNCiAgICAgICAgICBsb3R0ZXJ5SWQ6IDENCiAgICAgICAgfV07DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlpoLmnpzmnInlpJrkuKrmipXms6jnu4TvvIzliJnliKDpmaTmjIflrprnmoTnu4QNCiAgICAgICAgdGhpcy5mb3JtRGF0YS5iZXRHcm91cHMuc3BsaWNlKGlkeCwgMSk7DQogICAgICB9DQoNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy51cGRhdGVUb3RhbHMoKTsNCiAgICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsgLy8g5by65Yi25pu05paw6KeG5Zu+DQogICAgICB9KTsNCiAgICB9LA0KICAgIHVwZGF0ZVRvdGFscygpIHsNCiAgICAgIHRoaXMudG90YWxBbW91bnQgPSB0aGlzLmZvcm1EYXRhLmJldEdyb3Vwcy5yZWR1Y2UoKHN1bSwgZykgPT4gc3VtICsgTnVtYmVyKHRoaXMuZ2V0R3JvdXBBbW91bnQoZykpLCAwKTsNCiAgICAgIHRoaXMudG90YWxCZXRzID0gdGhpcy5mb3JtRGF0YS5iZXRHcm91cHMucmVkdWNlKChzdW0sIGcpID0+IHN1bSArIE51bWJlcih0aGlzLmdldEdyb3VwQmV0cyhnKSksIDApOw0KICAgIH0sDQogICAgZ2V0SGV6aGlPcHRpb25zKG1ldGhvZElkKSB7DQogICAgICBjb25zdCBtYXAgPSB7DQogICAgICAgIDIxOiBbNywgMjBdLA0KICAgICAgICAyMjogWzgsIDE5XSwNCiAgICAgICAgMjM6IFs5LCAxOF0sDQogICAgICAgIDI0OiBbMTAsIDE3XSwNCiAgICAgICAgMjU6IFsxMSwgMTZdLA0KICAgICAgICAyNjogWzEyLCAxNV0sDQogICAgICAgIDI3OiBbMTMsIDE0XSwNCiAgICAgICAgMzc6IFswLCAyN10sDQogICAgICAgIDM4OiBbMSwgMjZdLA0KICAgICAgICAzOTogWzIsIDI1XSwNCiAgICAgICAgNDA6IFszLCAyNF0sDQogICAgICAgIDQxOiBbNCwgMjNdLA0KICAgICAgICA0MjogWzUsIDIyXSwNCiAgICAgICAgNDM6IFs2LCAyMV0NCiAgICAgIH07DQogICAgICByZXR1cm4gbWFwW21ldGhvZElkXSB8fCBbXTsNCiAgICB9LA0KICAgIG9uTWV0aG9kQ2hhbmdlKGdyb3VwKSB7DQogICAgICANCiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5mb3JtRGF0YS5iZXRHcm91cHMuZmluZEluZGV4KGcgPT4gZyA9PT0gZ3JvdXApOw0KICAgICAgaWYgKGluZGV4ID4gLTEpIHsNCiAgICAgICAgLy8g5Yib5bu65LiA5Liq5paw55qE57uE5a+56LGh77yM56Gu5L+d5YyF5ZCr5omA5pyJ5b+F6KaB55qE5a2X5q61DQogICAgICAgIGNvbnN0IHVwZGF0ZWRHcm91cCA9IHsNCiAgICAgICAgICAuLi5ncm91cCwNCiAgICAgICAgICBkYW5zaHVhbmc6IG51bGwsDQogICAgICAgICAgZGF4aWFvOiBudWxsLA0KICAgICAgICAgIGRpbmd3ZWk6IG51bGwsDQogICAgICAgICAgaGV6aGk6IG51bGwNCiAgICAgICAgICAvLyDkuI3ph43nva4gYmV0TnVtYmVycyDlkowgbW9uZXkNCiAgICAgICAgfTsNCg0KICAgICAgICAvLyDmoLnmja7njqnms5Xnsbvlnovorr7nva7pu5jorqTlgLwNCiAgICAgICAgaWYgKHVwZGF0ZWRHcm91cC5tZXRob2RJZCA9PT0gMjkpIHsNCiAgICAgICAgICB1cGRhdGVkR3JvdXAuZGFuc2h1YW5nID0gMjAwOw0KICAgICAgICB9IGVsc2UgaWYgKHVwZGF0ZWRHcm91cC5tZXRob2RJZCA9PT0gMzApIHsNCiAgICAgICAgICB1cGRhdGVkR3JvdXAuZGF4aWFvID0gMzAwOw0KICAgICAgICB9IGVsc2UgaWYgKHVwZGF0ZWRHcm91cC5tZXRob2RJZCA9PT0gMikgew0KICAgICAgICAgIHVwZGF0ZWRHcm91cC5kaW5nd2VpID0gMTAwOw0KICAgICAgICB9IGVsc2UgaWYgKFsyMSwgMjIsIDIzLCAyNCwgMjUsIDI2LCAyNywgMzcsIDM4LCAzOSwgNDAsIDQxLCA0MiwgNDNdLmluY2x1ZGVzKHVwZGF0ZWRHcm91cC5tZXRob2RJZCkpIHsNCiAgICAgICAgICBjb25zdCBvcHRpb25zID0gdGhpcy5nZXRIZXpoaU9wdGlvbnModXBkYXRlZEdyb3VwLm1ldGhvZElkKTsNCiAgICAgICAgICBpZiAob3B0aW9ucyAmJiBvcHRpb25zLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIHVwZGF0ZWRHcm91cC5oZXpoaSA9IG9wdGlvbnNbMF07DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5pu05paw57uE5pWw5o2uDQogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm1EYXRhLmJldEdyb3VwcywgaW5kZXgsIHVwZGF0ZWRHcm91cCk7DQogICAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCk7DQogICAgICB9DQogICAgfSwNCiAgICAvLyDojrflj5blvZPliY3mnJ/lj7cNCiAgICBnZXRDdXJyZW50SXNzdWVOdW1iZXIoKSB7DQogICAgICBjb25zdCBsb2FkaW5nSW5zdGFuY2UgPSB0aGlzLiRsb2FkaW5nKHsNCiAgICAgICAgbG9jazogdHJ1ZSwNCiAgICAgICAgdGV4dDogJ+ato+WcqOiOt+WPluacn+WPty4uLicsDQogICAgICAgIHNwaW5uZXI6ICdlbC1pY29uLWxvYWRpbmcnLA0KICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgwLCAwLCAwLCAwLjcpJywNCiAgICAgICAgY3VzdG9tQ2xhc3M6ICdiZXQtZGlhbG9nLWxvYWRpbmcnDQogICAgICB9KTsNCg0KICAgICAgLy8g5L2/55SoUHJvbWlzZS5hbGzlkIzml7bojrflj5bkuKTkuKrmnJ/lj7cNCiAgICAgIFByb21pc2UuYWxsKFsNCiAgICAgICAgLy8g6I635Y+W56aP5b2pM0TmnJ/lj7cNCiAgICAgICAgZ2V0Q3VycmVudFFpaGFvKDEpLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnpo/lvakzROacn+WPt+Wksei0pTonLCBlcnJvcik7DQogICAgICAgICAgcmV0dXJuIHsgY29kZTogNTAwLCBtc2c6ICfojrflj5blpLHotKUnIH07DQogICAgICAgIH0pLA0KICAgICAgICAvLyDojrflj5bkvZPlvanmjpLkuInmnJ/lj7cNCiAgICAgICAgZ2V0Q3VycmVudFFpaGFvKDIpLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bkvZPlvanmjpLkuInmnJ/lj7flpLHotKU6JywgZXJyb3IpOw0KICAgICAgICAgIHJldHVybiB7IGNvZGU6IDUwMCwgbXNnOiAn6I635Y+W5aSx6LSlJyB9Ow0KICAgICAgICB9KQ0KICAgICAgXSkudGhlbigoW2ZjM2RSZXNwb25zZSwgdGNSZXNwb25zZV0pID0+IHsNCiAgICAgICAgLy8g5aSE55CG56aP5b2pM0TmnJ/lj7cNCiAgICAgICAgaWYgKGZjM2RSZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmZvcm1EYXRhLmZjM2RJc3N1ZU51bWJlciA9IGZjM2RSZXNwb25zZS5tc2c7DQogICAgICAgICAgDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W56aP5b2pM0TmnJ/lj7flpLHotKU6JywgZmMzZFJlc3BvbnNlKTsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bnpo/lvakzROacn+WPt+Wksei0pScpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5aSE55CG5L2T5b2p5o6S5LiJ5pyf5Y+3DQogICAgICAgIGlmICh0Y1Jlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuZm9ybURhdGEudGNJc3N1ZU51bWJlciA9IHRjUmVzcG9uc2UubXNnOw0KICAgICAgICAgDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5L2T5b2p5o6S5LiJ5pyf5Y+35aSx6LSlOicsIHRjUmVzcG9uc2UpOw0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluS9k+W9qeaOkuS4ieacn+WPt+Wksei0pScpOw0KICAgICAgICB9DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgbG9hZGluZ0luc3RhbmNlLmNsb3NlKCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOaWsOWinu+8muWKqOaAgeiuoeeul+WPt+eggeaVsOmHj+eahOaWueazlQ0KICAgIGNhbGN1bGF0ZU51bWJlckNvdW50KGlucHV0KSB7DQogICAgICBpZiAoIWlucHV0IHx8ICFpbnB1dC50cmltKCkpIHJldHVybiAwOw0KDQogICAgICAvLyDmuIXnkIbovpPlhaXvvIzljrvpmaTpppblsL7nqbrnmb0NCiAgICAgIGNvbnN0IGNsZWFuSW5wdXQgPSBpbnB1dC50cmltKCk7DQoNCiAgICAgIC8vIOS9v+eUqOabtOeyvuehrueahOato+WImeWMuemFjeWPt+eggee7hA0KICAgICAgLy8g5Yy56YWNMi055L2N6L+e57ut5pWw5a2X77yM6ICD6JmR5ZCE56eN5YiG6ZqU56ymDQogICAgICBjb25zdCBudW1iZXJQYXR0ZXJuID0gL1xkezIsOX0vZzsNCiAgICAgIGNvbnN0IGFsbE1hdGNoZXMgPSBjbGVhbklucHV0Lm1hdGNoKG51bWJlclBhdHRlcm4pOw0KDQogICAgICBpZiAoIWFsbE1hdGNoZXMpIHJldHVybiAwOw0KDQogICAgICAvLyDmjInooYzliIbmnpDvvIzmm7Tlh4bnoa7lnLDor4bliKvlj7fnoIHlkozph5Hpop0NCiAgICAgIGNvbnN0IGxpbmVzID0gY2xlYW5JbnB1dC5zcGxpdCgvW1xyXG5dKy8pLmZpbHRlcihsaW5lID0+IGxpbmUudHJpbSgpKTsNCiAgICAgIGxldCB0b3RhbENvdW50ID0gMDsNCg0KICAgICAgZm9yIChjb25zdCBsaW5lIG9mIGxpbmVzKSB7DQogICAgICAgIGNvbnN0IHRyaW1tZWRMaW5lID0gbGluZS50cmltKCk7DQogICAgICAgIGlmICghdHJpbW1lZExpbmUpIGNvbnRpbnVlOw0KDQogICAgICAgIGNvbnN0IGxpbmVOdW1iZXJzID0gdHJpbW1lZExpbmUubWF0Y2gobnVtYmVyUGF0dGVybik7DQogICAgICAgIGlmICghbGluZU51bWJlcnMpIGNvbnRpbnVlOw0KDQogICAgICAgIC8vIOajgOafpemHkemineebuOWFs+WFs+mUruWtlw0KICAgICAgICBjb25zdCBtb25leUtleXdvcmRzID0gL1vlhYPlnZfnsbPpkrHotbbnu4Tnm7TlkIrmlL7pmLLlkIRdLzsNCiAgICAgICAgY29uc3QgaGFzTW9uZXlLZXl3b3JkID0gbW9uZXlLZXl3b3Jkcy50ZXN0KHRyaW1tZWRMaW5lKTsNCg0KICAgICAgICAvLyDmo4Dmn6XmmK/lkKbmnInmmI7mmL7nmoTph5Hpop3liIbpmpTnrKbvvIjlpoIr44CBL+etie+8iQ0KICAgICAgICBjb25zdCBoYXNNb25leURlbGltaXRlciA9IC9bK++8i1wvXS8udGVzdCh0cmltbWVkTGluZSk7DQoNCiAgICAgICAgaWYgKGhhc01vbmV5S2V5d29yZCB8fCBoYXNNb25leURlbGltaXRlcikgew0KICAgICAgICAgIC8vIOWmguaenOWMheWQq+mHkemineWFs+mUruWtl+aIluWIhumalOespu+8jOacgOWQjjEtMuS4quaVsOWtl+WPr+iDveaYr+mHkeminQ0KICAgICAgICAgIC8vIOabtOS/neWuiOeahOS8sOiuoe+8muWHj+WOuzHkuKrlj6/og73nmoTph5Hpop0NCiAgICAgICAgICB0b3RhbENvdW50ICs9IE1hdGgubWF4KDAsIGxpbmVOdW1iZXJzLmxlbmd0aCAtIDEpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOe6r+WPt+eggeihjO+8jOaJgOacieaVsOWtl+mDveeul+S9nOWPt+eggQ0KICAgICAgICAgIHRvdGFsQ291bnQgKz0gbGluZU51bWJlcnMubGVuZ3RoOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIHJldHVybiB0b3RhbENvdW50Ow0KICAgIH0sDQogICAgLy8g5pm66IO96aKE5aSE55CG5om56YeP5Y+356CBK+mHkeminei+k+WFpe+8jOmBv+WFjemHkemineiiq+ivr+ivhuWIq+S4uuWPt+eggQ0KICAgIHNtYXJ0UHJlcHJvY2Vzc0lucHV0KGlucHV0KSB7DQogICAgICBpZiAoIWlucHV0KSByZXR1cm4gaW5wdXQ7DQogICAgICAvLyDmlK/mjIHlpJrooYzmiJYi4oCUIuetiei2hee6p+WIhumalOespg0KICAgICAgbGV0IHBhcnRzID0gaW5wdXQuc3BsaXQoL1vigJRcLVxuXHJdKy8pLm1hcChzID0+IHMudHJpbSgpKS5maWx0ZXIoQm9vbGVhbik7DQogICAgICBpZiAocGFydHMubGVuZ3RoID4gMSkgew0KICAgICAgICAvLyDph5Hpop3ooajovr7lhbPplK7lrZfmm7Tlrr3mnb7vvIzlhbzlrrnotbYv57uEL+ebtC/lkIov5pS+L+mYsi/lhYMv5ZCE562JDQogICAgICAgIGNvbnN0IG1vbmV5RXhwciA9IC8o6LW2fOe7hHznm7R85ZCKfOaUvnzpmLJ85YWDfOWQhClbXlxkXSpcZCsuKiQvOw0KICAgICAgICBjb25zdCBsYXN0ID0gcGFydHNbcGFydHMubGVuZ3RoIC0gMV07DQogICAgICAgIGlmIChtb25leUV4cHIudGVzdChsYXN0KSkgew0KICAgICAgICAgIC8vIOWQiOW5tuS4uiLlj7fnoIHigJTlj7fnoIHigJQuLi7igJTph5Hpop3ooajovr4iDQogICAgICAgICAgcmV0dXJuIHBhcnRzLnNsaWNlKDAsIC0xKS5qb2luKCfigJQnKSArICfigJQnICsgbGFzdDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGlucHV0Ow0KICAgIH0sDQogICAgLy8g5paw5aKe77ya5qOA5p+l6aaW57uE5Y+356CB5piv5ZCm5Li65LiJ5L2N5pWw55qE5pa55rOVDQogICAgaXNGaXJzdE51bWJlclRocmVlRGlnaXRzKGlucHV0KSB7DQogICAgICBpZiAoIWlucHV0IHx8ICFpbnB1dC50cmltKCkpIHJldHVybiBmYWxzZTsNCg0KICAgICAgLy8g5riF55CG6L6T5YWl77yM5Y676Zmk6aaW5bC+56m655m9DQogICAgICBjb25zdCBjbGVhbklucHV0ID0gaW5wdXQudHJpbSgpOw0KDQogICAgICAvLyDljLnphY3nrKzkuIDkuKoyLTnkvY3ov57nu63mlbDlrZcNCiAgICAgIGNvbnN0IGZpcnN0TnVtYmVyTWF0Y2ggPSBjbGVhbklucHV0Lm1hdGNoKC9cZHsyLDl9Lyk7DQoNCiAgICAgIGlmICghZmlyc3ROdW1iZXJNYXRjaCkgcmV0dXJuIGZhbHNlOw0KDQogICAgICAvLyDmo4Dmn6XnrKzkuIDkuKrlj7fnoIHmmK/lkKbkuLrkuInkvY3mlbANCiAgICAgIHJldHVybiBmaXJzdE51bWJlck1hdGNoWzBdLmxlbmd0aCA9PT0gMzsNCiAgICB9LA0KICAgIGhhbmRsZU51bWJlclJlY29nbml0aW9uKHZhbHVlKSB7DQogICAgICAvLyDlrp7ml7borqHnrpflubbmm7TmlrDlj7fnoIHmlbDph48NCiAgICAgIHRoaXMubnVtYmVyQ291bnQgPSB0aGlzLmNhbGN1bGF0ZU51bWJlckNvdW50KHZhbHVlKTsNCg0KICAgICAgLy8g5Yik5pat5aaC5p6c6aaW57uE5Y+356CB5Li65LiJ5L2N5pWw5YiZ5LiN6ZmQ5Yi25Y+356CB57uE5pWw5Li6MzANCiAgICAgIGNvbnN0IGlzRmlyc3RUaHJlZURpZ2l0cyA9IHRoaXMuaXNGaXJzdE51bWJlclRocmVlRGlnaXRzKHZhbHVlKTsNCiAgICAgIHRoaXMuaXNOdW1iZXJDb3VudEV4Y2VlZGVkID0gaXNGaXJzdFRocmVlRGlnaXRzID8gZmFsc2UgOiB0aGlzLm51bWJlckNvdW50ID4gMzA7DQoNCiAgICAgIGlmICghdmFsdWUpIHsNCiAgICAgICAgdGhpcy5mb3JtRGF0YS5iZXRHcm91cHMgPSBbew0KICAgICAgICAgIG1ldGhvZElkOiB1bmRlZmluZWQsDQogICAgICAgICAgbW9uZXk6ICcnLA0KICAgICAgICAgIGJldE51bWJlcnM6ICcnLA0KICAgICAgICAgIGRhbnNodWFuZzogbnVsbCwNCiAgICAgICAgICBkYXhpYW86IG51bGwsDQogICAgICAgICAgZGluZ3dlaTogbnVsbCwNCiAgICAgICAgICBoZXpoaTogbnVsbCwNCiAgICAgICAgICBsb3R0ZXJ5SWQ6IHRoaXMuZnV0aVN3aXRjaCA9PT0gJ3RjJyA/IDIgOiAxDQogICAgICAgIH1dOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWmguaenOi2hei/hzMw5Liq5Y+356CB77yM5LiN6L+b6KGM6K+G5Yir5aSE55CG77yM5L2G5LuN54S25pi+56S66K6h5pWwDQogICAgICBpZiAodGhpcy5pc051bWJlckNvdW50RXhjZWVkZWQpIHsNCiAgICAgICAgDQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIC8vIDEuIOWFiOaVtOS9k+ivhuWIq++8iOW/hemhu+eUqOWOn+WniyB2YWx1Ze+8jOS4jeiDvemihOWkhOeQhu+8ge+8iQ0KICAgICAgbGV0IHJlc3VsdCA9IGhhbmRsZU51bWJlclJlY29nbml0aW9uKHZhbHVlKTsNCiAgICAgIC8vIDIuIOWmguaenOaVtOS9k+ayoeWRveS4re+8jOWGjeaMieihjOWIhuWJsu+8jOavj+ihjOWNleeLrOivhuWIq++8jOS4jeWBmuS7u+S9leWQiOW5tg0KICAgICAgaWYgKCFyZXN1bHQgfHwgIXJlc3VsdC5ncm91cHMgfHwgIXJlc3VsdC5ncm91cHMuc29tZShnID0+IGcubWV0aG9kSWQpKSB7DQogICAgICAgIGNvbnN0IGxpbmVzID0gdmFsdWUuc3BsaXQoL1tcclxuXSsvKS5tYXAobGluZSA9PiBsaW5lLnRyaW0oKSkuZmlsdGVyKEJvb2xlYW4pOw0KICAgICAgICBsZXQgYWxsR3JvdXBzID0gW107DQogICAgICAgIGZvciAoY29uc3QgbGluZSBvZiBsaW5lcykgew0KICAgICAgICAgIGNvbnN0IGxpbmVSZXN1bHQgPSBoYW5kbGVOdW1iZXJSZWNvZ25pdGlvbihsaW5lKTsNCiAgICAgICAgICBpZiAobGluZVJlc3VsdCAmJiBsaW5lUmVzdWx0Lmdyb3VwcyAmJiBsaW5lUmVzdWx0Lmdyb3Vwcy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICBhbGxHcm91cHMgPSBhbGxHcm91cHMuY29uY2F0KGxpbmVSZXN1bHQuZ3JvdXBzKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgcmVzdWx0ID0geyBncm91cHM6IGFsbEdyb3VwcyB9Ow0KICAgICAgfQ0KICAgICAgDQogICAgICANCg0KICAgICAgaWYgKHJlc3VsdCkgew0KICAgICAgICAvLyDlpoLmnpzlrZjlnKhncm91cHPvvIzor7TmmI7mnInlpJrkuKrlkozlgLznu4QNCiAgICAgICAgaWYgKHJlc3VsdC5ncm91cHMgJiYgcmVzdWx0Lmdyb3Vwcy5sZW5ndGggPiAwKSB7DQogICAgIA0KICAgICAgICAgIC8vIOa4heepuueOsOacieeahOaKleazqOe7hA0KICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm1EYXRhLCAnYmV0R3JvdXBzJywgW10pOw0KICAgICAgICAgIC8vIOa3u+WKoOaJgOacieWSjOWAvOe7hA0KICAgICAgICAgIHJlc3VsdC5ncm91cHMuZm9yRWFjaChncm91cCA9PiB7DQogICAgICAgICAgICBjb25zdCBuZXdHcm91cCA9IHsNCiAgICAgICAgICAgICAgbWV0aG9kSWQ6IGdyb3VwLm1ldGhvZElkLA0KICAgICAgICAgICAgICBkYW5zaHVhbmc6IGdyb3VwLmRhbnNodWFuZywNCiAgICAgICAgICAgICAgZGF4aWFvOiBncm91cC5kYXhpYW8sDQogICAgICAgICAgICAgIGRpbmd3ZWk6IGdyb3VwLmRpbmd3ZWksDQogICAgICAgICAgICAgIGhlemhpOiBncm91cC5oZXpoaSwNCiAgICAgICAgICAgICAgYmV0TnVtYmVyczogZ3JvdXAuYmV0TnVtYmVycywNCiAgICAgICAgICAgICAgbW9uZXk6IGdyb3VwLm1vbmV5ICE9IG51bGwgPyBTdHJpbmcoZ3JvdXAubW9uZXkpIDogJycsIC8vIOehruS/nW1vbmV55Li65a2X56ym5Liy57G75Z6LDQogICAgICAgICAgICAgIGxvdHRlcnlJZDogdGhpcy5mdXRpU3dpdGNoID09PSAndGMnID8gMiA6IChncm91cC5sb3R0ZXJ5SWQgfHwgMSkNCiAgICAgICAgICAgIH07DQoNCiAgICAgICAgICAgIHRoaXMuZm9ybURhdGEuYmV0R3JvdXBzLnB1c2gobmV3R3JvdXApOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KDQogICAgICAgICAgLy8g5Y2V5Liq5oqV5rOo57uE55qE5oOF5Ya1DQogICAgICAgICAgY29uc3QgbmV3R3JvdXAgPSB7DQogICAgICAgICAgICBtZXRob2RJZDogcmVzdWx0Lm1ldGhvZElkLA0KICAgICAgICAgICAgZGFuc2h1YW5nOiByZXN1bHQuZGFuc2h1YW5nLA0KICAgICAgICAgICAgZGF4aWFvOiByZXN1bHQuZGF4aWFvLA0KICAgICAgICAgICAgZGluZ3dlaTogcmVzdWx0LmRpbmd3ZWksDQogICAgICAgICAgICBoZXpoaTogcmVzdWx0LmhlemhpLA0KICAgICAgICAgICAgYmV0TnVtYmVyczogcmVzdWx0LmJldE51bWJlcnMsDQogICAgICAgICAgICBtb25leTogcmVzdWx0Lm1vbmV5ICE9IG51bGwgPyBTdHJpbmcocmVzdWx0Lm1vbmV5KSA6ICcnLCAvLyDnoa7kv51tb25leeS4uuWtl+espuS4suexu+Weiw0KICAgICAgICAgICAgbG90dGVyeUlkOiB0aGlzLmZ1dGlTd2l0Y2ggPT09ICd0YycgPyAyIDogKHJlc3VsdC5sb3R0ZXJ5SWQgfHwgMSkNCiAgICAgICAgICB9Ow0KDQogICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybURhdGEsICdiZXRHcm91cHMnLCBbbmV3R3JvdXBdKTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWQjOatpeWIsGxvY2FsRm9ybQ0KICAgICAgICBjb25zdCBsb2NhbEdyb3VwcyA9IHRoaXMuZm9ybURhdGEuYmV0R3JvdXBzLm1hcChncm91cCA9PiAoew0KICAgICAgICAgIC4uLmdyb3VwLA0KICAgICAgICAgIGRhbnNodWFuZzogZ3JvdXAuZGFuc2h1YW5nIHx8IG51bGwsDQogICAgICAgICAgZGF4aWFvOiBncm91cC5kYXhpYW8gfHwgbnVsbCwNCiAgICAgICAgICBkaW5nd2VpOiBncm91cC5kaW5nd2VpIHx8IG51bGwsDQogICAgICAgICAgaGV6aGk6IGdyb3VwLmhlemhpIHx8IG51bGwsDQogICAgICAgICAgYmV0TnVtYmVyczogZ3JvdXAuYmV0TnVtYmVycyB8fCAnJywNCiAgICAgICAgICBtb25leTogZ3JvdXAubW9uZXkgfHwgbnVsbCwNCiAgICAgICAgICBsb3R0ZXJ5SWQ6IGdyb3VwLmxvdHRlcnlJZCB8fCAxDQogICAgICAgIH0pKTsNCiAgIA0KICAgICAgICB0aGlzLiRzZXQodGhpcy5sb2NhbEZvcm0sICdiZXRHcm91cHMnLCBsb2NhbEdyb3Vwcyk7DQoNCiAgICAgICAgLy8g5by65Yi25pu05paw6KeG5Zu+DQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICANCiAgICAgICAgICB0aGlzLnVwZGF0ZVRvdGFscygpOw0KICAgICAgICAgIC8vIOehruS/neavj+S4quaKleazqOe7hOmDveato+ehruabtOaWsA0KICAgICAgICAgIHRoaXMuZm9ybURhdGEuYmV0R3JvdXBzLmZvckVhY2goKGdyb3VwLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgY29uc3QgdXBkYXRlZEdyb3VwID0geyAuLi5ncm91cCB9Ow0KICAgICAgICAgICANCiAgICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm1EYXRhLmJldEdyb3VwcywgaW5kZXgsIHVwZGF0ZWRHcm91cCk7DQogICAgICAgICAgfSk7DQogICAgDQogICAgICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICBmb3JtYXRCZXROdW1iZXJzVG9EaXNwbGF5KGpzb25TdHIpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGlmICghanNvblN0cikgcmV0dXJuICcnOw0KDQogICAgICAgIC8vIOWmguaenOS4jeaYr0pTT07moLzlvI/vvIznm7TmjqXov5Tlm54NCiAgICAgICAgaWYgKCFqc29uU3RyLnN0YXJ0c1dpdGgoJ3snKSkgew0KICAgICAgICAgIHJldHVybiBqc29uU3RyOw0KICAgICAgICB9DQoNCiAgICAgICAgY29uc3QgZGF0YSA9IHR5cGVvZiBqc29uU3RyID09PSAnc3RyaW5nJyA/IEpTT04ucGFyc2UoanNvblN0cikgOiBqc29uU3RyOw0KICAgICAgICBpZiAoIWRhdGEubnVtYmVycyB8fCAhQXJyYXkuaXNBcnJheShkYXRhLm51bWJlcnMpKSByZXR1cm4ganNvblN0cjsNCg0KICAgICAgICByZXR1cm4gZGF0YS5udW1iZXJzLm1hcChudW0gPT4gew0KICAgICAgICAgIC8vIOi3qOW6pueOqeazleeJueauiuWkhOeQhg0KICAgICAgICAgIGlmIChudW0ua3VhZHUgIT09IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgcmV0dXJuIGDot6jluqYke251bS5rdWFkdX1gOw0KICAgICAgICAgIH0NCiAgICAgICAgICAvLyDog4bmi5bnjqnms5XnibnmrorlpITnkIYNCiAgICAgICAgICBpZiAobnVtLmRhbm1hICE9PSB1bmRlZmluZWQgJiYgbnVtLnR1b21hICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgIHJldHVybiBg6IOGJHtudW0uZGFubWF95ouWJHtudW0udHVvbWF9YDsNCiAgICAgICAgICB9DQogICAgICAgICAgLy8g5YW25LuW546p5rOVDQogICAgICAgICAgY29uc3QgdmFsdWVzID0gT2JqZWN0LnZhbHVlcyhudW0pOw0KICAgICAgICAgIHJldHVybiB2YWx1ZXMuam9pbignJyk7DQogICAgICAgIH0pLmpvaW4oJywnKTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+agvOW8j+WMluS4i+azqOWPt+eggeWksei0pTonLCBlcnJvciwgJ2pzb25TdHI6JywganNvblN0cik7DQogICAgICAgIHJldHVybiBqc29uU3RyOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g6I635Y+W55So5LqO5pi+56S655qE5LiL5rOo5Y+356CBDQogICAgZ2V0RGlzcGxheUJldE51bWJlcnMoZ3JvdXApIHsNCiAgICAgIGlmICghZ3JvdXAuYmV0TnVtYmVycykgcmV0dXJuICcnOw0KDQogICAgICAvLyDot6jluqbnjqnms5UgKDYwLTY5KSDlkozog4bmi5bnjqnms5UgKDQ0LTU5KSDpnIDopoHnibnmrormoLzlvI/ljJYNCiAgICAgIGlmICgoZ3JvdXAubWV0aG9kSWQgPj0gNDQgJiYgZ3JvdXAubWV0aG9kSWQgPD0gNTkpIHx8IChncm91cC5tZXRob2RJZCA+PSA2MCAmJiBncm91cC5tZXRob2RJZCA8PSA2OSkpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKbmmK9KU09O5qC85byPDQogICAgICAgICAgaWYgKGdyb3VwLmJldE51bWJlcnMuc3RhcnRzV2l0aCgneycpICYmIGdyb3VwLmJldE51bWJlcnMuaW5jbHVkZXMoJ251bWJlcnMnKSkgew0KICAgICAgICAgICAgcmV0dXJuIHRoaXMuZm9ybWF0QmV0TnVtYmVyc1RvRGlzcGxheShncm91cC5iZXROdW1iZXJzKTsNCiAgICAgICAgICB9DQogICAgICAgICAgLy8g5aaC5p6c5LiN5pivSlNPTuagvOW8j++8jOebtOaOpei/lOWbnu+8iOeUqOaIt+ato+WcqOe8lui+ke+8iQ0KICAgICAgICAgIHJldHVybiBncm91cC5iZXROdW1iZXJzOw0KICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+agvOW8j+WMluaYvuekuuWPt+eggeWksei0pTonLCBlcnJvcik7DQogICAgICAgICAgcmV0dXJuIGdyb3VwLmJldE51bWJlcnM7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g5YW25LuW546p5rOV55u05o6l5pi+56S6DQogICAgICByZXR1cm4gZ3JvdXAuYmV0TnVtYmVyczsNCiAgICB9LA0KICAgIC8vIOWkhOeQhuS4i+azqOWPt+eggei+k+WFpQ0KICAgIG9uQmV0TnVtYmVyc0lucHV0KHZhbHVlLCBpbmRleCkgew0KICAgICAgY29uc3QgZ3JvdXAgPSB0aGlzLmZvcm1EYXRhLmJldEdyb3Vwc1tpbmRleF07DQoNCiAgICAgIC8vIOi3qOW6pueOqeazleWSjOiDhuaLlueOqeazlemcgOimgeS/neaMgUpTT07moLzlvI/lrZjlgqjvvIzkvYbmmL7npLrkuLrnlKjmiLflj4vlpb3moLzlvI8NCiAgICAgIGlmICgoZ3JvdXAubWV0aG9kSWQgPj0gNDQgJiYgZ3JvdXAubWV0aG9kSWQgPD0gNTkpIHx8IChncm91cC5tZXRob2RJZCA+PSA2MCAmJiBncm91cC5tZXRob2RJZCA8PSA2OSkpIHsNCiAgICAgICAgLy8g5a+55LqO6L+Z5Lqb54m55q6K546p5rOV77yM55So5oi36L6T5YWl55qE5Y+L5aW95qC85byP6ZyA6KaB6L2s5o2i5Li6SlNPTuagvOW8j+WtmOWCqA0KICAgICAgICAvLyDkvYbov5nph4zmiJHku6zmmoLml7bnm7TmjqXlrZjlgqjnlKjmiLfovpPlhaXvvIzorqnor4bliKvpgLvovpHlpITnkIYNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybURhdGEuYmV0R3JvdXBzLCBpbmRleCwgeyAuLi5ncm91cCwgYmV0TnVtYmVyczogdmFsdWUgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlhbbku5bnjqnms5Xnm7TmjqXlrZjlgqgNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybURhdGEuYmV0R3JvdXBzLCBpbmRleCwgeyAuLi5ncm91cCwgYmV0TnVtYmVyczogdmFsdWUgfSk7DQogICAgICB9DQoNCiAgICAgIHRoaXMudXBkYXRlVG90YWxzKCk7DQogICAgfSwNCiAgICBpbml0Rm9ybURhdGEoKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDkv53lrZjljp/lp4vmlbDmja4NCiAgICAgICAgdGhpcy5vcmlnaW5hbFVzZXJJZCA9IHRoaXMucm93LnVzZXJJZDsNCiAgICAgICAgdGhpcy5vcmlnaW5hbFNlcmlhbE51bWJlciA9IHRoaXMucm93LnNlcmlhbE51bWJlcjsNCg0KICAgICAgICAvLyDorr7nva7mnJ/lj7cNCiAgICAgICAgdGhpcy5mb3JtRGF0YS5mYzNkSXNzdWVOdW1iZXIgPSB0aGlzLnJvdy5pc3N1ZU51bWJlcjsNCiAgICAgICAgdGhpcy5mb3JtRGF0YS50Y0lzc3VlTnVtYmVyID0gdGhpcy5yb3cuaXNzdWVOdW1iZXI7DQoNCiAgICAgICAgLy8g5qC85byP5YyW5LiL5rOo5Y+356CBDQogICAgICAgIGNvbnN0IGZvcm1hdHRlZEJldE51bWJlcnMgPSB0aGlzLmZvcm1hdEJldE51bWJlcnNUb0Rpc3BsYXkodGhpcy5yb3cuYmV0TnVtYmVycyk7DQoNCiAgICAgICAgLy8g5Yib5bu65oqV5rOo57uEDQogICAgICAgIGNvbnN0IGdyb3VwID0gew0KICAgICAgICAgIG1ldGhvZElkOiB0aGlzLnJvdy5tZXRob2RJZCwNCiAgICAgICAgICBtb25leTogdGhpcy5yb3cubW9uZXkgIT0gbnVsbCA/IFN0cmluZyh0aGlzLnJvdy5tb25leSkgOiAnJywgLy8g56Gu5L+dbW9uZXnkuLrlrZfnrKbkuLLnsbvlnosNCiAgICAgICAgICBiZXROdW1iZXJzOiBmb3JtYXR0ZWRCZXROdW1iZXJzLCAvLyDkvb/nlKjmoLzlvI/ljJblkI7nmoTlj7fnoIENCiAgICAgICAgICBkYW5zaHVhbmc6IHRoaXMucm93LmRhbnNodWFuZywNCiAgICAgICAgICBkYXhpYW86IHRoaXMucm93LmRheGlhbywNCiAgICAgICAgICBkaW5nd2VpOiB0aGlzLnJvdy5kaW5nd2VpLA0KICAgICAgICAgIGhlemhpOiB0aGlzLnJvdy5oZXpoaSwNCiAgICAgICAgICBsb3R0ZXJ5SWQ6IHRoaXMucm93LmxvdHRlcnlJZA0KICAgICAgICB9Ow0KDQogICAgICAgIC8vIOiuvue9ruaKleazqOe7hA0KICAgICAgICB0aGlzLmZvcm1EYXRhLmJldEdyb3VwcyA9IFtncm91cF07DQoNCiAgICAgICAgLy8g5aaC5p6c5pyJ6K+G5Yir5paH5pys77yM5Lmf6K6+572uDQogICAgICAgIGlmICh0aGlzLnJvdy5zaGliaWUpIHsNCiAgICAgICAgICB0aGlzLmZvcm1EYXRhLnNoaWJpZSA9IHRoaXMucm93LnNoaWJpZTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWmguaenOaciea1geawtOWPt++8jOS5n+iuvue9rg0KICAgICAgICBpZiAodGhpcy5yb3cuc2VyaWFsTnVtYmVyKSB7DQogICAgICAgDQogICAgICAgICAgdGhpcy5zZXJpYWxOdW1iZXIgPSB0aGlzLnJvdy5zZXJpYWxOdW1iZXI7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmm7TmlrDmgLvorqENCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIHRoaXMudXBkYXRlVG90YWxzKCk7DQogICAgICAgICAgLy8g5by65Yi25pu05paw6KeG5Zu+5Lul56Gu5L+d54m55q6K546p5rOV5q2j56Gu5pi+56S6DQogICAgICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsNCiAgICAgICAgfSk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliJ3lp4vljJbooajljZXmlbDmja7lpLHotKU6JywgZXJyb3IpOw0KICAgICAgfQ0KICAgIH0sDQogICAgZm9ybWF0TnVtYmVyc0ZvclJlcXVlc3QoYmV0TnVtYmVycywgbWV0aG9kSWQpIHsNCiAgICAgIGlmICghYmV0TnVtYmVycykgcmV0dXJuIHsgbnVtYmVyczogW10gfTsNCg0KICAgICAgLy8g6IOG5ouW546p5rOVICg0NC01OSkgLSDlj6rlrZjlgqhkYW5tYeWSjHR1b21hDQogICAgICBpZiAobWV0aG9kSWQgPj0gNDQgJiYgbWV0aG9kSWQgPD0gNTkpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMuZm9ybWF0RGFudHVvTnVtYmVycyhiZXROdW1iZXJzLCBtZXRob2RJZCk7DQogICAgICB9DQoNCiAgICAgIC8vIOi3qOW6pueOqeazlSAoNjAtNjkpIC0g5Y+q5a2Y5YKoa3VhZHUNCiAgICAgIGlmIChtZXRob2RJZCA+PSA2MCAmJiBtZXRob2RJZCA8PSA2OSkgew0KICAgICAgICByZXR1cm4gdGhpcy5mb3JtYXRLdWFkdU51bWJlcnMoYmV0TnVtYmVycywgbWV0aG9kSWQpOw0KICAgICAgfQ0KDQogICAgICAvLyDliIblibLlj7fnoIHnu4QNCiAgICAgIGNvbnN0IG51bWJlckdyb3VwcyA9IGJldE51bWJlcnMuc3BsaXQoJywnKS5maWx0ZXIobiA9PiBuLnRyaW0oKSk7DQoNCiAgICAgIC8vIOagueaNrueOqeazleagvOW8j+WMlg0KICAgICAgY29uc3QgZm9ybWF0TWFwID0gew0KICAgICAgICAvLyDkuIDkvY3mlbDnjqnms5XvvIjni6zog4bjgIHkuIDnoIHlrprkvY3jgIHmnYDnoIHnrYnvvIkNCiAgICAgICAgMTogKG51bSkgPT4gKHsgYTogcGFyc2VJbnQobnVtKSB9KSwNCiAgICAgICAgMjogKG51bSkgPT4gKHsgYTogcGFyc2VJbnQobnVtKSB9KSwNCiAgICAgICAgMzM6IChudW0pID0+ICh7IGE6IHBhcnNlSW50KG51bSkgfSksDQogICAgICAgIDM0OiAobnVtKSA9PiAoeyBhOiBudW0gfSksIC8vIOS4pOeggeWumuS9je+8jOS/neaMgeWOn+Wni+aooeW8j+WmgiI5KjYiDQogICAgICAgIDM1OiAobnVtKSA9PiAoeyBhOiBudW0gfSksIC8vIOS4gOeggeWNleWPjA0KICAgICAgICAzNjogKG51bSkgPT4gKHsgYTogbnVtIH0pLCAvLyDkuIDnoIHlpKflsI8NCiAgICAgICAgLy8g5Lik5L2N5pWw546p5rOV77yI5Lik56CB57uE5ZCI44CB5a+55a2Q562J77yJDQogICAgICAgIDM6IChudW0pID0+IHsNCiAgICAgICAgICBjb25zdCBkaWdpdHMgPSBudW0uc3BsaXQoJycpLm1hcChuID0+IHBhcnNlSW50KG4pKTsNCiAgICAgICAgICByZXR1cm4geyBhOiBkaWdpdHNbMF0sIGI6IGRpZ2l0c1sxXSB9Ow0KICAgICAgICB9LA0KICAgICAgICA0OiAobnVtKSA9PiB7DQogICAgICAgICAgY29uc3QgZGlnaXRzID0gbnVtLnNwbGl0KCcnKS5tYXAobiA9PiBwYXJzZUludChuKSk7DQogICAgICAgICAgcmV0dXJuIHsgYTogZGlnaXRzWzBdLCBiOiBkaWdpdHNbMV0gfTsNCiAgICAgICAgfSwNCiAgICAgICAgLy8g5Lik56CB57uE5LiJDQogICAgICAgIDcwOiAobnVtKSA9PiB7DQogICAgICAgICAgY29uc3QgZGlnaXRzID0gbnVtLnNwbGl0KCcnKS5tYXAobiA9PiBwYXJzZUludChuKSk7DQogICAgICAgICAgcmV0dXJuIHsgYTogZGlnaXRzWzBdLCBiOiBkaWdpdHNbMV0gfTsNCiAgICAgICAgfSwNCg0KICAgICAgICAvLyDkuInkvY3mlbDnjqnms5XvvIjkuInnoIHnm7TpgInjgIHnu4TpgInjgIHpmLLlr7nnrYnvvIkNCiAgICAgICAgNTogKG51bSkgPT4gew0KICAgICAgICAgIGNvbnN0IGRpZ2l0cyA9IG51bS5zcGxpdCgnJykubWFwKG4gPT4gcGFyc2VJbnQobikpOw0KICAgICAgICAgIHJldHVybiB7IGE6IGRpZ2l0c1swXSwgYjogZGlnaXRzWzFdLCBjOiBkaWdpdHNbMl0gfTsNCiAgICAgICAgfSwNCiAgICAgICAgNjogKG51bSkgPT4gew0KICAgICAgICAgIGNvbnN0IGRpZ2l0cyA9IG51bS5zcGxpdCgnJykubWFwKG4gPT4gcGFyc2VJbnQobikpOw0KICAgICAgICAgIHJldHVybiB7IGE6IGRpZ2l0c1swXSwgYjogZGlnaXRzWzFdLCBjOiBkaWdpdHNbMl0gfTsNCiAgICAgICAgfSwNCiAgICAgICAgNzogKG51bSkgPT4gew0KICAgICAgICAgIGNvbnN0IGRpZ2l0cyA9IG51bS5zcGxpdCgnJykubWFwKG4gPT4gcGFyc2VJbnQobikpOw0KICAgICAgICAgIHJldHVybiB7IGE6IGRpZ2l0c1swXSwgYjogZGlnaXRzWzFdLCBjOiBkaWdpdHNbMl0gfTsNCiAgICAgICAgfSwNCiAgICAgICAgMTAwOiAobnVtKSA9PiB7DQogICAgICAgICAgY29uc3QgZGlnaXRzID0gbnVtLnNwbGl0KCcnKS5tYXAobiA9PiBwYXJzZUludChuKSk7DQogICAgICAgICAgcmV0dXJuIHsgYTogZGlnaXRzWzBdLCBiOiBkaWdpdHNbMV0sIGM6IGRpZ2l0c1syXSB9Ow0KICAgICAgICB9LA0KDQogICAgICAgIC8vIOWbm+eggQ0KICAgICAgICA4OiAobnVtKSA9PiB7DQogICAgICAgICAgY29uc3QgZGlnaXRzID0gbnVtLnNwbGl0KCcnKS5tYXAobiA9PiBwYXJzZUludChuKSk7DQogICAgICAgICAgcmV0dXJuIHsgYTogZGlnaXRzWzBdLCBiOiBkaWdpdHNbMV0sIGM6IGRpZ2l0c1syXSwgZDogZGlnaXRzWzNdIH07DQogICAgICAgIH0sDQogICAgICAgIDk6IChudW0pID0+IHsNCiAgICAgICAgICBjb25zdCBkaWdpdHMgPSBudW0uc3BsaXQoJycpLm1hcChuID0+IHBhcnNlSW50KG4pKTsNCiAgICAgICAgICByZXR1cm4geyBhOiBkaWdpdHNbMF0sIGI6IGRpZ2l0c1sxXSwgYzogZGlnaXRzWzJdLCBkOiBkaWdpdHNbM10gfTsNCiAgICAgICAgfSwNCg0KICAgICAgICAvLyDkupTnoIENCiAgICAgICAgMTA6IChudW0pID0+IHsNCiAgICAgICAgICBjb25zdCBkaWdpdHMgPSBudW0uc3BsaXQoJycpLm1hcChuID0+IHBhcnNlSW50KG4pKTsNCiAgICAgICAgICByZXR1cm4geyBhOiBkaWdpdHNbMF0sIGI6IGRpZ2l0c1sxXSwgYzogZGlnaXRzWzJdLCBkOiBkaWdpdHNbM10sIGU6IGRpZ2l0c1s0XSB9Ow0KICAgICAgICB9LA0KICAgICAgICAxMTogKG51bSkgPT4gew0KICAgICAgICAgIGNvbnN0IGRpZ2l0cyA9IG51bS5zcGxpdCgnJykubWFwKG4gPT4gcGFyc2VJbnQobikpOw0KICAgICAgICAgIHJldHVybiB7IGE6IGRpZ2l0c1swXSwgYjogZGlnaXRzWzFdLCBjOiBkaWdpdHNbMl0sIGQ6IGRpZ2l0c1szXSwgZTogZGlnaXRzWzRdIH07DQogICAgICAgIH0sDQoNCiAgICAgICAgLy8g5YWt56CBDQogICAgICAgIDEyOiAobnVtKSA9PiB7DQogICAgICAgICAgY29uc3QgZGlnaXRzID0gbnVtLnNwbGl0KCcnKS5tYXAobiA9PiBwYXJzZUludChuKSk7DQogICAgICAgICAgcmV0dXJuIHsgYTogZGlnaXRzWzBdLCBiOiBkaWdpdHNbMV0sIGM6IGRpZ2l0c1syXSwgZDogZGlnaXRzWzNdLCBlOiBkaWdpdHNbNF0sIGY6IGRpZ2l0c1s1XSB9Ow0KICAgICAgICB9LA0KICAgICAgICAxMzogKG51bSkgPT4gew0KICAgICAgICAgIGNvbnN0IGRpZ2l0cyA9IG51bS5zcGxpdCgnJykubWFwKG4gPT4gcGFyc2VJbnQobikpOw0KICAgICAgICAgIHJldHVybiB7IGE6IGRpZ2l0c1swXSwgYjogZGlnaXRzWzFdLCBjOiBkaWdpdHNbMl0sIGQ6IGRpZ2l0c1szXSwgZTogZGlnaXRzWzRdLCBmOiBkaWdpdHNbNV0gfTsNCiAgICAgICAgfSwNCg0KICAgICAgICAvLyDkuIPnoIENCiAgICAgICAgMTQ6IChudW0pID0+IHsNCiAgICAgICAgICBjb25zdCBkaWdpdHMgPSBudW0uc3BsaXQoJycpLm1hcChuID0+IHBhcnNlSW50KG4pKTsNCiAgICAgICAgICByZXR1cm4geyBhOiBkaWdpdHNbMF0sIGI6IGRpZ2l0c1sxXSwgYzogZGlnaXRzWzJdLCBkOiBkaWdpdHNbM10sIGU6IGRpZ2l0c1s0XSwgZjogZGlnaXRzWzVdLCBnOiBkaWdpdHNbNl0gfTsNCiAgICAgICAgfSwNCiAgICAgICAgMTU6IChudW0pID0+IHsNCiAgICAgICAgICBjb25zdCBkaWdpdHMgPSBudW0uc3BsaXQoJycpLm1hcChuID0+IHBhcnNlSW50KG4pKTsNCiAgICAgICAgICByZXR1cm4geyBhOiBkaWdpdHNbMF0sIGI6IGRpZ2l0c1sxXSwgYzogZGlnaXRzWzJdLCBkOiBkaWdpdHNbM10sIGU6IGRpZ2l0c1s0XSwgZjogZGlnaXRzWzVdLCBnOiBkaWdpdHNbNl0gfTsNCiAgICAgICAgfSwNCg0KICAgICAgICAvLyDlhavnoIENCiAgICAgICAgMTY6IChudW0pID0+IHsNCiAgICAgICAgICBjb25zdCBkaWdpdHMgPSBudW0uc3BsaXQoJycpLm1hcChuID0+IHBhcnNlSW50KG4pKTsNCiAgICAgICAgICByZXR1cm4geyBhOiBkaWdpdHNbMF0sIGI6IGRpZ2l0c1sxXSwgYzogZGlnaXRzWzJdLCBkOiBkaWdpdHNbM10sIGU6IGRpZ2l0c1s0XSwgZjogZGlnaXRzWzVdLCBnOiBkaWdpdHNbNl0sIGg6IGRpZ2l0c1s3XSB9Ow0KICAgICAgICB9LA0KICAgICAgICAxNzogKG51bSkgPT4gew0KICAgICAgICAgIGNvbnN0IGRpZ2l0cyA9IG51bS5zcGxpdCgnJykubWFwKG4gPT4gcGFyc2VJbnQobikpOw0KICAgICAgICAgIHJldHVybiB7IGE6IGRpZ2l0c1swXSwgYjogZGlnaXRzWzFdLCBjOiBkaWdpdHNbMl0sIGQ6IGRpZ2l0c1szXSwgZTogZGlnaXRzWzRdLCBmOiBkaWdpdHNbNV0sIGc6IGRpZ2l0c1s2XSwgaDogZGlnaXRzWzddIH07DQogICAgICAgIH0sDQoNCiAgICAgICAgLy8g5Lmd56CBDQogICAgICAgIDE4OiAobnVtKSA9PiB7DQogICAgICAgICAgY29uc3QgZGlnaXRzID0gbnVtLnNwbGl0KCcnKS5tYXAobiA9PiBwYXJzZUludChuKSk7DQogICAgICAgICAgcmV0dXJuIHsgYTogZGlnaXRzWzBdLCBiOiBkaWdpdHNbMV0sIGM6IGRpZ2l0c1syXSwgZDogZGlnaXRzWzNdLCBlOiBkaWdpdHNbNF0sIGY6IGRpZ2l0c1s1XSwgZzogZGlnaXRzWzZdLCBoOiBkaWdpdHNbN10sIGk6IGRpZ2l0c1s4XSB9Ow0KICAgICAgICB9LA0KICAgICAgICAxOTogKG51bSkgPT4gew0KICAgICAgICAgIGNvbnN0IGRpZ2l0cyA9IG51bS5zcGxpdCgnJykubWFwKG4gPT4gcGFyc2VJbnQobikpOw0KICAgICAgICAgIHJldHVybiB7IGE6IGRpZ2l0c1swXSwgYjogZGlnaXRzWzFdLCBjOiBkaWdpdHNbMl0sIGQ6IGRpZ2l0c1szXSwgZTogZGlnaXRzWzRdLCBmOiBkaWdpdHNbNV0sIGc6IGRpZ2l0c1s2XSwgaDogZGlnaXRzWzddLCBpOiBkaWdpdHNbOF0gfTsNCiAgICAgICAgfSwNCg0KICAgICAgICAvLyDljIXmiZPnu4Tlha3jgIHnu4TkuInvvIjkuI3pnIDopoHlj7fnoIHvvIzov5Tlm57nqbrlr7nosaHvvIkNCiAgICAgICAgMjA6ICgpID0+ICh7fSksDQogICAgICAgIDMxOiAoKSA9PiAoe30pLA0KDQogICAgICAgIC8vIOWSjOWAvOeOqeazle+8iOWSjOWAvOebtOaOpeS8oOmAku+8jOS4jemcgOimgeWPt+egge+8iQ0KICAgICAgICAyMTogKCkgPT4gKHt9KSwNCiAgICAgICAgMjI6ICgpID0+ICh7fSksDQogICAgICAgIDIzOiAoKSA9PiAoe30pLA0KICAgICAgICAyNDogKCkgPT4gKHt9KSwNCiAgICAgICAgMjU6ICgpID0+ICh7fSksDQogICAgICAgIDI2OiAoKSA9PiAoe30pLA0KICAgICAgICAyNzogKCkgPT4gKHt9KSwNCg0KICAgICAgICAvLyDmlrDlop7lkozlgLznjqnms5XvvIjlkozlgLznm7TmjqXkvKDpgJLvvIzkuI3pnIDopoHlj7fnoIHvvIkNCiAgICAgICAgMzc6ICgpID0+ICh7fSksDQogICAgICAgIDM4OiAoKSA9PiAoe30pLA0KICAgICAgICAzOTogKCkgPT4gKHt9KSwNCiAgICAgICAgNDA6ICgpID0+ICh7fSksDQogICAgICAgIDQxOiAoKSA9PiAoe30pLA0KICAgICAgICA0MjogKCkgPT4gKHt9KSwNCiAgICAgICAgNDM6ICgpID0+ICh7fSksDQoNCiAgICAgICAgLy8g55u06YCJ5aSN5byP77yI5pqC5pe25oyJ56m65a+56LGh5aSE55CG77yJDQogICAgICAgIDI4OiAoKSA9PiAoe30pLA0KDQogICAgICAgIC8vIOWSjOWAvOWNleWPjA0KICAgICAgICAyOTogKCkgPT4gKHt9KSwNCiAgICAgICAgLy8g5ZKM5YC85aSn5bCPDQogICAgICAgIDMwOiAoKSA9PiAoe30pLA0KDQogICAgICB9Ow0KDQogICAgICBjb25zdCBmb3JtYXR0ZXIgPSBmb3JtYXRNYXBbbWV0aG9kSWRdOw0KICAgICAgaWYgKCFmb3JtYXR0ZXIpIHJldHVybiB7IG51bWJlcnM6IFtdIH07DQoNCiAgICAgIHJldHVybiB7DQogICAgICAgIG51bWJlcnM6IG51bWJlckdyb3Vwcy5tYXAobnVtID0+IGZvcm1hdHRlcihudW0udHJpbSgpKSkNCiAgICAgIH07DQogICAgfSwNCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgLy8g5YWI6aqM6K+B6YeR6aKdDQogICAgICBjb25zdCBtb25leVZhbGlkYXRpb24gPSB0aGlzLnZhbGlkYXRlTW9uZXkoKTsNCiAgICAgIGlmICghbW9uZXlWYWxpZGF0aW9uLnZhbGlkKSB7DQogICAgICAgIHRoaXMuJGFsZXJ0KG1vbmV5VmFsaWRhdGlvbi5tZXNzYWdlLCAn6ZSZ6K+vJywgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywNCiAgICAgICAgICB0eXBlOiAnZXJyb3InLA0KICAgICAgICAgIGRhbmdlcm91c2x5VXNlSFRNTFN0cmluZzogdHJ1ZQ0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBjb25zdCBsb2FkaW5nSW5zdGFuY2UgPSB0aGlzLiRsb2FkaW5nKHsNCiAgICAgICAgICAgIGxvY2s6IHRydWUsDQogICAgICAgICAgICB0ZXh0OiAn5q2j5Zyo5o+Q5Lqk5LiL5rOoLi4uJywNCiAgICAgICAgICAgIHNwaW5uZXI6ICdlbC1pY29uLWxvYWRpbmcnLA0KICAgICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMCwgMCwgMCwgMC43KScNCiAgICAgICAgICB9KTsNCg0KICAgICAgICAgIC8vIOS/ruatoyB1c2VySWQg6LWL5YC86YC76L6RDQogICAgICAgICAgY29uc3QgYmV0UmVjb3JkcyA9IHRoaXMuZm9ybURhdGEuYmV0R3JvdXBzLm1hcCgoZ3JvdXAsIGluZGV4KSA9PiB7DQogICAgICAgICAgICAvLyDmoLzlvI/ljJbkuIvms6jlj7fnoIHkuLpKU09O5qC85byPDQogICAgICAgICAgICBjb25zdCBudW1iZXJzID0gdGhpcy5mb3JtYXROdW1iZXJzRm9yUmVxdWVzdChncm91cC5iZXROdW1iZXJzLCBncm91cC5tZXRob2RJZCk7DQogICAgICAgICAgICAvLyDku44gc2Vzc2lvblN0b3JhZ2Ug6I635Y+WIHN5c1VzZXJJZA0KICAgICAgICAgICAgY29uc3Qgc3lzVXNlcklkID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgnc3lzVXNlcklkJyk7DQoNCiAgICAgICAgICAgIGNvbnN0IHJlY29yZCA9IHsNCiAgICAgICAgICAgICAgbG90dGVyeUlkOiBncm91cC5sb3R0ZXJ5SWQsDQogICAgICAgICAgICAgIGlzc3VlTnVtYmVyOiBncm91cC5sb3R0ZXJ5SWQgPT09IDEgPyB0aGlzLmZvcm1EYXRhLmZjM2RJc3N1ZU51bWJlciA6IHRoaXMuZm9ybURhdGEudGNJc3N1ZU51bWJlciwNCiAgICAgICAgICAgICAgbWV0aG9kSWQ6IGdyb3VwLm1ldGhvZElkLA0KICAgICAgICAgICAgICAvLyDkv67mlLnvvJp1c2VySWQg5aeL57uI55So5b2T5YmN6YCJ5oup55qEIHNlbGVjdGVkVXNlcklkDQogICAgICAgICAgICAgIHVzZXJJZDogTnVtYmVyKHRoaXMuc2VsZWN0ZWRVc2VySWQpLA0KICAgICAgICAgICAgICAvLyDmlrDlop7vvJrku44gc2Vzc2lvblN0b3JhZ2Ug6I635Y+W5bm25Lyg6YCSIHN5c1VzZXJJZA0KICAgICAgICAgICAgICBzeXNVc2VySWQ6IHN5c1VzZXJJZCA/IE51bWJlcihzeXNVc2VySWQpIDogbnVsbCwNCiAgICAgICAgICAgICAgYmV0TnVtYmVyczogSlNPTi5zdHJpbmdpZnkobnVtYmVycyksDQogICAgICAgICAgICAgIG1vbmV5OiBncm91cC5tb25leSwNCiAgICAgICAgICAgICAgZGFuc2h1YW5nOiBncm91cC5kYW5zaHVhbmcsDQogICAgICAgICAgICAgIGRheGlhbzogZ3JvdXAuZGF4aWFvLA0KICAgICAgICAgICAgICBkaW5nd2VpOiBncm91cC5kaW5nd2VpLA0KICAgICAgICAgICAgICBoZXpoaTogZ3JvdXAuaGV6aGksDQogICAgICAgICAgICAgIHRvdGFsQW1vdW50OiB0aGlzLmdldEdyb3VwQW1vdW50KGdyb3VwKSwNCiAgICAgICAgICAgICAgdG90YWxCZXRzOiB0aGlzLmdldEdyb3VwQmV0cyhncm91cCksDQogICAgICAgICAgICAgIHNoaWJpZTogdGhpcy5mb3JtRGF0YS5zaGliaWUsDQogICAgICAgICAgICAgIGJldFRpbWU6IHRoaXMuZm9ybWF0RGF0ZShuZXcgRGF0ZSgpKSwNCiAgICAgICAgICAgICAgc2VyaWFsTnVtYmVyOiB0aGlzLnNlcmlhbE51bWJlciAvLyDmlrDlop7vvJrkvKDpgJLmtYHmsLTlj7cNCiAgICAgICAgICAgIH07DQogICAgICAgICAgICAvLyDlpoLmnpzmmK/kv67mlLnmk43kvZzvvIzmt7vliqBiZXRJZA0KICAgICAgICAgICAgaWYgKHRoaXMucm93ICYmIGluZGV4ID09PSAwKSB7DQogICAgICAgICAgICAgIHJlY29yZC5iZXRJZCA9IHRoaXMucm93LmJldElkOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgLy8g6YCQ6KGM5omT5Y2w5q+P5LiqIHJlY29yZA0KICAgICAgICAgIA0KICAgICAgICAgICAgcmV0dXJuIHJlY29yZDsNCiAgICAgICAgICB9KTsNCg0KICAgICAgICAgIC8vIOaPkOS6pOWJjeaJk+WNsOaJgOaciSBiZXRSZWNvcmRzDQogICAgICAgDQoNCiAgICAgICAgICAvLyDmoLnmja7mmK/lkKbmmK/kv67mlLnmk43kvZzpgInmi6nkuI3lkIznmoTmj5DkuqTmlrnlvI8NCiAgICAgICAgICBjb25zdCB1cmwgPSB0aGlzLnJvdyA/ICcvZ2FtZS9yZWNvcmQnIDogJy9nYW1lL3JlY29yZC9iYXRjaCc7DQogICAgICAgICAgY29uc3QgbWV0aG9kID0gdGhpcy5yb3cgPyAncHV0JyA6ICdwb3N0JzsNCiAgICAgICAgICBjb25zdCBkYXRhID0gdGhpcy5yb3cgPyBiZXRSZWNvcmRzWzBdIDogYmV0UmVjb3JkczsNCg0KICAgICAgICAgIHJlcXVlc3Qoew0KICAgICAgICAgICAgdXJsLA0KICAgICAgICAgICAgbWV0aG9kLA0KICAgICAgICAgICAgZGF0YQ0KICAgICAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCfmk43kvZzmiJDlip8nKTsNCiAgICAgICAgICAgICAgdGhpcy4kZW1pdCgnc3VjY2VzcycpOw0KICAgICAgICAgICAgICAvLyDmuIXnqbrlj7fnoIHor4bliKvmoYblkozmipXms6jnu4QNCiAgICAgICAgICAgICAgdGhpcy5mb3JtRGF0YS5zaGliaWUgPSAnJzsNCiAgICAgICAgICAgICAgdGhpcy5mb3JtRGF0YS5iZXRHcm91cHMgPSBbew0KICAgICAgICAgICAgICAgIG1ldGhvZElkOiB1bmRlZmluZWQsDQogICAgICAgICAgICAgICAgbW9uZXk6ICcnLA0KICAgICAgICAgICAgICAgIGJldE51bWJlcnM6ICcnLA0KICAgICAgICAgICAgICAgIGRhbnNodWFuZzogbnVsbCwNCiAgICAgICAgICAgICAgICBkYXhpYW86IG51bGwsDQogICAgICAgICAgICAgICAgZGluZ3dlaTogbnVsbCwNCiAgICAgICAgICAgICAgICBoZXpoaTogbnVsbCwNCiAgICAgICAgICAgICAgICBsb3R0ZXJ5SWQ6IDENCiAgICAgICAgICAgICAgfV07DQogICAgICAgICAgICAgIHRoaXMudXBkYXRlVG90YWxzKCk7DQogICAgICAgICAgICAgIC8vIOaWsOWinu+8muS7heWcqOS/ruaUueaXtuWFs+mXreW8ueeql++8jOaWsOWinuaXtuS4jeWFs+mXrQ0KICAgICAgICAgICAgICBpZiAodGhpcy5yb3cpIHsNCiAgICAgICAgICAgICAgICB0aGlzLmNsb3NlKCk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIC8vIOWmguaenOWQjuerr+i/lOWbnuS6huWFt+S9k+WTquS6m+aKleazqOWksei0pQ0KICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEuZmFpbGVkSW5kaWNlcykpIHsNCiAgICAgICAgICAgICAgICAvLyDkv53nlZnlpLHotKXnmoTmipXms6jnu4QNCiAgICAgICAgICAgICAgICBjb25zdCBmYWlsZWRHcm91cHMgPSB0aGlzLmZvcm1EYXRhLmJldEdyb3Vwcy5maWx0ZXIoKF8sIGluZGV4KSA9Pg0KICAgICAgICAgICAgICAgICAgcmVzcG9uc2UuZGF0YS5mYWlsZWRJbmRpY2VzLmluY2x1ZGVzKGluZGV4KQ0KICAgICAgICAgICAgICAgICk7DQogICAgICAgICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybURhdGEsICdiZXRHcm91cHMnLCBmYWlsZWRHcm91cHMpOw0KICAgICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKGAke3Jlc3BvbnNlLmRhdGEuZmFpbGVkSW5kaWNlcy5sZW5ndGh95Liq5LiL5rOo5aSx6LSl77yM6K+36YeN6K+VYCk7DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgLy8g5qOA5p+l6ZSZ6K+v5piv5ZCm5bey57uP6KKr5ZON5bqU5oum5oiq5Zmo5aSE55CGDQogICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLl9lcnJvckhhbmRsZWQpIHsNCiAgICAgICAgICAgICAgICAgIC8vIOmUmeivr+W3sue7j+iiq+WTjeW6lOaLpuaIquWZqOWkhOeQhuW5tuaYvuekuu+8jOi/memHjOS4jeWGjemHjeWkjeWkhOeQhg0KICAgICAgICAgIA0KICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAvLyDmnKrooqvlpITnkIbnmoTplJnor6/vvIzmmL7npLrpgJrnlKjplJnor6/kv6Hmga8NCiAgICAgICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKHJlc3BvbnNlLm1zZyB8fCAn5pON5L2c5aSx6LSl77yM6K+36YeN6K+VJyk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICAgICAgY29uc29sZS5lcnJvcign572R57uc6K+35rGC5aSx6LSlOicsIGVycm9yKTsNCg0KICAgICAgICAgICAgLy8g546w5Zyo5Y+q5pyJ55yf5q2j55qE572R57uc6ZSZ6K+v5omN5Lya6L+b5YWlIGNhdGNoIOWdlw0KICAgICAgICAgICAgLy8g5Lia5Yqh6ZSZ6K+v5bey57uP5Zyo5ZON5bqU5oum5oiq5Zmo5Lit5aSE55CG5bm26L+U5Zue5YiwIHRoZW4g5Z2XDQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign572R57uc6L+e5o6l5aSx6LSl77yM6K+35qOA5p+l572R57uc5ZCO6YeN6K+VJyk7DQogICAgICAgICAgfSkuZmluYWxseSgoKSA9PiB7DQogICAgICAgICAgICBsb2FkaW5nSW5zdGFuY2UuY2xvc2UoKTsNCiAgICAgICAgICAgIHRoaXMudXBkYXRlVG90YWxzKCk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0R3JvdXBCZXRzKGdyb3VwKSB7DQogICAgICAvLyDog4bmi5bnjqnms5UgKDQ0LTU5KSAtIOWni+e7iOaYvuekuuS4ujHms6gNCiAgICAgIGlmIChncm91cC5tZXRob2RJZCA+PSA0NCAmJiBncm91cC5tZXRob2RJZCA8PSA1OSkgew0KICAgICAgICByZXR1cm4gMTsNCiAgICAgIH0NCg0KICAgICAgLy8g6Leo5bqm546p5rOVICg2MC02OSkgLSDlp4vnu4jmmL7npLrkuLox5rOoDQogICAgICBpZiAoZ3JvdXAubWV0aG9kSWQgPj0gNjAgJiYgZ3JvdXAubWV0aG9kSWQgPD0gNjkpIHsNCiAgICAgICAgcmV0dXJuIDE7DQogICAgICB9DQoNCiAgICAgIGlmICh0aGlzLmlzU3BlY2lhbE1ldGhvZChncm91cC5tZXRob2RJZCkpIHsNCiAgICAgICAgaWYgKFsyMSwyMiwyMywyNCwyNSwyNiwyNywzNywzOCwzOSw0MCw0MSw0Miw0M10uaW5jbHVkZXMoZ3JvdXAubWV0aG9kSWQpKSB7DQogICAgICAgICAgcmV0dXJuIGdyb3VwLmhlemhpID8gMSA6IDA7DQogICAgICAgIH0NCiAgICAgICAgaWYgKFsyOSwzMF0uaW5jbHVkZXMoZ3JvdXAubWV0aG9kSWQpKSB7DQogICAgICAgICAgaWYgKGdyb3VwLmRhbnNodWFuZyB8fCBncm91cC5kYXhpYW8pIHsNCiAgICAgICAgICAgIHJldHVybiAxOw0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gMDsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoWzIwLDMxXS5pbmNsdWRlcyhncm91cC5tZXRob2RJZCkpIHsgIC8vIOWMheaJk+e7hOWFreWSjOWMheaJk+e7hOS4iQ0KICAgICAgICAgIHJldHVybiAxOw0KICAgICAgICB9DQogICAgICAgIHJldHVybiAwOw0KICAgICAgfQ0KICAgICAgaWYgKGdyb3VwLmJldE51bWJlcnMpIHsNCiAgICAgICAgcmV0dXJuIGdyb3VwLmJldE51bWJlcnMuc3BsaXQoJywnKS5maWx0ZXIocyA9PiBzLnRyaW0oKSkubGVuZ3RoOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIDA7DQogICAgfSwNCiAgICBnZXRHcm91cEFtb3VudChncm91cCkgew0KICAgICAgY29uc3QgYmV0cyA9IHRoaXMuZ2V0R3JvdXBCZXRzKGdyb3VwKTsNCiAgICAgIGNvbnN0IG1vbmV5ID0gTnVtYmVyKGdyb3VwLm1vbmV5KSB8fCAwOw0KICAgICAgcmV0dXJuIGJldHMgKiBtb25leTsNCiAgICB9LA0KICAgIGlzU3BlY2lhbE1ldGhvZChtZXRob2RJZCkgew0KICAgICAgcmV0dXJuIFsyMCwgMjEsIDIyLCAyMywgMjQsIDI1LCAyNiwgMjcsIDI5LCAzMCwgMzEsIDM3LCAzOCwgMzksIDQwLCA0MSwgNDIsIDQzXS5pbmNsdWRlcyhtZXRob2RJZCk7DQogICAgfSwNCiAgICAvLyDog4bmi5bmoLzlvI/ljJbmlrnms5UNCiAgICBmb3JtYXREYW50dW9OdW1iZXJzKGJldE51bWJlcnMsIG1ldGhvZElkKSB7DQogICAgICAvLyDop6PmnpDog4bnoIHlkozmi5bnoIENCiAgICAgIGNvbnN0IHBhcnRzID0gYmV0TnVtYmVycy5zcGxpdCgn5ouWJyk7DQogICAgICBpZiAocGFydHMubGVuZ3RoICE9PSAyKSByZXR1cm4geyBudW1iZXJzOiBbXSB9Ow0KDQogICAgICBjb25zdCBkYW5tYSA9IHBhcnRzWzBdLnJlcGxhY2UoL1teMC05XS9nLCAnJyk7DQogICAgICBjb25zdCB0dW9tYSA9IHBhcnRzWzFdLnJlcGxhY2UoL1teMC05LO+8jFxzXS9nLCAnJykuc3BsaXQoL1ss77yMXHNdKy8pLmZpbHRlcihuID0+IG4gJiYgbiAhPT0gZGFubWEpOw0KDQogICAgICByZXR1cm4gew0KICAgICAgICBudW1iZXJzOiBbew0KICAgICAgICAgIGRhbm1hOiBkYW5tYSwNCiAgICAgICAgICB0dW9tYTogdHVvbWEuam9pbignLCcpDQogICAgICAgIH1dDQogICAgICB9Ow0KICAgIH0sDQogICAgLy8g6Leo5bqm5qC85byP5YyW5pa55rOVDQogICAgZm9ybWF0S3VhZHVOdW1iZXJzKGJldE51bWJlcnMsIG1ldGhvZElkKSB7DQogICAgICBjb25zb2xlLmxvZygnZm9ybWF0S3VhZHVOdW1iZXJzIOi+k+WFpTonLCBiZXROdW1iZXJzLCAnbWV0aG9kSWQ6JywgbWV0aG9kSWQpOw0KDQogICAgICAvLyDlpoLmnpzlt7Lnu4/mmK9KU09O5qC85byP77yM55u05o6l6L+U5ZueDQogICAgICBpZiAoYmV0TnVtYmVycy5zdGFydHNXaXRoKCd7JykpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICBjb25zdCBwYXJzZWQgPSBKU09OLnBhcnNlKGJldE51bWJlcnMpOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCflt7LmmK9KU09O5qC85byPOicsIHBhcnNlZCk7DQogICAgICAgICAgcmV0dXJuIHBhcnNlZDsNCiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCdKU09O6Kej5p6Q5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAvLyDop6PmnpDot6jluqblgLwgLSDmlK/mjIHlpJrnp43moLzlvI8NCiAgICAgIGxldCBrdWFkdVZhbHVlID0gbnVsbDsNCg0KICAgICAgLy8g5qC85byPMTogIui3qOW6pjUiDQogICAgICBsZXQgbWF0Y2ggPSBiZXROdW1iZXJzLm1hdGNoKC/ot6jluqYoXGQpLyk7DQogICAgICBpZiAobWF0Y2gpIHsNCiAgICAgICAga3VhZHVWYWx1ZSA9IG1hdGNoWzFdOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5qC85byPMjogIui3qDUtNTAiIOaIliAi6LeoNSA1MCINCiAgICAgICAgbWF0Y2ggPSBiZXROdW1iZXJzLm1hdGNoKC/ot6goXGQpW1xzXC1dKlxkKy8pOw0KICAgICAgICBpZiAobWF0Y2gpIHsNCiAgICAgICAgICBrdWFkdVZhbHVlID0gbWF0Y2hbMV07DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5qC85byPMzog5LuObWV0aG9kSWTmjqjlr7zot6jluqblgLwNCiAgICAgICAgICBpZiAobWV0aG9kSWQgPj0gNjAgJiYgbWV0aG9kSWQgPD0gNjkpIHsNCiAgICAgICAgICAgIGt1YWR1VmFsdWUgPSAobWV0aG9kSWQgLSA2MCkudG9TdHJpbmcoKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgY29uc29sZS5sb2coJ+ino+aekOWHuueahOi3qOW6puWAvDonLCBrdWFkdVZhbHVlKTsNCg0KICAgICAgaWYgKGt1YWR1VmFsdWUgPT09IG51bGwpIHsNCiAgICAgICAgY29uc29sZS53YXJuKCfml6Dms5Xop6PmnpDot6jluqblgLzvvIzov5Tlm57nqbrmlbDnu4QnKTsNCiAgICAgICAgcmV0dXJuIHsgbnVtYmVyczogW10gfTsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgcmVzdWx0ID0gew0KICAgICAgICBudW1iZXJzOiBbew0KICAgICAgICAgIGt1YWR1OiBrdWFkdVZhbHVlDQogICAgICAgIH1dDQogICAgICB9Ow0KDQogICAgICBjb25zb2xlLmxvZygnZm9ybWF0S3VhZHVOdW1iZXJzIOi+k+WHujonLCByZXN1bHQpOw0KICAgICAgcmV0dXJuIHJlc3VsdDsNCiAgICB9LA0KICAgIHVwZGF0ZUdyb3VwVmFsdWUoZ3JvdXAsIGZpZWxkLCB2YWx1ZSkgew0KDQogICAgICBjb25zdCBpbmRleCA9IHRoaXMuZm9ybURhdGEuYmV0R3JvdXBzLmZpbmRJbmRleChnID0+IGcgPT09IGdyb3VwKTsNCiAgICAgIGlmIChpbmRleCA+IC0xKSB7DQogICAgICAgIGNvbnN0IHVwZGF0ZWRHcm91cCA9IHsNCiAgICAgICAgICAuLi50aGlzLmZvcm1EYXRhLmJldEdyb3Vwc1tpbmRleF0sDQogICAgICAgICAgW2ZpZWxkXTogdmFsdWUNCiAgICAgICAgfTsNCiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybURhdGEuYmV0R3JvdXBzLCBpbmRleCwgdXBkYXRlZEdyb3VwKTsNCiAgICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsNCiAgICAgIH0NCiAgICB9LA0KDQoNCiAgICAvLyDmoLzlvI/ljJbml6XmnJ/kuLogeXl5eS1NTS1kZCBISDptbTpzcw0KICAgIGZvcm1hdERhdGUoZGF0ZSkgew0KICAgICAgY29uc3QgcGFkID0gKG51bSkgPT4gKG51bSA8IDEwID8gYDAke251bX1gIDogbnVtKTsNCiAgICAgIGNvbnN0IHllYXIgPSBkYXRlLmdldEZ1bGxZZWFyKCk7DQogICAgICBjb25zdCBtb250aCA9IHBhZChkYXRlLmdldE1vbnRoKCkgKyAxKTsNCiAgICAgIGNvbnN0IGRheSA9IHBhZChkYXRlLmdldERhdGUoKSk7DQogICAgICBjb25zdCBob3VycyA9IHBhZChkYXRlLmdldEhvdXJzKCkpOw0KICAgICAgY29uc3QgbWludXRlcyA9IHBhZChkYXRlLmdldE1pbnV0ZXMoKSk7DQogICAgICBjb25zdCBzZWNvbmRzID0gcGFkKGRhdGUuZ2V0U2Vjb25kcygpKTsNCiAgICAgIHJldHVybiBgJHt5ZWFyfS0ke21vbnRofS0ke2RheX0gJHtob3Vyc306JHttaW51dGVzfToke3NlY29uZHN9YDsNCiAgICB9LA0KICAgIGdldEN1cnJlbnRVc2VyKCkgew0KICAgICAgbGV0IHVzZXJJZCA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ1VzZXJfSWQnKTsNCg0KICAgICAgaWYgKHVzZXJJZCkgew0KICAgICAgICAvLyDlpoLmnpxzZXNzaW9u5Lit5pyJVXNlcl9JZO+8jOebtOaOpeS9v+eUqA0KICAgICAgICB0aGlzLnNlbGVjdGVkVXNlcklkID0gTnVtYmVyKHVzZXJJZCk7DQogICAgICAgIHRoaXMubG9hZFVzZXJCeUlkKHVzZXJJZCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlpoLmnpxzZXNzaW9u5Lit5rKh5pyJVXNlcl9JZO+8jOWFiOiOt+WPlueUqOaIt+WIl+ihqO+8jOeEtuWQjuS9v+eUqOesrOS4gOS4queUqOaItw0KICAgICAgICB0aGlzLmluaXRpYWxpemVGaXJzdFVzZXIoKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgbG9hZFVzZXJCeUlkKHVzZXJJZCkgew0KICAgICAgcmVxdWVzdCh7DQogICAgICAgIHVybDogYC9nYW1lL2N1c3RvbWVyLyR7dXNlcklkfWAsDQogICAgICAgIG1ldGhvZDogJ2dldCcNCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDAgJiYgcmVzLmRhdGEpIHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRVc2VyID0gcmVzLmRhdGE7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5jdXJyZW50VXNlciA9IHsgbmFtZTogJ+acquefpeeUqOaItycgfTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnlKjmiLfkv6Hmga/lpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLmN1cnJlbnRVc2VyID0geyBuYW1lOiAn5pyq55+l55So5oi3JyB9Ow0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGluaXRpYWxpemVGaXJzdFVzZXIoKSB7DQogICAgICAvLyDojrflj5blvZPliY3nlKjmiLfnmoTnjqnlrrbliJfooagNCiAgICAgIHJlcXVlc3Qoew0KICAgICAgICB1cmw6ICcvZ2FtZS9jdXN0b21lci9saXN0JywNCiAgICAgICAgbWV0aG9kOiAnZ2V0JywNCiAgICAgICAgcGFyYW1zOiB7IHBhZ2VOdW06IDEsIHBhZ2VTaXplOiAxMDAwIH0NCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDAgJiYgcmVzLnJvd3MgJiYgcmVzLnJvd3MubGVuZ3RoID4gMCkgew0KICAgICAgICAgIC8vIOS9v+eUqOesrOS4gOS4queOqeWutuS9nOS4uum7mOiupOeUqOaItw0KICAgICAgICAgIGNvbnN0IGZpcnN0VXNlciA9IHJlcy5yb3dzWzBdOw0KICAgICAgICAgIHRoaXMuc2VsZWN0ZWRVc2VySWQgPSBmaXJzdFVzZXIudXNlcklkOw0KICAgICAgICAgIHRoaXMuY3VycmVudFVzZXIgPSBmaXJzdFVzZXI7DQogICAgICAgICAgLy8g5L+d5a2Y5Yiwc2Vzc2lvbuS4rQ0KICAgICAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oJ1VzZXJfSWQnLCBmaXJzdFVzZXIudXNlcklkKTsNCiAgICAgICAgDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCflvZPliY3nlKjmiLfmsqHmnInnjqnlrrbmlbDmja4nKTsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRVc2VyID0geyBuYW1lOiAn5peg546p5a625pWw5o2uJyB9Ow0KICAgICAgICAgIHRoaXMuc2VsZWN0ZWRVc2VySWQgPSBudWxsOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlueOqeWutuWIl+ihqOWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuY3VycmVudFVzZXIgPSB7IG5hbWU6ICfojrflj5blpLHotKUnIH07DQogICAgICAgIHRoaXMuc2VsZWN0ZWRVc2VySWQgPSBudWxsOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBnZXRBbGxVc2VycygpIHsNCiAgICAgIHJlcXVlc3Qoew0KICAgICAgICB1cmw6ICcvZ2FtZS9jdXN0b21lci9saXN0JywNCiAgICAgICAgbWV0aG9kOiAnZ2V0JywNCiAgICAgICAgcGFyYW1zOiB7IHBhZ2VOdW06IDEsIHBhZ2VTaXplOiAxMDAwIH0NCiAgICAgIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDAgJiYgcmVzLnJvd3MpIHsNCiAgICAgICAgICB0aGlzLnVzZXJMaXN0ID0gcmVzLnJvd3M7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy51c2VyTGlzdCA9IFtdOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIHN3aXRjaFVzZXIoKSB7DQogICAgICBpZiAoIXRoaXMuc2VsZWN0ZWRVc2VySWQpIHJldHVybjsNCiAgICAgIHNlc3Npb25TdG9yYWdlLnNldEl0ZW0oJ1VzZXJfSWQnLCB0aGlzLnNlbGVjdGVkVXNlcklkKTsNCiAgICAgIHRoaXMuZ2V0Q3VycmVudFVzZXIoKTsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5YiH5o2i55So5oi35oiQ5YqfJyk7DQogICAgfSwNCiAgICBvbkZ1dGlTd2l0Y2hDaGFuZ2UodmFsKSB7DQogICAgICBzZXNzaW9uU3RvcmFnZS5zZXRJdGVtKCdmdXRpU3dpdGNoJywgdmFsKTsNCiAgICAgIGlmICh2YWwgPT09ICd0YycpIHsNCiAgICAgICAgLy8g5LuF5L2T5b2p77yM5omA5pyJ5LiL5rOo5Y2V6YO96K6+5Li6Mg0KICAgICAgICBpZiAodGhpcy5mb3JtRGF0YSAmJiB0aGlzLmZvcm1EYXRhLmJldEdyb3Vwcykgew0KICAgICAgICAgIHRoaXMuZm9ybURhdGEuYmV0R3JvdXBzLmZvckVhY2goZ3JvdXAgPT4gew0KICAgICAgICAgICAgZ3JvdXAubG90dGVyeUlkID0gMjsNCiAgICAgICAgICAgIC8vIOehruS/nW1vbmV55a2X5q615aeL57uI5Li65a2X56ym5Liy57G75Z6LDQogICAgICAgICAgICBpZiAoZ3JvdXAubW9uZXkgIT0gbnVsbCAmJiB0eXBlb2YgZ3JvdXAubW9uZXkgIT09ICdzdHJpbmcnKSB7DQogICAgICAgICAgICAgIGdyb3VwLm1vbmV5ID0gU3RyaW5nKGdyb3VwLm1vbmV5KTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5YiH5o2i5Li65YWo6YOo5b2p56eN5pe277yM56Gu5L+d5pWw5o2u57G75Z6L5q2j56GuDQogICAgICAgIGlmICh0aGlzLmZvcm1EYXRhICYmIHRoaXMuZm9ybURhdGEuYmV0R3JvdXBzKSB7DQogICAgICAgICAgdGhpcy5mb3JtRGF0YS5iZXRHcm91cHMuZm9yRWFjaChncm91cCA9PiB7DQogICAgICAgICAgICAvLyDnoa7kv51tb25leeWtl+auteWni+e7iOS4uuWtl+espuS4suexu+Weiw0KICAgICAgICAgICAgaWYgKGdyb3VwLm1vbmV5ICE9IG51bGwgJiYgdHlwZW9mIGdyb3VwLm1vbmV5ICE9PSAnc3RyaW5nJykgew0KICAgICAgICAgICAgICBncm91cC5tb25leSA9IFN0cmluZyhncm91cC5tb25leSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIC8vIOWIh+aNouS4uuWFqOmDqOW9qeenjeaXtuS4jeWBmuW8uuWItumZkOWItg0KICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsNCiAgICB9LA0KICAgIC8vIHJlbW90ZSDmkJzntKLnlKjnmoQgZmlsdGVyTWV0aG9kDQogICAgZmlsdGVyTWV0aG9kKHF1ZXJ5KSB7DQogICAgICBpZiAoIXF1ZXJ5KSB7DQogICAgICAgIHRoaXMuZmlsdGVyZWRHYW1lTWV0aG9kcyA9IHRoaXMuZ2FtZU1ldGhvZHNEYXRhOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5maWx0ZXJlZEdhbWVNZXRob2RzID0gdGhpcy5nYW1lTWV0aG9kc0RhdGEuZmlsdGVyKGl0ZW0gPT4gew0KICAgICAgICAgIHJldHVybiAoaXRlbS5tZXRob2ROYW1lICYmIGl0ZW0ubWV0aG9kTmFtZS5pbmRleE9mKHF1ZXJ5KSAhPT0gLTEpIHx8DQogICAgICAgICAgICAgICAgIChxdWVyeSA9PT0gJzMnICYmIGl0ZW0ubWV0aG9kTmFtZSAmJiBpdGVtLm1ldGhvZE5hbWUuaW5kZXhPZign5LiJJykgIT09IC0xKTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICBzaG93Rm9ybWF0RGlhbG9nKCkgew0KICAgICAgdGhpcy5mb3JtYXREaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMuZm9ybWF0SW5wdXQgPSAnJzsNCiAgICAgIHRoaXMuZm9ybWF0T3V0cHV0ID0gJyc7DQogICAgICB0aGlzLmZvcm1hdFRhYiA9ICd1bmlmaWVkJzsgLy8g5q+P5qyh5omT5byA6YO96buY6K6k5pi+56S65a2X56ym6L2s5o2iDQogICAgfSwNCiAgICAvKiog5pi+56S657uf6K6h5a+56K+d5qGGICovDQogICAgc2hvd1N0YXRpc3RpY3MoKSB7DQogICAgICB0aGlzLnN0YXRpc3RpY3NWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8qKiDpqozor4Hph5Hpop0gKi8NCiAgICB2YWxpZGF0ZU1vbmV5KCkgew0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmZvcm1EYXRhLmJldEdyb3Vwcy5sZW5ndGg7IGkrKykgew0KICAgICAgICBjb25zdCBncm91cCA9IHRoaXMuZm9ybURhdGEuYmV0R3JvdXBzW2ldOw0KICAgICAgICBjb25zdCBtb25leSA9IGdyb3VwLm1vbmV5Ow0KICAgICAgICBjb25zdCBncm91cE51bWJlciA9IGkgKyAxOyAvLyDkuIvms6jljZXlj7fku44x5byA5aeLDQoNCiAgICAgICAgLy8g56Gu5L+dbW9uZXnmmK/lrZfnrKbkuLLnsbvlnovvvIzpmLLmraJ0cmltKCnmlrnms5XmiqXplJkNCiAgICAgICAgY29uc3QgbW9uZXlTdHIgPSBtb25leSAhPSBudWxsID8gU3RyaW5nKG1vbmV5KSA6ICcnOw0KDQogICAgICAgIC8vIOajgOafpeaYr+WQpuS4uuepug0KICAgICAgICBpZiAoIW1vbmV5U3RyIHx8IG1vbmV5U3RyLnRyaW0oKSA9PT0gJycpIHsNCiAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgdmFsaWQ6IGZhbHNlLA0KICAgICAgICAgICAgbWVzc2FnZTogYOesrDxzcGFuIHN0eWxlPSJjb2xvcjogI2ZmNDc1NzsgZm9udC13ZWlnaHQ6IGJvbGQ7IGZvbnQtc2l6ZTogMTZweDsiPiR7Z3JvdXBOdW1iZXJ9PC9zcGFuPue7hOS4i+azqOmHkemineS4jeiDveS4ujxzcGFuIHN0eWxlPSJjb2xvcjogI2ZmNDc1NzsgZm9udC13ZWlnaHQ6IGJvbGQ7IGZvbnQtc2l6ZTogMTZweDsiPuepujwvc3Bhbj5gDQogICAgICAgICAgfTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOajgOafpeaYr+WQpuS4uuaVsOWtlw0KICAgICAgICBjb25zdCBudW1Nb25leSA9IE51bWJlcihtb25leVN0ci50cmltKCkpOw0KICAgICAgICBpZiAoaXNOYU4obnVtTW9uZXkpIHx8IG51bU1vbmV5IDw9IDApIHsNCiAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgdmFsaWQ6IGZhbHNlLA0KICAgICAgICAgICAgbWVzc2FnZTogYOesrDxzcGFuIHN0eWxlPSJjb2xvcjogI2ZmNDc1NzsgZm9udC13ZWlnaHQ6IGJvbGQ7IGZvbnQtc2l6ZTogMTZweDsiPiR7Z3JvdXBOdW1iZXJ9PC9zcGFuPue7hOS4i+azqOmHkemineS4jeiDveS4ujxzcGFuIHN0eWxlPSJjb2xvcjogI2ZmNDc1NzsgZm9udC13ZWlnaHQ6IGJvbGQ7IGZvbnQtc2l6ZTogMTZweDsiPiIke21vbmV5U3RyfSI8L3NwYW4+77yM6K+35L+u5pS5YA0KICAgICAgICAgIH07DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgdmFsaWQ6IHRydWUsDQogICAgICAgIG1lc3NhZ2U6ICcnDQogICAgICB9Ow0KICAgIH0sDQogICAgLyoqIOajgOafpee7n+iuoeadg+mZkCAqLw0KICAgIGFzeW5jIGNoZWNrU3RhdGlzdGljc1Blcm1pc3Npb24oKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDosIPnlKjkuJPpl6jnmoTnu5/orqHmnYPpmZBBUEkNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRTdGF0aXN0aWNzUGVybWlzc2lvbigpOw0KDQoNCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmNvZGUgPT09IDIwMCAmJiByZXNwb25zZS5kYXRhICE9PSB1bmRlZmluZWQpIHsNCg0KICAgICAgICAgIHRoaXMuaGFzU3RhdGlzdGljc1Blcm1pc3Npb24gPSByZXNwb25zZS5kYXRhID09PSAnMSc7DQogICAgICAgIH0gZWxzZSB7DQogDQogICAgICAgICAgdGhpcy5oYXNTdGF0aXN0aWNzUGVybWlzc2lvbiA9IGZhbHNlOw0KICAgICAgICB9DQoNCiAgDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnlKjmiLfmnYPpmZDlpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLmhhc1N0YXRpc3RpY3NQZXJtaXNzaW9uID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVGb3JtYXRJbnB1dCgpIHsNCiAgICAgIC8vIOWPpeWPt3RhYu+8muaJgOacieWPpeWPt+exu+Wtl+espui9rOS4ui0NCiAgICAgIGxldCBvdXQgPSAodGhpcy5mb3JtYXRJbnB1dCB8fCAnJykucmVwbGFjZSgvW+OAgu+8ji5dL2csICctJyk7DQogICAgICAvLyDov57nu63lpJrkuKrnn63mqKrlkIjlubbkuLrkuIDkuKoNCiAgICAgIG91dCA9IG91dC5yZXBsYWNlKC8tKy9nLCAnLScpOw0KICAgICAgLy8g5YWI5pu/5o2i5omA5pyJ5rGJ5a2X5pWw5a2X5Li66Zi/5ouJ5Lyv5pWw5a2XDQogICAgICBsZXQgbGluZXMgPSBvdXQuc3BsaXQoJ1xuJykubWFwKGxpbmUgPT4gdGhpcy5yZXBsYWNlQ2hpbmVzZU51bWJlcihsaW5lKSk7DQogICAgICBsaW5lcyA9IHRoaXMuYXV0b0NvbnZlcnRDaGluZXNlTW9uZXkobGluZXMpOw0KICAgICAgdGhpcy5mb3JtYXRPdXRwdXQgPSBsaW5lcy5maWx0ZXIobGluZSA9PiBsaW5lKS5qb2luKCdcbicpOw0KICAgIH0sDQogICAgaW5zZXJ0Rm9ybWF0UmVzdWx0KCkgew0KICAgICAgdGhpcy5mb3JtRGF0YS5zaGliaWUgPSB0aGlzLmZvcm1hdE91dHB1dDsNCiAgICAgIHRoaXMuZm9ybWF0RGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgdGhpcy5oYW5kbGVOdW1iZXJSZWNvZ25pdGlvbih0aGlzLmZvcm1hdE91dHB1dCk7DQogICAgfSwNCiAgICBoYW5kbGVDb2xvbkZvcm1hdElucHV0KCkgew0KICAgICAgLy8g5YaS5Y+3dGFi77ya5omA5pyJ5YaS5Y+357G75a2X56ym6L2s5Li6LQ0KICAgICAgbGV0IG91dCA9ICh0aGlzLmNvbG9uRm9ybWF0SW5wdXQgfHwgJycpLnJlcGxhY2UoL1vvvJo6XS9nLCAnLScpOw0KICAgICAgLy8g6L+e57ut5aSa5Liq55+t5qiq5ZCI5bm25Li65LiA5LiqDQogICAgICBvdXQgPSBvdXQucmVwbGFjZSgvLSsvZywgJy0nKTsNCiAgICAgIC8vIOWFiOabv+aNouaJgOacieaxieWtl+aVsOWtl+S4uumYv+aLieS8r+aVsOWtlw0KICAgICAgbGV0IGxpbmVzID0gb3V0LnNwbGl0KCdcbicpLm1hcChsaW5lID0+IHRoaXMucmVwbGFjZUNoaW5lc2VOdW1iZXIobGluZSkpOw0KICAgICAgbGluZXMgPSB0aGlzLmF1dG9Db252ZXJ0Q2hpbmVzZU1vbmV5KGxpbmVzKTsNCiAgICAgIHRoaXMuY29sb25Gb3JtYXRPdXRwdXQgPSBsaW5lcy5maWx0ZXIobGluZSA9PiBsaW5lKS5qb2luKCdcbicpOw0KICAgIH0sDQogICAgaW5zZXJ0Q29sb25Gb3JtYXRSZXN1bHQoKSB7DQogICAgICB0aGlzLmZvcm1EYXRhLnNoaWJpZSA9IHRoaXMuY29sb25Gb3JtYXRPdXRwdXQ7DQogICAgICB0aGlzLmZvcm1hdERpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICAgIHRoaXMuaGFuZGxlTnVtYmVyUmVjb2duaXRpb24odGhpcy5jb2xvbkZvcm1hdE91dHB1dCk7DQogICAgfSwNCg0KICAgIGhhbmRsZU1vbmV5Rm9ybWF0SW5wdXQoKSB7DQogICAgICAvLyDlj6rlgZrmsYnlrZfph5Hpop3ovazmlbDlrZfvvIzkuJTlj6rmm7/mjaLmnIDlkI7kuIDkuKrmsYnlrZfph5Hpop3kuLov5pWw5a2XDQogICAgICBsZXQgbGluZXMgPSAodGhpcy5tb25leUZvcm1hdElucHV0IHx8ICcnKS5zcGxpdCgnXG4nKS5tYXAobGluZSA9PiB0aGlzLnJlcGxhY2VDaGluZXNlTnVtYmVyKGxpbmUpKTsNCiAgICAgIGNvbnN0IGNoaW5lc2VNb25leVJlZyA9IC8oW+S4gOS6jOS4ieWbm+S6lOWFreS4g+WFq+S5neWNgeeZvuWNg+S4h+S4pOmbtl0rKSjlhYN85Z2XfOexsyk/L2c7DQogICAgICBsaW5lcyA9IGxpbmVzLm1hcChsaW5lID0+IHsNCiAgICAgICAgbGV0IGxhc3RNYXRjaDsNCiAgICAgICAgbGV0IG1hdGNoOw0KICAgICAgICB3aGlsZSAoKG1hdGNoID0gY2hpbmVzZU1vbmV5UmVnLmV4ZWMobGluZSkpICE9PSBudWxsKSB7DQogICAgICAgICAgbGFzdE1hdGNoID0gbWF0Y2g7DQogICAgICAgIH0NCiAgICAgICAgaWYgKGxhc3RNYXRjaCkgew0KICAgICAgICAgIGNvbnN0IG51bSA9IHRoaXMuY2hpbmVzZVRvTnVtYmVyKGxhc3RNYXRjaFsxXSk7DQogICAgICAgICAgY29uc3QgdW5pdCA9IGxhc3RNYXRjaFsyXSB8fCAnJzsNCiAgICAgICAgICBjb25zdCBzdGFydCA9IGxhc3RNYXRjaC5pbmRleDsNCiAgICAgICAgICBjb25zdCBlbmQgPSBzdGFydCArIGxhc3RNYXRjaFswXS5sZW5ndGg7DQogICAgICAgICAgbGV0IHByZWZpeCA9IGxpbmUuc2xpY2UoMCwgc3RhcnQpOw0KICAgICAgICAgIGlmICghL1tcL1xzLO+8jC7jgIItXSskLy50ZXN0KHByZWZpeCkpIHsNCiAgICAgICAgICAgIHByZWZpeCArPSAnLyc7DQogICAgICAgICAgfQ0KICAgICAgICAgIHJldHVybiBwcmVmaXggKyBudW0gKyB1bml0ICsgbGluZS5zbGljZShlbmQpOw0KICAgICAgICB9DQogICAgICAgIHJldHVybiBsaW5lOw0KICAgICAgfSk7DQogICAgICB0aGlzLm1vbmV5Rm9ybWF0T3V0cHV0ID0gbGluZXMuZmlsdGVyKGxpbmUgPT4gbGluZSkuam9pbignXG4nKTsNCiAgICB9LA0KICAgIGluc2VydE1vbmV5Rm9ybWF0UmVzdWx0KCkgew0KICAgICAgdGhpcy5mb3JtRGF0YS5zaGliaWUgPSB0aGlzLm1vbmV5Rm9ybWF0T3V0cHV0Ow0KICAgICAgdGhpcy5mb3JtYXREaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICB0aGlzLmhhbmRsZU51bWJlclJlY29nbml0aW9uKHRoaXMubW9uZXlGb3JtYXRPdXRwdXQpOw0KICAgIH0sDQogICAgaGFuZGxlVW5pZmllZEZvcm1hdElucHV0KCkgew0KICAgICAgLy8g57uf5LiA5a2X56ym6L2s5o2i77ya5pm66IO96K+G5Yir5bm26L2s5o2i5ZCE56eN5YiG6ZqU56ym5ZKM5rGJ5a2X6YeR6aKdDQogICAgICBsZXQgb3V0ID0gdGhpcy51bmlmaWVkRm9ybWF0SW5wdXQgfHwgJyc7DQoNCiAgICAgIC8vIDEuIOWFiOWkhOeQhuaxieWtl+mHkeminei9rOaNou+8iOWcqOWFtuS7lui9rOaNouS5i+WJje+8iQ0KICAgICAgbGV0IGxpbmVzID0gb3V0LnNwbGl0KCdcbicpLm1hcChsaW5lID0+IGxpbmUudHJpbSgpKS5maWx0ZXIoQm9vbGVhbik7DQogICAgICBsaW5lcyA9IHRoaXMuYXV0b0NvbnZlcnRDaGluZXNlTW9uZXkobGluZXMpOw0KICAgICAgb3V0ID0gbGluZXMuam9pbignXG4nKTsNCg0KICAgICAgLy8gMi4g5pu/5o2i5Ymp5L2Z55qE5rGJ5a2X5pWw5a2X5Li66Zi/5ouJ5Lyv5pWw5a2X77yI5LiN5YyF5ous5bey6L2s5o2i55qE6YeR6aKd77yJDQogICAgICBvdXQgPSB0aGlzLnJlcGxhY2VDaGluZXNlTnVtYmVyKG91dCk7DQoNCiAgICAgIC8vIDMuIOi9rOaNouWQhOenjeWIhumalOespuS4uuepuuagvO+8iOaUr+aMgeS4gOS4quaIluWkmuS4qui/nue7reWtl+espu+8iQ0KICAgICAgLy8g6YCX5Y+344CB5Y+l5Y+344CB5YaS5Y+344CB5LiL5YiS57q/6YO96L2s5Li656m65qC8DQogICAgICBvdXQgPSBvdXQucmVwbGFjZSgvWyzvvIxdKy9nLCAnICcpOyAgICAgICAgLy8g6YCX5Y+377yI5Lit6Iux5paH77yJDQogICAgICBvdXQgPSBvdXQucmVwbGFjZSgvW+OAgu+8ji5dKy9nLCAnICcpOyAgICAgICAvLyDlj6Xlj7fvvIjkuK3oi7HmlofvvIkNCiAgICAgIG91dCA9IG91dC5yZXBsYWNlKC9b77yaOl0rL2csICcgJyk7ICAgICAgICAvLyDlhpLlj7fvvIjkuK3oi7HmlofvvIkNCiAgICAgIG91dCA9IG91dC5yZXBsYWNlKC9fKy9nLCAnICcpOyAgICAgICAgICAgIC8vIOS4i+WIkue6vw0KDQogICAgICAvLyA0LiDlpITnkIbliqDlj7fnsbvlrZfnrKbvvIjovazmjaLkuLrnqbrmoLzvvIkNCiAgICAgIG91dCA9IG91dC5yZXBsYWNlKC9b4p6V77yL77mi4pya4pyb4pyc4pyZ4p2H77iP4p2M4p2OXSsvZywgJyAnKTsNCg0KICAgICAgLy8gNS4g5ZCI5bm25aSa5Liq6L+e57ut56m65qC85Li65LiA5LiqDQogICAgICBvdXQgPSBvdXQucmVwbGFjZSgvXHMrL2csICcgJyk7DQoNCiAgICAgIHRoaXMudW5pZmllZEZvcm1hdE91dHB1dCA9IG91dC5zcGxpdCgnXG4nKS5tYXAobGluZSA9PiBsaW5lLnRyaW0oKSkuZmlsdGVyKEJvb2xlYW4pLmpvaW4oJ1xuJyk7DQogICAgfSwNCiAgICBpbnNlcnRVbmlmaWVkRm9ybWF0UmVzdWx0KCkgew0KICAgICAgdGhpcy5mb3JtRGF0YS5zaGliaWUgPSB0aGlzLnVuaWZpZWRGb3JtYXRPdXRwdXQ7DQogICAgICB0aGlzLmZvcm1hdERpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICAgIHRoaXMuaGFuZGxlTnVtYmVyUmVjb2duaXRpb24odGhpcy51bmlmaWVkRm9ybWF0T3V0cHV0KTsNCiAgICB9LA0KICAgIGhhbmRsZUNoYWluRm9ybWF0SW5wdXQoKSB7DQogICAgICAvLyDpk77lrZDliIbnsbvvvJrmjInlj7fnoIHplb/luqbliIbnu4TliLDkuI3lkIzooYwNCiAgICAgIGxldCBpbnB1dCA9IHRoaXMuY2hhaW5Gb3JtYXRJbnB1dCB8fCAnJzsNCg0KICAgICAgLy8gMS4g5YWI6L+b6KGM5Z+656GA55qE5a2X56ym6L2s5o2iDQogICAgICBpbnB1dCA9IHRoaXMucmVwbGFjZUNoaW5lc2VOdW1iZXIoaW5wdXQpOw0KDQogICAgICAvLyAyLiDmj5Dlj5bmiYDmnInmlbDlrZfvvIjmlK/mjIHlkITnp43liIbpmpTnrKbvvIkNCiAgICAgIC8vIOS9v+eUqOato+WImeWMuemFjeaJgOaciei/nue7reeahOaVsOWtlw0KICAgICAgY29uc3QgbnVtYmVyUGF0dGVybiA9IC9cZCsvZzsNCiAgICAgIGNvbnN0IG51bWJlcnMgPSBpbnB1dC5tYXRjaChudW1iZXJQYXR0ZXJuKSB8fCBbXTsNCg0KICAgICAgLy8gMy4g5oyJ6ZW/5bqm5YiG57uE77yM5ZCM5pe26K6w5b2V5q+P5Liq6ZW/5bqm6aaW5qyh5Ye6546w55qE5L2N572uDQogICAgICBjb25zdCBsZW5ndGhHcm91cHMgPSB7fTsNCiAgICAgIGNvbnN0IGxlbmd0aEZpcnN0SW5kZXggPSB7fTsNCg0KICAgICAgbnVtYmVycy5mb3JFYWNoKChudW0sIGluZGV4KSA9PiB7DQogICAgICAgIGNvbnN0IGxlbmd0aCA9IG51bS5sZW5ndGg7DQogICAgICAgIGlmICghbGVuZ3RoR3JvdXBzW2xlbmd0aF0pIHsNCiAgICAgICAgICBsZW5ndGhHcm91cHNbbGVuZ3RoXSA9IFtdOw0KICAgICAgICAgIGxlbmd0aEZpcnN0SW5kZXhbbGVuZ3RoXSA9IGluZGV4OyAvLyDorrDlvZXor6Xplb/luqbpppbmrKHlh7rnjrDnmoTkvY3nva4NCiAgICAgICAgfQ0KICAgICAgICBsZW5ndGhHcm91cHNbbGVuZ3RoXS5wdXNoKG51bSk7DQogICAgICB9KTsNCg0KICAgICAgLy8gNC4g5oyJ6aaW5qyh5Ye6546w55qE6aG65bqP5o6S5bqP5bm255Sf5oiQ6L6T5Ye6DQogICAgICBjb25zdCBzb3J0ZWRMZW5ndGhzID0gT2JqZWN0LmtleXMobGVuZ3RoR3JvdXBzKS5zb3J0KChhLCBiKSA9PiBsZW5ndGhGaXJzdEluZGV4W2FdIC0gbGVuZ3RoRmlyc3RJbmRleFtiXSk7DQogICAgICBjb25zdCByZXN1bHRMaW5lcyA9IFtdOw0KDQogICAgICBzb3J0ZWRMZW5ndGhzLmZvckVhY2gobGVuZ3RoID0+IHsNCiAgICAgICAgY29uc3QgbnVtcyA9IGxlbmd0aEdyb3Vwc1tsZW5ndGhdOw0KICAgICAgICBpZiAobnVtcy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgLy8g5q+P6KGM5qC85byP77ya55u05o6l5pi+56S65Y+356CB5YiX6KGo77yM5LiN5Yqg6ZW/5bqm5qCH6K+GDQogICAgICAgICAgY29uc3QgbGluZSA9IG51bXMuam9pbignICcpOw0KICAgICAgICAgIHJlc3VsdExpbmVzLnB1c2gobGluZSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KDQogICAgICB0aGlzLmNoYWluRm9ybWF0T3V0cHV0ID0gcmVzdWx0TGluZXMuam9pbignXG4nKTsNCiAgICB9LA0KICAgIGluc2VydENoYWluRm9ybWF0UmVzdWx0KCkgew0KICAgICAgdGhpcy5mb3JtRGF0YS5zaGliaWUgPSB0aGlzLmNoYWluRm9ybWF0T3V0cHV0Ow0KICAgICAgdGhpcy5mb3JtYXREaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICB0aGlzLmhhbmRsZU51bWJlclJlY29nbml0aW9uKHRoaXMuY2hhaW5Gb3JtYXRPdXRwdXQpOw0KICAgIH0sDQogICAgaGFuZGxlQW1vdW50Rm9ybWF0SW5wdXQoKSB7DQogICAgICAvLyDph5Hpop3mlbTlkIjvvJrlsIbnm7jlkIzph5Hpop3kuJTnm7jlkIzkvY3mlbDnmoTlj7fnoIHlkIjlubbliLDkuIDooYwNCiAgICAgIC8vIOaUr+aMgeavj+ihjOS4gOS4quWPt+egge+8jOS5n+aUr+aMgeS4gOihjOWGheeUqOepuuagvOWIhumalOeahOWkmuS4quWPt+eggQ0KICAgICAgY29uc3QgaW5wdXQgPSB0aGlzLmFtb3VudEZvcm1hdElucHV0IHx8ICcnOw0KICAgICAgY29uc3QgbGluZXMgPSBpbnB1dC5zcGxpdCgnXG4nKS5maWx0ZXIobGluZSA9PiBsaW5lLnRyaW0oKSk7DQoNCiAgICAgIC8vIOino+aekOavj+ihjOeahOWPt+eggeWSjOmHkeminQ0KICAgICAgY29uc3QgZ3JvdXBzID0ge307DQogICAgICBjb25zdCBncm91cE9yZGVyID0gW107IC8vIOiusOW9leWIhue7hOmmluasoeWHuueOsOeahOmhuuW6jw0KDQogICAgICBsaW5lcy5mb3JFYWNoKGxpbmUgPT4gew0KICAgICAgICBjb25zdCB0cmltbWVkTGluZSA9IGxpbmUudHJpbSgpOw0KICAgICAgICBpZiAoIXRyaW1tZWRMaW5lKSByZXR1cm47DQoNCiAgICAgICAgLy8g5YWI5bCd6K+V5oyJ56m65qC85YiG5Ymy77yM55yL5piv5ZCm5pyJ5aSa5Liq5Y+356CBLemHkemineWvuQ0KICAgICAgICBjb25zdCBwYXJ0cyA9IHRyaW1tZWRMaW5lLnNwbGl0KC9ccysvKS5maWx0ZXIocGFydCA9PiBwYXJ0LnRyaW0oKSk7DQoNCiAgICAgICAgLy8g5aaC5p6c5Y+q5pyJ5LiA5Liq6YOo5YiG77yM5oyJ5Y6f5p2l55qE6YC76L6R5aSE55CGDQogICAgICAgIGlmIChwYXJ0cy5sZW5ndGggPT09IDEpIHsNCiAgICAgICAgICB0aGlzLnByb2Nlc3NBbW91bnRGb3JtYXRJdGVtKHBhcnRzWzBdLCBncm91cHMsIGdyb3VwT3JkZXIpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOWmguaenOacieWkmuS4qumDqOWIhu+8jOmAkOS4quWkhOeQhuavj+S4quWPt+eggS3ph5Hpop3lr7kNCiAgICAgICAgICBwYXJ0cy5mb3JFYWNoKHBhcnQgPT4gew0KICAgICAgICAgICAgdGhpcy5wcm9jZXNzQW1vdW50Rm9ybWF0SXRlbShwYXJ0LCBncm91cHMsIGdyb3VwT3JkZXIpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCg0KICAgICAgLy8g5oyJ6aaW5qyh5Ye6546w55qE6aG65bqP55Sf5oiQ6L6T5Ye6DQogICAgICBjb25zdCByZXN1bHRMaW5lcyA9IFtdOw0KICAgICAgZ3JvdXBPcmRlci5mb3JFYWNoKGdyb3VwS2V5ID0+IHsNCiAgICAgICAgY29uc3QgZ3JvdXAgPSBncm91cHNbZ3JvdXBLZXldOw0KICAgICAgICBpZiAoZ3JvdXAgJiYgZ3JvdXAubnVtYmVycy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgLy8g5ZCI5bm255u45ZCM6YeR6aKd5ZKM5L2N5pWw55qE5Y+356CB5Yiw5LiA6KGMDQogICAgICAgICAgY29uc3QgbGluZSA9IGdyb3VwLm51bWJlcnMuam9pbignLScpICsgJy0nICsgZ3JvdXAuYW1vdW50Ow0KICAgICAgICAgIHJlc3VsdExpbmVzLnB1c2gobGluZSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KDQogICAgICB0aGlzLmFtb3VudEZvcm1hdE91dHB1dCA9IHJlc3VsdExpbmVzLmpvaW4oJ1xuJyk7DQogICAgfSwNCg0KICAgIC8vIOaWsOWinui+heWKqeWHveaVsO+8muWkhOeQhuWNleS4quWPt+eggS3ph5Hpop3pobkNCiAgICBwcm9jZXNzQW1vdW50Rm9ybWF0SXRlbShpdGVtLCBncm91cHMsIGdyb3VwT3JkZXIpIHsNCiAgICAgIGNvbnN0IHRyaW1tZWRJdGVtID0gaXRlbS50cmltKCk7DQogICAgICBpZiAoIXRyaW1tZWRJdGVtKSByZXR1cm47DQoNCiAgICAgIC8vIOafpeaJvuacgOWQjuS4gOS4quefreaoque6v++8jOWIhuemu+WPt+eggeWSjOmHkeminQ0KICAgICAgY29uc3QgbGFzdERhc2hJbmRleCA9IHRyaW1tZWRJdGVtLmxhc3RJbmRleE9mKCctJyk7DQogICAgICBpZiAobGFzdERhc2hJbmRleCA9PT0gLTEpIHJldHVybjsgLy8g5rKh5pyJ5om+5Yiw5YiG6ZqU56ym77yM6Lez6L+HDQoNCiAgICAgIGNvbnN0IG51bWJlciA9IHRyaW1tZWRJdGVtLnN1YnN0cmluZygwLCBsYXN0RGFzaEluZGV4KS50cmltKCk7DQogICAgICBjb25zdCBhbW91bnQgPSB0cmltbWVkSXRlbS5zdWJzdHJpbmcobGFzdERhc2hJbmRleCArIDEpLnRyaW0oKTsNCg0KICAgICAgaWYgKCFudW1iZXIgfHwgIWFtb3VudCkgcmV0dXJuOyAvLyDlj7fnoIHmiJbph5Hpop3kuLrnqbrvvIzot7Pov4cNCg0KICAgICAgLy8g5oyJ6YeR6aKd5ZKM5Y+356CB5L2N5pWw5YiG57uEDQogICAgICBjb25zdCBudW1iZXJMZW5ndGggPSBudW1iZXIubGVuZ3RoOw0KICAgICAgY29uc3QgZ3JvdXBLZXkgPSBgJHthbW91bnR9XyR7bnVtYmVyTGVuZ3RofWA7IC8vIOmHkeminV/kvY3mlbDkvZzkuLrliIbnu4TplK4NCg0KICAgICAgaWYgKCFncm91cHNbZ3JvdXBLZXldKSB7DQogICAgICAgIGdyb3Vwc1tncm91cEtleV0gPSB7DQogICAgICAgICAgYW1vdW50OiBhbW91bnQsDQogICAgICAgICAgbGVuZ3RoOiBudW1iZXJMZW5ndGgsDQogICAgICAgICAgbnVtYmVyczogW10NCiAgICAgICAgfTsNCiAgICAgICAgZ3JvdXBPcmRlci5wdXNoKGdyb3VwS2V5KTsgLy8g6K6w5b2V5YiG57uE6aaW5qyh5Ye6546w55qE6aG65bqPDQogICAgICB9DQogICAgICBncm91cHNbZ3JvdXBLZXldLm51bWJlcnMucHVzaChudW1iZXIpOw0KICAgIH0sDQogICAgaW5zZXJ0QW1vdW50Rm9ybWF0UmVzdWx0KCkgew0KICAgICAgdGhpcy5mb3JtRGF0YS5zaGliaWUgPSB0aGlzLmFtb3VudEZvcm1hdE91dHB1dDsNCiAgICAgIHRoaXMuZm9ybWF0RGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgdGhpcy5oYW5kbGVOdW1iZXJSZWNvZ25pdGlvbih0aGlzLmFtb3VudEZvcm1hdE91dHB1dCk7DQogICAgfSwNCiAgICBoYW5kbGVSZW1vdmVGb3JtYXRJbnB1dCgpIHsNCiAgICAgIC8vIOWOu+mZpOWIhumalOespuWSjOiLseaWh+Wtl+avje+8muWPquS/neeVmeaxieWtl+OAgeaVsOWtl+WSjOaNouihjA0KICAgICAgY29uc3QgaW5wdXQgPSB0aGlzLnJlbW92ZUZvcm1hdElucHV0IHx8ICcnOw0KDQogICAgICAvLyDkvb/nlKjmraPliJnooajovr7lvI/lj6rkv53nlZnmsYnlrZfjgIHmlbDlrZflkozmjaLooYznrKYNCiAgICAgIC8vIFx1NGUwMC1cdTlmZmYg5Yy56YWN5rGJ5a2XDQogICAgICAvLyBcZCDljLnphY3mlbDlrZcNCiAgICAgIC8vIFxuXHIg5Yy56YWN5o2i6KGM56ymDQogICAgICAvLyDljrvpmaTmiYDmnInoi7HmloflrZfmr43vvIjlpKflsI/lhpnvvInlkozlhbbku5blrZfnrKYNCiAgICAgIGNvbnN0IG91dHB1dCA9IGlucHV0LnJlcGxhY2UoL1teXHU0ZTAwLVx1OWZmZlxkXG5ccl0vZywgJycpOw0KDQogICAgICAvLyDmuIXnkIblpJrkvZnnmoTnqbrooYzvvIzkvYbkv53nlZnlv4XopoHnmoTmjaLooYwNCiAgICAgIHRoaXMucmVtb3ZlRm9ybWF0T3V0cHV0ID0gb3V0cHV0LnNwbGl0KCdcbicpDQogICAgICAgIC5tYXAobGluZSA9PiBsaW5lLnRyaW0oKSkNCiAgICAgICAgLmZpbHRlcigobGluZSwgaW5kZXgsIGFycikgPT4gew0KICAgICAgICAgIC8vIOS/neeVmemdnuepuuihjO+8jOS7peWPiuWJjeS4gOihjOmdnuepuueahOepuuihjO+8iOS9nOS4uuWIhumalO+8iQ0KICAgICAgICAgIHJldHVybiBsaW5lIHx8IChpbmRleCA+IDAgJiYgYXJyW2luZGV4IC0gMV0pOw0KICAgICAgICB9KQ0KICAgICAgICAuam9pbignXG4nKQ0KICAgICAgICAucmVwbGFjZSgvXG57Myx9L2csICdcblxuJyk7IC8vIOacgOWkmuS/neeVmeS4pOS4qui/nue7reaNouihjA0KICAgIH0sDQogICAgaW5zZXJ0UmVtb3ZlRm9ybWF0UmVzdWx0KCkgew0KICAgICAgdGhpcy5mb3JtRGF0YS5zaGliaWUgPSB0aGlzLnJlbW92ZUZvcm1hdE91dHB1dDsNCiAgICAgIHRoaXMuZm9ybWF0RGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgdGhpcy5oYW5kbGVOdW1iZXJSZWNvZ25pdGlvbih0aGlzLnJlbW92ZUZvcm1hdE91dHB1dCk7DQogICAgfSwNCiAgICBoYW5kbGVVbmRlcnNjb3JlRm9ybWF0SW5wdXQoKSB7DQogICAgICAvLyDkuIvliJLnur90YWLvvJrlsIbkuIDkuKrmiJblpJrkuKrov57nu63nmoTkuIvliJLnur/ovazmjaLkuLrnqbrmoLwNCiAgICAgIGxldCBvdXQgPSAodGhpcy51bmRlcnNjb3JlRm9ybWF0SW5wdXQgfHwgJycpLnJlcGxhY2UoL18rL2csICcgJyk7DQogICAgICAvLyDlhYjmm7/mjaLmiYDmnInmsYnlrZfmlbDlrZfkuLrpmL/mi4nkvK/mlbDlrZcNCiAgICAgIGxldCBsaW5lcyA9IG91dC5zcGxpdCgnXG4nKS5tYXAobGluZSA9PiB0aGlzLnJlcGxhY2VDaGluZXNlTnVtYmVyKGxpbmUpKTsNCiAgICAgIGxpbmVzID0gdGhpcy5hdXRvQ29udmVydENoaW5lc2VNb25leShsaW5lcyk7DQogICAgICB0aGlzLnVuZGVyc2NvcmVGb3JtYXRPdXRwdXQgPSBsaW5lcy5maWx0ZXIobGluZSA9PiBsaW5lKS5qb2luKCdcbicpOw0KICAgIH0sDQogICAgaW5zZXJ0VW5kZXJzY29yZUZvcm1hdFJlc3VsdCgpIHsNCiAgICAgIHRoaXMuZm9ybURhdGEuc2hpYmllID0gdGhpcy51bmRlcnNjb3JlRm9ybWF0T3V0cHV0Ow0KICAgICAgdGhpcy5mb3JtYXREaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICB0aGlzLmhhbmRsZU51bWJlclJlY29nbml0aW9uKHRoaXMudW5kZXJzY29yZUZvcm1hdE91dHB1dCk7DQogICAgfSwNCiAgICAvLyDlt6Xlhbflh73mlbDvvJrmm7/mjaLmiYDmnInmsYnlrZfmlbDlrZfkuLrpmL/mi4nkvK/mlbDlrZcNCiAgICByZXBsYWNlQ2hpbmVzZU51bWJlcihzdHIpIHsNCiAgICAgIC8vIOWFiOWkhOeQhuWujOaVtOeahOS4reaWh+aVsOWtl++8iOWMheaLrOWNleS9je+8ie+8jOeEtuWQjuWGjeWkhOeQhuWNleS4quaxieWtl+aVsOWtlw0KICAgICAgLy8g5Yy56YWN5a6M5pW055qE5Lit5paH5pWw5a2X6KGo6L6+5byP77yM5aaCIuS6jOWNgeS6lCLjgIEi5LiA55m+5LqM5Y2B5LiJIuetiQ0KICAgICAgY29uc3QgY2hpbmVzZU51bWJlclBhdHRlcm4gPSAvW+mbtuS4gOS6jOS4pOS4ieWbm+S6lOWFreS4g+WFq+S5neWNgeeZvuWNg+S4h10rL2c7DQoNCiAgICAgIHJldHVybiBzdHIucmVwbGFjZShjaGluZXNlTnVtYmVyUGF0dGVybiwgKG1hdGNoKSA9PiB7DQogICAgICAgIC8vIOWmguaenOWMuemFjeeahOaYr+WNleS4quWtl+espuS4lOS4jeWMheWQq+WNleS9je+8jOS9v+eUqOeugOWNleaYoOWwhA0KICAgICAgICBpZiAobWF0Y2gubGVuZ3RoID09PSAxICYmICEvW+WNgeeZvuWNg+S4h10vLnRlc3QobWF0Y2gpKSB7DQogICAgICAgICAgY29uc3QgbWFwID0geyAn6Zu2JzowLCfkuIAnOjEsJ+S6jCc6Miwn5LikJzoyLCfkuIknOjMsJ+Wbmyc6NCwn5LqUJzo1LCflha0nOjYsJ+S4gyc6Nywn5YWrJzo4LCfkuZ0nOjkgfTsNCiAgICAgICAgICByZXR1cm4gbWFwW21hdGNoXSB8fCBtYXRjaDsNCiAgICAgICAgfQ0KICAgICAgICAvLyDlkKbliJnkvb/nlKjlrozmlbTnmoTkuK3mlofmlbDlrZfovazmjaINCiAgICAgICAgcmV0dXJuIHRoaXMuY2hpbmVzZVRvTnVtYmVyKG1hdGNoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgY2hpbmVzZVRvTnVtYmVyKGNoaW5lc2UpIHsNCiAgICAgIGlmICghY2hpbmVzZSkgcmV0dXJuIDA7DQoNCiAgICAgIC8vIOWFiOWIpOaWreaYr+WQpuS4uue6r+aVsOWtlw0KICAgICAgaWYgKC9eXGQrJC8udGVzdChjaGluZXNlKSkgew0KICAgICAgICByZXR1cm4gcGFyc2VJbnQoY2hpbmVzZSwgMTApOw0KICAgICAgfQ0KDQogICAgICAvLyDnp7vpmaTljZXkvY0NCiAgICAgIGNvbnN0IHN0ciA9IGNoaW5lc2UucmVwbGFjZSgv5YWDfOWdl3znsbMvZywgJycpOw0KDQogICAgICAvLyDmlbDlrZfmmKDlsIQNCiAgICAgIGNvbnN0IG51bU1hcCA9IHsNCiAgICAgICAgJ+mbtic6IDAsICfkuIAnOiAxLCAn5LqMJzogMiwgJ+S4pCc6IDIsICfkuIknOiAzLCAn5ZubJzogNCwNCiAgICAgICAgJ+S6lCc6IDUsICflha0nOiA2LCAn5LiDJzogNywgJ+WFqyc6IDgsICfkuZ0nOiA5DQogICAgICB9Ow0KDQogICAgICAvLyDljZXkvY3mmKDlsIQNCiAgICAgIGNvbnN0IHVuaXRNYXAgPSB7ICfljYEnOiAxMCwgJ+eZvic6IDEwMCwgJ+WNgyc6IDEwMDAsICfkuIcnOiAxMDAwMCB9Ow0KDQogICAgICBsZXQgcmVzdWx0ID0gMDsNCiAgICAgIGxldCB0ZW1wID0gMDsNCiAgICAgIGxldCBoYXNOdW0gPSBmYWxzZTsNCg0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzdHIubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgY29uc3QgY2hhciA9IHN0cltpXTsNCg0KICAgICAgICBpZiAobnVtTWFwLmhhc093blByb3BlcnR5KGNoYXIpKSB7DQogICAgICAgICAgLy8g5piv5pWw5a2XDQogICAgICAgICAgdGVtcCA9IG51bU1hcFtjaGFyXTsNCiAgICAgICAgICBoYXNOdW0gPSB0cnVlOw0KICAgICAgICB9IGVsc2UgaWYgKHVuaXRNYXAuaGFzT3duUHJvcGVydHkoY2hhcikpIHsNCiAgICAgICAgICAvLyDmmK/ljZXkvY0NCiAgICAgICAgICBjb25zdCB1bml0ID0gdW5pdE1hcFtjaGFyXTsNCg0KICAgICAgICAgIGlmIChjaGFyID09PSAn5Y2BJyAmJiAhaGFzTnVtKSB7DQogICAgICAgICAgICAvLyAi5Y2BIuWJjemdouayoeacieaVsOWtl++8jOWmgiLljYHkupQi77yM5Y2B5YmN6Z2i6buY6K6k5Li6MQ0KICAgICAgICAgICAgdGVtcCA9IDE7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgaWYgKHVuaXQgPT09IDEwMDAwKSB7DQogICAgICAgICAgICAvLyDkuIcNCiAgICAgICAgICAgIHJlc3VsdCA9IChyZXN1bHQgKyB0ZW1wKSAqIHVuaXQ7DQogICAgICAgICAgICB0ZW1wID0gMDsNCiAgICAgICAgICB9IGVsc2UgaWYgKHVuaXQgPj0gMTAwKSB7DQogICAgICAgICAgICAvLyDnmb7jgIHljYMNCiAgICAgICAgICAgIHJlc3VsdCArPSB0ZW1wICogdW5pdDsNCiAgICAgICAgICAgIHRlbXAgPSAwOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyDljYENCiAgICAgICAgICAgIHJlc3VsdCArPSB0ZW1wICogdW5pdDsNCiAgICAgICAgICAgIHRlbXAgPSAwOw0KICAgICAgICAgIH0NCiAgICAgICAgICBoYXNOdW0gPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAvLyDlpITnkIbmnIDlkI7liankvZnnmoTmlbDlrZcNCiAgICAgIHJlc3VsdCArPSB0ZW1wOw0KDQogICAgICByZXR1cm4gcmVzdWx0Ow0KICAgIH0sDQogICAgYXV0b0NvbnZlcnRDaGluZXNlTW9uZXkobGluZXMpIHsNCiAgICAgIGlmICh0eXBlb2YgbGluZXMgPT09ICdzdHJpbmcnKSBsaW5lcyA9IGxpbmVzLnNwbGl0KC9cbi8pOw0KICAgICAgLy8g5pu057K+56Gu55qE5Lit5paH6YeR6aKd5q2j5YiZ77yM5pSv5oyB5aSN5p2C55qE5Lit5paH5pWw5a2XDQogICAgICBjb25zdCBjaGluZXNlTW9uZXlSZWcgPSAvKFvpm7bkuIDkuozkuInlm5vkupTlha3kuIPlhavkuZ3ljYHnmb7ljYPkuIfkuKRdKyko5YWDfOWdl3znsbMpPy9nOw0KICAgICAgcmV0dXJuIGxpbmVzLm1hcChsaW5lID0+IHsNCiAgICAgICAgLy8g5p+l5om+5omA5pyJ5rGJ5a2X6YeR6aKdDQogICAgICAgIGxldCBsYXN0TWF0Y2g7DQogICAgICAgIGxldCBtYXRjaDsNCiAgICAgICAgLy8g6YeN572u5q2j5YiZ55qEbGFzdEluZGV4DQogICAgICAgIGNoaW5lc2VNb25leVJlZy5sYXN0SW5kZXggPSAwOw0KICAgICAgICB3aGlsZSAoKG1hdGNoID0gY2hpbmVzZU1vbmV5UmVnLmV4ZWMobGluZSkpICE9PSBudWxsKSB7DQogICAgICAgICAgLy8g6aqM6K+B5Yy56YWN55qE5piv5ZCm55yf55qE5piv5pWw5a2X77yI5YyF5ZCr5pWw5a2X5a2X56ym5oiW5Y2V5L2N5a2X56ym77yJDQogICAgICAgICAgaWYgKC9b5LiA5LqM5LiJ5Zub5LqU5YWt5LiD5YWr5Lmd5Y2B55m+5Y2D5LiH5Lik6Zu2XS8udGVzdChtYXRjaFsxXSkpIHsNCiAgICAgICAgICAgIGxhc3RNYXRjaCA9IG1hdGNoOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBpZiAobGFzdE1hdGNoKSB7DQogICAgICAgICAgY29uc3QgbnVtID0gdGhpcy5jaGluZXNlVG9OdW1iZXIobGFzdE1hdGNoWzFdKTsNCiAgICAgICAgICBjb25zdCB1bml0ID0gbGFzdE1hdGNoWzJdIHx8ICcnOw0KICAgICAgICAgIC8vIOabv+aNouacgOWQjuS4gOS4quaxieWtl+mHkemineS4uuaVsOWtlw0KICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gbGFzdE1hdGNoLmluZGV4Ow0KICAgICAgICAgIGNvbnN0IGVuZCA9IHN0YXJ0ICsgbGFzdE1hdGNoWzBdLmxlbmd0aDsNCiAgICAgICAgICBsZXQgcHJlZml4ID0gbGluZS5zbGljZSgwLCBzdGFydCk7DQoNCiAgICAgICAgICAvLyDnoa7kv53msYnlrZfph5Hpop3liY3pnaLmnInnqbrmoLzvvIzpgb/lhY3lkozlj7fnoIHmt7fmt4YNCiAgICAgICAgICBpZiAocHJlZml4ICYmICEvW1xzXC8s77yMLuOAgi1dJC8udGVzdChwcmVmaXgpKSB7DQogICAgICAgICAgICBwcmVmaXggKz0gJyAnOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIHJldHVybiBwcmVmaXggKyBudW0gKyB1bml0ICsgbGluZS5zbGljZShlbmQpOw0KICAgICAgICB9DQogICAgICAgIHJldHVybiBsaW5lOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDliJ3lp4vljJbmnKznu4Tluo/lj7cNCiAgICBpbml0U2VyaWFsTnVtYmVyKCkgew0KICAgICAgLy8g5aaC5p6c5piv5L+u5pS55qih5byP77yI5pyJcm935pWw5o2u77yJ5LiU5bey5pyJ5rWB5rC05Y+377yM5LiN6KaB6YeN5paw5Yid5aeL5YyWDQogICAgICBpZiAodGhpcy5yb3cgJiYgdGhpcy5yb3cuc2VyaWFsTnVtYmVyKSB7DQoNCiAgICAgICAgdGhpcy5zZXJpYWxOdW1iZXIgPSB0aGlzLnJvdy5zZXJpYWxOdW1iZXI7DQogICAgICAgIHRoaXMub3JpZ2luYWxTZXJpYWxOdW1iZXIgPSB0aGlzLnJvdy5zZXJpYWxOdW1iZXI7DQogICAgICAgIC8vIOS7jeeEtuiOt+WPluacgOWkp+a1geawtOWPt+eUqOS6jueUn+aIkOaWsOa1geawtOWPt+aXtuWPguiAgw0KICAgICAgICBnZXRNYXhTZXJpYWxOdW1iZXIoKS50aGVuKG1heCA9PiB7DQogICAgICAgICAgdGhpcy5tYXhTZXJpYWxOdW1iZXIgPSBtYXggPyBOdW1iZXIobWF4KSA6IDE7DQogICAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICB0aGlzLm1heFNlcmlhbE51bWJlciA9IDE7DQogICAgICAgIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOaWsOWinuaooeW8j+aIluayoeaciea1geawtOWPt+aXtu+8jOiOt+WPluacgOWkp+a1geawtOWPtw0KICAgICAgDQogICAgICBnZXRNYXhTZXJpYWxOdW1iZXIoKS50aGVuKG1heCA9PiB7DQogICAgICAgIGlmIChtYXggPT0gbnVsbCkgew0KICAgICAgICAgIHRoaXMubWF4U2VyaWFsTnVtYmVyID0gMTsNCiAgICAgICAgICB0aGlzLnNlcmlhbE51bWJlciA9IDE7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5tYXhTZXJpYWxOdW1iZXIgPSBOdW1iZXIobWF4KTsNCiAgICAgICAgICB0aGlzLnNlcmlhbE51bWJlciA9IE51bWJlcihtYXgpOw0KICAgICAgICB9DQogICAgICAgIA0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLm1heFNlcmlhbE51bWJlciA9IDE7DQogICAgICAgIHRoaXMuc2VyaWFsTnVtYmVyID0gMTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5aKe5Yqg5pys57uE5bqP5Y+377yM5Y+q6IO95Yqg5Yiw5pyA5aSnKzENCiAgICBpbmNyZWFzZVNlcmlhbE51bWJlcigpIHsNCiAgICAgIGNvbnN0IG5leHQgPSBOdW1iZXIodGhpcy5tYXhTZXJpYWxOdW1iZXIpICsgMTsNCiAgICAgIGlmICh0aGlzLnNlcmlhbE51bWJlciA8IG5leHQpIHsNCiAgICAgICAgdGhpcy5zZXJpYWxOdW1iZXIgPSBuZXh0Ow0KICAgICAgfQ0KICAgICAgLy8g5aaC5p6c5bey57uP5piv5pyA5aSnKzHvvIzliJnkuI3lho3lop7liqANCiAgICB9LA0KICAgIC8vIOeUn+aIkOaWsOeahOa1geawtOWPtw0KICAgIGdlbmVyYXRlTmV3U2VyaWFsTnVtYmVyKCkgew0KICAgICAgdGhpcy5nZW5lcmF0ZUxvYWRpbmcgPSB0cnVlOw0KICAgICAgZ2VuZXJhdGVTZXJpYWxOdW1iZXIoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuc2VyaWFsTnVtYmVyID0gcmVzcG9uc2Uuc2VyaWFsTnVtYmVyOw0KICAgICAgICAgIHRoaXMubWF4U2VyaWFsTnVtYmVyID0gcmVzcG9uc2Uuc2VyaWFsTnVtYmVyOw0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg55Sf5oiQ5paw5rWB5rC05Y+3OiAke3Jlc3BvbnNlLnNlcmlhbE51bWJlcn1gKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnlJ/miJDmtYHmsLTlj7flpLHotKU6ICcgKyByZXNwb25zZS5tc2cpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+eUn+aIkOa1geawtOWPt+Wksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+eUn+aIkOa1geawtOWPt+Wksei0pScpOw0KICAgICAgfSkuZmluYWxseSgoKSA9PiB7DQogICAgICAgIHRoaXMuZ2VuZXJhdGVMb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWkhOeQhueUqOaIt0lE5Y+Y5YyWDQogICAgaGFuZGxlVXNlcklkQ2hhbmdlKG5ld1VzZXJJZCwgb2xkVXNlcklkKSB7DQogICAgDQogICAgICBpZiAobmV3VXNlcklkID09PSB0aGlzLm9yaWdpbmFsVXNlcklkKSB7DQogICAgICAgIC8vIOaUueWbnuWOn+eUqOaIt++8jOaBouWkjeWOn+a1geawtOWPtw0KICAgICAgICB0aGlzLnNlcmlhbE51bWJlciA9IHRoaXMub3JpZ2luYWxTZXJpYWxOdW1iZXI7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbyhg5bey5oGi5aSN5Y6f5rWB5rC05Y+3OiAke3RoaXMub3JpZ2luYWxTZXJpYWxOdW1iZXJ9YCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDmlLnkuLrlhbbku5bnlKjmiLfvvIzoh6rliqjnlJ/miJDmlrDmtYHmsLTlj7cNCiAgICAgICAgdGhpcy5hdXRvR2VuZXJhdGVTZXJpYWxOdW1iZXIoKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOiHquWKqOeUn+aIkOa1geawtOWPt++8iOS4jeaYvuekuuaIkOWKn+a2iOaBr++8iQ0KICAgIGF1dG9HZW5lcmF0ZVNlcmlhbE51bWJlcigpIHsNCiAgICAgIGdlbmVyYXRlU2VyaWFsTnVtYmVyKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLnNlcmlhbE51bWJlciA9IHJlc3BvbnNlLnNlcmlhbE51bWJlcjsNCiAgICAgICAgICB0aGlzLm1heFNlcmlhbE51bWJlciA9IHJlc3BvbnNlLnNlcmlhbE51bWJlcjsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oYOeUqOaIt+WPmOabtO+8jOiHquWKqOeUn+aIkOaWsOa1geawtOWPtzogJHtyZXNwb25zZS5zZXJpYWxOdW1iZXJ9YCk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6Ieq5Yqo55Sf5oiQ5rWB5rC05Y+35aSx6LSlOiAnICsgcmVzcG9uc2UubXNnKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfoh6rliqjnlJ/miJDmtYHmsLTlj7flpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfoh6rliqjnlJ/miJDmtYHmsLTlj7flpLHotKUnKTsNCiAgICAgIH0pOw0KICAgIH0sDQogIH0NCn0NCg=="}, {"version": 3, "sources": ["BetDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BetDialog.vue", "sourceRoot": "src/views/game/record/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\" width=\"600px\" append-to-body\r\n      :close-on-click-modal=\"false\" class=\"modern-bet-dialog\" style=\"height: 108%;  top: -55px;\">\r\n\r\n      <el-form ref=\"form\" :model=\"formData\" :rules=\"rules\" label-width=\"80px\" class=\"modern-bet-form\">\r\n        <div class=\"info-card\">\r\n          <!-- 福体开关行 -->\r\n          <div class=\"info-row\">\r\n            <div class=\"info-group\">\r\n              <div class=\"info-label\">\r\n                <i class=\"el-icon-s-operation\"></i>\r\n                <span>福体开关</span>\r\n              </div>\r\n              <el-switch v-model=\"futiSwitch\" active-value=\"all\" inactive-value=\"tc\" active-text=\"全部彩种\" inactive-text=\"仅体彩\"\r\n                @change=\"onFutiSwitchChange\"\r\n                size=\"small\"\r\n                class=\"lottery-switch\" />\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 用户信息行 -->\r\n          <div class=\"info-row\">\r\n            <div class=\"info-group\">\r\n              <div class=\"info-label\">\r\n                <i class=\"el-icon-user\"></i>\r\n                <span>当前用户</span>\r\n              </div>\r\n              <el-input v-model=\"currentUser.name\" disabled placeholder=\"未获取\" class=\"compact-input\" style=\"width: 100px;\" />\r\n            </div>\r\n            <div class=\"info-group\">\r\n              <div class=\"info-label\">\r\n                <i class=\"el-icon-switch-button\"></i>\r\n                <span>切换用户</span>\r\n              </div>\r\n              <el-select v-model=\"selectedUserId\" placeholder=\"请选择用户\" class=\"compact-select\" style=\"width: 120px;\">\r\n                <el-option v-for=\"user in userList\" :key=\"user.userId\" :label=\"user.name\" :value=\"user.userId\" />\r\n              </el-select>\r\n              <el-button\r\n                type=\"primary\"\r\n                :disabled=\"!selectedUserId\"\r\n                @click=\"switchUser\"\r\n                v-if=\"!row\"\r\n                class=\"compact-btn\"\r\n              >切换</el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 流水号信息行 -->\r\n          <div class=\"info-row\">\r\n            <div class=\"info-group\">\r\n              <div class=\"info-label\">\r\n                <i class=\"el-icon-document\"></i>\r\n                <span>流水号</span>\r\n              </div>\r\n              <el-input v-model.number=\"serialNumber\" placeholder=\"流水号\" class=\"compact-input\" style=\"width: 120px;\" />\r\n              <el-button type=\"success\" @click=\"generateNewSerialNumber\" :loading=\"generateLoading\" class=\"compact-btn\">\r\n                <i class=\"el-icon-refresh\"></i>\r\n                生成新流水号\r\n              </el-button>\r\n              <el-button type=\"primary\" @click=\"showFormatDialog\" class=\"compact-btn\">\r\n                <i class=\"el-icon-s-operation\"></i>\r\n                格式转换工具\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"showStatisticsButton\"\r\n                type=\"success\"\r\n                @click=\"showStatistics\"\r\n                class=\"compact-btn\">\r\n                <i class=\"el-icon-data-analysis\"></i>\r\n                下注统计\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 隐藏的期号字段，用于后端传输 -->\r\n          <div style=\"display: none;\">\r\n            <el-input v-model=\"formData.fc3dIssueNumber\" />\r\n            <el-input v-model=\"formData.tcIssueNumber\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"recognition-section\">\r\n          <el-form-item label=\"号码识别\" prop=\"shibie\">\r\n            <div class=\"recognition-wrapper\">\r\n              <div class=\"recognition-header\">\r\n                <i class=\"el-icon-view\"></i>\r\n                <span v-if=\"!isNumberCountExceeded\">智能识别</span>\r\n                <span v-else-if=\"isFirstNumberThreeDigits(formData.shibie)\" style=\"color: #67c23a;\">首组为三位数，不限制组数 ({{ numberCount }}组)</span>\r\n                <span v-else style=\"color: #f56c6c;\">单次识别不可超过30组，你现在是{{ numberCount }}组</span>\r\n                <el-button size=\"mini\" type=\"primary\" @click=\"showBetFormatDialog\">玩法格式</el-button>\r\n              </div>\r\n              <el-input v-model=\"formData.shibie\" type=\"textarea\" :rows=\"4\"\r\n                placeholder=\"请将号码输入此处，识别后自动生成下注号码\"\r\n                @input=\"handleNumberRecognition\" size=\"small\" :resize=\"'vertical'\"\r\n                :class=\"['recognition-textarea', { 'number-count-exceeded': isNumberCountExceeded }]\" />\r\n            </div>\r\n          </el-form-item>\r\n        </div>\r\n\r\n        <div class=\"bet-groups-list-wrapper\">\r\n          <div class=\"bet-groups-list\">\r\n            <div v-for=\"(group, idx) in formData.betGroups\" :key=\"idx\"\r\n              :class=\"['bet-group-card', { 'bet-group-even': idx % 2 === 1 }]\">\r\n              <div style=\"display:flex;align-items:center;gap:8px;margin-bottom:8px;\">\r\n                <el-form-item :label=\"'玩法名称'\" :prop=\"'betGroups.' + idx + '.methodId'\" style=\"flex:1;margin-bottom:0;\">\r\n                  <el-select v-model=\"group.methodId\"\r\n                             filterable\r\n                             remote\r\n                             :remote-method=\"filterMethod\"\r\n                             :loading=\"loadingGameMethods\"\r\n                             clearable\r\n                             placeholder=\"请选择玩法名称\"\r\n                             size=\"small\"\r\n                             style=\"width:100%\"\r\n                             @change=\"onMethodChange(group)\">\r\n                    <el-option v-for=\"item in filteredGameMethods\"\r\n                               :key=\"item.methodId\"\r\n                               :label=\"item.methodName\"\r\n                               :value=\"item.methodId\">\r\n                      {{ item.methodName }}\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item :label=\"'金额'\" :prop=\"'betGroups.' + idx + '.money'\" style=\"width:200px;margin-bottom:0;\">\r\n                  <el-input v-model=\"group.money\" placeholder=\"请输入金额\" size=\"small\" @input=\"updateTotals\" />\r\n                </el-form-item>\r\n                <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"removeBetGroup(idx)\" circle size=\"mini\" />\r\n              </div>\r\n\r\n              <div\r\n                v-if=\"group.methodId === 29 || group.methodId === 30 || group.methodId === 2 || [21, 22, 23, 24, 25, 26, 27, 37, 38, 39, 40, 41, 42, 43].includes(group.methodId)\"\r\n                style=\"margin-left: 80px; margin-bottom: 8px;\">\r\n                <template v-if=\"group.methodId === 29\">\r\n                  <div class=\"option-group\">\r\n                    <span class=\"option-label\" style=\"font-size: 16px;font-weight: 600;\">单双：</span>\r\n                    <el-radio-group v-model=\"group.danshuang\" size=\"medium\">\r\n                      <el-radio-button :label=\"200\">单</el-radio-button>\r\n                      <el-radio-button :label=\"201\">双</el-radio-button>\r\n                    </el-radio-group>\r\n                  </div>\r\n                </template>\r\n                <template v-else-if=\"group.methodId === 30\">\r\n                  <div class=\"option-group\">\r\n                    <span class=\"option-label\" style=\"font-size: 16px;font-weight: 600;\">大小：</span>\r\n                    <el-radio-group v-model=\"group.daxiao\" size=\"medium\">\r\n                      <el-radio-button :label=\"300\">大</el-radio-button>\r\n                      <el-radio-button :label=\"301\">小</el-radio-button>\r\n                    </el-radio-group>\r\n                  </div>\r\n                </template>\r\n                <template v-else-if=\"group.methodId === 2\">\r\n                  <div class=\"option-group\">\r\n                    <span class=\"option-label\" style=\"font-size: 16px;font-weight: 600;\">定位：</span>\r\n                    <el-radio-group v-model=\"group.dingwei\" size=\"medium\">\r\n                      <el-radio-button :label=\"100\">百位</el-radio-button>\r\n                      <el-radio-button :label=\"101\">十位</el-radio-button>\r\n                      <el-radio-button :label=\"102\">个位</el-radio-button>\r\n                    </el-radio-group>\r\n                  </div>\r\n                </template>\r\n                <template v-else-if=\"[21, 22, 23, 24, 25, 26, 27, 37, 38, 39, 40, 41, 42, 43].includes(group.methodId)\">\r\n                  <div class=\"option-group\">\r\n                    <span class=\"option-label\" style=\"font-size: 16px;font-weight: 600;\">和值：</span>\r\n                    <el-radio-group v-model=\"group.hezhi\" size=\"medium\">\r\n                      <el-radio-button v-for=\"hz in getHezhiOptions(group.methodId)\" :key=\"hz\"\r\n                        :label=\"hz\">和值{{ hz }}</el-radio-button>\r\n                    </el-radio-group>\r\n                  </div>\r\n                </template>\r\n              </div>\r\n\r\n              <el-form-item :label=\"'下注号码'\" :prop=\"'betGroups.' + idx + '.betNumbers'\" v-if=\"!isSpecialMethod(group.methodId)\"\r\n                style=\"margin-bottom:8px;\">\r\n                <el-input :value=\"getDisplayBetNumbers(group)\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入下注号码\"\r\n                  @input=\"onBetNumbersInput($event, idx)\" />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"彩种类型\" style=\"margin-bottom:8px;\">\r\n                <div style=\"display:flex;align-items:center;\">\r\n                  <el-radio-group v-model=\"group.lotteryId\" size=\"small\" style=\"margin-right:16px;flex-shrink:0;\"\r\n                    :fill=\"group.lotteryId === 2 ? '#1890ff' : '#ff4949'\">\r\n                    <el-radio-button :label=\"1\" data-lottery=\"fc3d\"\r\n                      style=\"margin-right:6px;\">福彩3D</el-radio-button>\r\n                    <el-radio-button :label=\"2\" data-lottery=\"tc\"\r\n                      style=\"margin-right:0;\">体彩排三</el-radio-button>\r\n                  </el-radio-group>\r\n                  <div class=\"group-summary\">\r\n                    <div class=\"summary-item amount\">\r\n                      <div class=\"summary-icon\">\r\n                        <i class=\"el-icon-wallet\"></i>\r\n                      </div>\r\n                      <div class=\"summary-content\">\r\n                        <div class=\"summary-label\">总额</div>\r\n                        <div class=\"summary-value\">￥{{ getGroupAmount(group) }}</div>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"summary-item bets\">\r\n                      <div class=\"summary-icon\">\r\n                        <i class=\"el-icon-tickets\"></i>\r\n                      </div>\r\n                      <div class=\"summary-content\">\r\n                        <div class=\"summary-label\">投注数</div>\r\n                        <div class=\"summary-value\">{{ getGroupBets(group) }}注</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </el-form-item>\r\n            </div>\r\n          </div>\r\n          <button class=\"bet-group-add-btn\" @click=\"addBetGroup\" title=\"添加投注组合\">\r\n            <!-- <i class=\"el-icon-plus\"></i> -->\r\n          </button>\r\n        </div>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-statistic title=\"下注总额\" :value=\"totalAmount\" size=\"small\">\r\n              <template slot=\"prefix\">￥</template>\r\n            </el-statistic>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-statistic title=\"总投注数\" :value=\"totalBets\" size=\"small\">\r\n              <template slot=\"suffix\">注</template>\r\n            </el-statistic>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-form-item label=\"是否结算\" prop=\"jiesuan\" v-if=\"dialogTitle === '修改下注管理'\">\r\n          <el-switch v-model=\"formData.jiesuan\" :active-value=\"1\" :inactive-value=\"0\" active-text=\"已结算\"\r\n            inactive-text=\"未结算\" size=\"small\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <div style=\"display: flex; align-items: center; justify-content: center; width: 100%;\">\r\n          <el-button type=\"primary\" @click=\"submitForm\" size=\"small\">确 定</el-button>\r\n          <el-button @click=\"close\" size=\"small\">取 消</el-button>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog\r\n      title=\"格式转换工具\"\r\n      :visible.sync=\"formatDialogVisible\"\r\n      width=\"650px\"\r\n      append-to-body\r\n      class=\"format-dialog\"\r\n      :close-on-click-modal=\"false\">\r\n\r\n      <div class=\"format-dialog-content\">\r\n        <el-tabs v-model=\"formatTab\" type=\"card\" class=\"format-tabs\">\r\n          <el-tab-pane name=\"unified\">\r\n            <span slot=\"label\">\r\n              <i class=\"el-icon-edit\"></i>\r\n              字符转换\r\n            </span>\r\n            <div class=\"tab-content\">\r\n              <div class=\"input-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-upload2\"></i>\r\n                  <span>输入内容</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"unifiedFormatInput\"\r\n                  placeholder=\"智能识别并转换：汉字金额(如二十)、逗号、句号、冒号、下划线、加号等转为空格&#10;示例：123,456。。789：：：012___345➕678二十元\"\r\n                  @input=\"handleUnifiedFormatInput\"\r\n                  class=\"format-input\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"arrow-section\">\r\n                <i class=\"el-icon-bottom\"></i>\r\n              </div>\r\n\r\n              <div class=\"output-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-download\"></i>\r\n                  <span>转换结果</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  :value=\"unifiedFormatOutput\"\r\n                  readonly\r\n                  placeholder=\"转换结果将在这里显示...\"\r\n                  class=\"format-output\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"button-section\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-check\"\r\n                  @click=\"insertUnifiedFormatResult\"\r\n                  :disabled=\"!unifiedFormatOutput\"\r\n                  class=\"action-button\">\r\n                  插入到号码识别\r\n                </el-button>\r\n                <el-button\r\n                  icon=\"el-icon-close\"\r\n                  @click=\"formatDialogVisible = false\"\r\n                  class=\"cancel-button\">\r\n                  关闭\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane name=\"chain\">\r\n            <span slot=\"label\">\r\n              <i class=\"el-icon-s-grid\"></i>\r\n              链子分类\r\n            </span>\r\n            <div class=\"tab-content\">\r\n              <div class=\"input-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-upload2\"></i>\r\n                  <span>输入内容</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"chainFormatInput\"\r\n                  placeholder=\"按号码长度自动分类到不同行\r\n示例：123-1234-568-56789-503-9012-12345678\"\r\n                  @input=\"handleChainFormatInput\"\r\n                  class=\"format-input\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"arrow-section\">\r\n                <i class=\"el-icon-bottom\"></i>\r\n              </div>\r\n\r\n              <div class=\"output-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-download\"></i>\r\n                  <span>分类结果</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"6\"\r\n                  :value=\"chainFormatOutput\"\r\n                  readonly\r\n                  placeholder=\"分类结果将在这里显示...\"\r\n                  class=\"format-output\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"button-section\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-check\"\r\n                  @click=\"insertChainFormatResult\"\r\n                  :disabled=\"!chainFormatOutput\"\r\n                  class=\"action-button\">\r\n                  插入到号码识别\r\n                </el-button>\r\n                <el-button\r\n                  icon=\"el-icon-close\"\r\n                  @click=\"formatDialogVisible = false\"\r\n                  class=\"cancel-button\">\r\n                  关闭\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane name=\"amount\">\r\n            <span slot=\"label\">\r\n              <i class=\"el-icon-money\"></i>\r\n              金额整合\r\n            </span>\r\n            <div class=\"tab-content\">\r\n              <div class=\"input-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-upload2\"></i>\r\n                  <span>输入内容</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"6\"\r\n                  v-model=\"amountFormatInput\"\r\n                  placeholder=\"将相同金额且相同位数的号码合并到一行\r\n  支持每行一个或一行内空格分隔多个\r\n  示例：57-50  59-50  79-50  88-100\r\n  结果： 57-59-79-50\r\n        88-100\"\r\n                  @input=\"handleAmountFormatInput\"\r\n                  class=\"format-input\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"arrow-section\">\r\n                <i class=\"el-icon-bottom\"></i>\r\n              </div>\r\n\r\n              <div class=\"output-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-download\"></i>\r\n                  <span>整合结果</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"6\"\r\n                  :value=\"amountFormatOutput\"\r\n                  readonly\r\n                  placeholder=\"整合结果将在这里显示...\"\r\n                  class=\"format-output\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"button-section\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-check\"\r\n                  @click=\"insertAmountFormatResult\"\r\n                  :disabled=\"!amountFormatOutput\"\r\n                  class=\"action-button\">\r\n                  插入到号码识别\r\n                </el-button>\r\n                <el-button\r\n                  icon=\"el-icon-close\"\r\n                  @click=\"formatDialogVisible = false\"\r\n                  class=\"cancel-button\">\r\n                  关闭\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n\r\n          <el-tab-pane name=\"remove\">\r\n            <span slot=\"label\">\r\n              <i class=\"el-icon-delete\"></i>\r\n              去除分隔符\r\n            </span>\r\n            <div class=\"tab-content\">\r\n              <div class=\"input-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-upload2\"></i>\r\n                  <span>输入内容</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"6\"\r\n                  v-model=\"removeFormatInput\"\r\n                  placeholder=\"去除所有分隔符和英文字母，只保留汉字、数字和换行\r\n示例：123,456。。789：：：abc___def➕ghi二十元ABC\r\n结果：123456789二十元\"\r\n                  @input=\"handleRemoveFormatInput\"\r\n                  class=\"format-input\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"arrow-section\">\r\n                <i class=\"el-icon-bottom\"></i>\r\n              </div>\r\n\r\n              <div class=\"output-section\">\r\n                <div class=\"section-header\">\r\n                  <i class=\"el-icon-download\"></i>\r\n                  <span>清理结果</span>\r\n                </div>\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"6\"\r\n                  :value=\"removeFormatOutput\"\r\n                  readonly\r\n                  placeholder=\"清理结果将在这里显示...\"\r\n                  class=\"format-output\"\r\n                />\r\n              </div>\r\n\r\n              <div class=\"button-section\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  icon=\"el-icon-check\"\r\n                  @click=\"insertRemoveFormatResult\"\r\n                  :disabled=\"!removeFormatOutput\"\r\n                  class=\"action-button\">\r\n                  插入到号码识别\r\n                </el-button>\r\n                <el-button\r\n                  icon=\"el-icon-close\"\r\n                  @click=\"formatDialogVisible = false\"\r\n                  class=\"cancel-button\">\r\n                  关闭\r\n                </el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 下注统计组件 -->\r\n    <bet-statistics :visible.sync=\"statisticsVisible\" />\r\n\r\n    <!-- 玩法格式弹窗组件 -->\r\n    <bet-format-dialog ref=\"betFormatDialog\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { handleNumberRecognition, handleSmartRecognition } from './numberRecognition.js';\r\nimport request from '@/utils/request';\r\nimport { getMaxSerialNumber } from '@/api/game/serial'\r\nimport { generateSerialNumber } from '@/api/game/record'\r\nimport { getCurrentQihao } from '@/api/game/qihao'\r\nimport { checkAndSetDefaultPlayer } from '@/api/game/customer'\r\nimport { getStatisticsPermission } from '@/api/system/user'\r\nimport BetStatistics from './BetStatistics.vue'\r\nimport BetFormatDialog from './BetFormatDialog.vue'\r\n\r\nexport default {\r\n  name: 'BetDialog',\r\n  components: {\r\n    BetStatistics,\r\n    BetFormatDialog\r\n  },\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: '新增下注'\r\n    },\r\n    row: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      totalAmount: 0,\r\n      totalBets: 0,\r\n      pendingRequests: [],\r\n      gameMethodsData: [], // 玩法数据\r\n      filteredGameMethods: [], // 用于 remote 搜索的玩法数据\r\n      loadingGameMethods: false, // 玩法加载状态\r\n      formData: {\r\n        fc3dIssueNumber: '',  // 福彩3D期号\r\n        tcIssueNumber: '',    // 体彩排三期号\r\n        shibie: '',\r\n        betGroups: [{\r\n          methodId: undefined,\r\n          money: '',\r\n          betNumbers: '',\r\n          danshuang: null,\r\n          daxiao: null,\r\n          dingwei: null,\r\n          hezhi: null,\r\n          lotteryId: 1\r\n        }]\r\n      },\r\n      // 新增：号码数量限制相关\r\n      numberCount: 0,\r\n      isNumberCountExceeded: false,\r\n      rules: {\r\n        betGroups: {\r\n          methodId: [{ required: true, message: '请选择玩法', trigger: 'change' }],\r\n          money: [{ required: true, message: '请输入金额', trigger: 'blur' }]\r\n        }\r\n      },\r\n      localForm: {\r\n        fc3dIssueNumber: '',  // 福彩3D期号\r\n        tcIssueNumber: '',    // 体彩排三期号\r\n        shibie: '',\r\n        betGroups: []\r\n      },\r\n      currentUser: { name: '' },\r\n      userList: [],\r\n      selectedUserId: null,\r\n      futiSwitch: sessionStorage.getItem('futiSwitch') || 'all',\r\n      formatDialogVisible: false,\r\n      formatInput: '',\r\n      formatOutput: '',\r\n      formatTab: 'unified',\r\n      colonFormatInput: '',\r\n      colonFormatOutput: '',\r\n      moneyFormatInput: '',\r\n      moneyFormatOutput: '',\r\n\r\n      underscoreFormatInput: '',\r\n      underscoreFormatOutput: '',\r\n      unifiedFormatInput: '',\r\n      unifiedFormatOutput: '',\r\n      chainFormatInput: '',\r\n      chainFormatOutput: '',\r\n      amountFormatInput: '',\r\n      amountFormatOutput: '',\r\n      removeFormatInput: '',\r\n      removeFormatOutput: '',\r\n      serialNumber: 1, // 新增本组序号\r\n      maxSerialNumber: 1, // 新增最大流水号\r\n      generateLoading: false, // 生成流水号加载状态\r\n      originalUserId: null, // 原始用户ID\r\n      originalSerialNumber: null, // 原始流水号\r\n      statisticsVisible: false, // 统计对话框显示状态\r\n      hasStatisticsPermission: false // 是否有统计权限\r\n    }\r\n  },\r\n  computed: {\r\n    /** 是否显示统计按钮 */\r\n    showStatisticsButton() {\r\n      return this.hasStatisticsPermission;\r\n    }\r\n  },\r\n  watch: {\r\n    visible(val) {\r\n      this.dialogVisible = val;\r\n      if (val) {\r\n        // 显示对话框时先检查玩家状态\r\n        this.checkPlayerStatus();\r\n        this.getAllUsers();\r\n        // 检查统计权限\r\n        this.checkStatisticsPermission();\r\n        // 新增：区分新增和修改，设置 selectedUserId\r\n        if (this.row && this.row.userId) {\r\n          // 修改时\r\n          this.selectedUserId = this.row.userId;\r\n        } else {\r\n          // 新增时，先检查session中是否有User_Id\r\n          let userId = sessionStorage.getItem('User_Id');\r\n          if (userId) {\r\n            this.selectedUserId = Number(userId);\r\n          } else {\r\n            // 如果没有，等待initializeFirstUser()设置\r\n            this.selectedUserId = null;\r\n          }\r\n        }\r\n        // 先初始化流水号，再设置表单数据\r\n      \r\n        this.initSerialNumber(); // 弹窗打开时初始化本组序号\r\n        // 如果是修改操作，初始化表单数据\r\n        if (this.row) {\r\n       \r\n          this.initFormData();\r\n        }\r\n        // 打开弹窗时根据开关状态处理\r\n        this.onFutiSwitchChange(this.futiSwitch);\r\n      }\r\n    },\r\n    dialogVisible(val) {\r\n      this.$emit('update:visible', val);\r\n      if (!val) {\r\n        this.resetForm();\r\n      }\r\n    },\r\n    // 监听用户ID变化，自动处理流水号\r\n    selectedUserId: {\r\n      handler(newUserId, oldUserId) {\r\n        // 只在修改模式下处理，且确保有原始数据\r\n        if (this.row && this.row.betId && this.originalUserId !== null && oldUserId !== undefined) {\r\n          this.handleUserIdChange(newUserId, oldUserId);\r\n        }\r\n      },\r\n      immediate: false\r\n    },\r\n    formatDialogVisible(val) {\r\n      if (!val) {\r\n        // 弹窗关闭时清空所有格式内容\r\n        this.formatInput = '';\r\n        this.formatOutput = '';\r\n        this.colonFormatInput = '';\r\n        this.colonFormatOutput = '';\r\n        this.moneyFormatInput = '';\r\n        this.moneyFormatOutput = '';\r\n\r\n        this.underscoreFormatInput = '';\r\n        this.underscoreFormatOutput = '';\r\n        this.unifiedFormatInput = '';\r\n        this.unifiedFormatOutput = '';\r\n        this.chainFormatInput = '';\r\n        this.chainFormatOutput = '';\r\n        this.amountFormatInput = '';\r\n        this.amountFormatOutput = '';\r\n        this.removeFormatInput = '';\r\n        this.removeFormatOutput = '';\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化时获取玩法列表\r\n    this.getGameMethods();\r\n  },\r\n  methods: {\r\n    // 显示玩法格式弹窗\r\n    showBetFormatDialog() {\r\n      this.$refs.betFormatDialog.show();\r\n    },\r\n    // 检查玩家状态\r\n    async checkPlayerStatus() {\r\n      try {\r\n        const response = await checkAndSetDefaultPlayer();\r\n\r\n        if (response.code === 500 && response.needCreatePlayer) {\r\n          // 没有玩家，显示提示并跳转\r\n          this.$confirm(response.msg + ' 是否立即前往创建？', '提示', {\r\n            confirmButtonText: '前往创建',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            // 关闭当前对话框\r\n            this.dialogVisible = false;\r\n            this.$emit('update:visible', false);\r\n            // 跳转到玩家页面\r\n            this.$router.push('/game/customer');\r\n          }).catch(() => {\r\n            // 用户取消，关闭对话框\r\n            this.dialogVisible = false;\r\n            this.$emit('update:visible', false);\r\n          });\r\n          return;\r\n        }\r\n\r\n        if (response.code === 200 && response.hasPlayers) {\r\n          // 有玩家，继续初始化\r\n          \r\n          if (response.defaultUserId) {\r\n            \r\n          }\r\n\r\n          // 继续初始化对话框\r\n          this.initializeDialog();\r\n        }\r\n      } catch (error) {\r\n        console.error('检查玩家状态失败:', error);\r\n        this.$message.error('检查玩家状态失败，请重试');\r\n        this.dialogVisible = false;\r\n        this.$emit('update:visible', false);\r\n      }\r\n    },\r\n\r\n    // 初始化对话框\r\n    initializeDialog() {\r\n      // 显示对话框时获取期号和玩法列表\r\n      this.getCurrentIssueNumber();\r\n      this.getGameMethods();\r\n      this.getCurrentUser();\r\n    },\r\n\r\n    // 获取玩法列表\r\n    getGameMethods() {\r\n      this.loadingGameMethods = true;\r\n      return request({\r\n        url: '/game/method/list',\r\n        method: 'get',\r\n        params: {\r\n          pageNum: 1,\r\n          pageSize: 1000  // 设置足够大的页面大小以获取所有数据\r\n        }\r\n      }).then(response => {\r\n        let data = [];\r\n        if (response.code === 200) {\r\n          if (Array.isArray(response.data)) {\r\n            data = response.data;\r\n          } else if (response.rows && Array.isArray(response.rows)) {\r\n            data = response.rows;\r\n          } else if (typeof response.data === 'string') {\r\n            try {\r\n              const parsedData = JSON.parse(response.data);\r\n              data = Array.isArray(parsedData) ? parsedData : [];\r\n            } catch (e) {\r\n              data = [];\r\n            }\r\n          }\r\n        }\r\n        this.gameMethodsData = data;\r\n        this.filteredGameMethods = data;\r\n        this.loadingGameMethods = false;\r\n      }).catch(() => {\r\n        this.gameMethodsData = [];\r\n        this.filteredGameMethods = [];\r\n        this.loadingGameMethods = false;\r\n      });\r\n    },\r\n    // 打开对话框\r\n    show(title = '新增下注') {\r\n      this.dialogTitle = title;\r\n      this.dialogVisible = true;\r\n    },\r\n    // 关闭对话框\r\n    close() {\r\n      this.dialogVisible = false;\r\n      this.$emit('update:visible', false);\r\n      this.$emit('cancel');  // 保持向后兼容\r\n    },\r\n    // 重置表单\r\n    resetForm() {\r\n      this.formData = {\r\n        fc3dIssueNumber: '',\r\n        tcIssueNumber: '',\r\n        shibie: '',\r\n        betGroups: [{\r\n          methodId: undefined,\r\n          money: '',\r\n          betNumbers: '',\r\n          danshuang: null,\r\n          daxiao: null,\r\n          dingwei: null,\r\n          hezhi: null,\r\n          lotteryId: 1\r\n        }]\r\n      };\r\n      this.totalAmount = 0;\r\n      this.totalBets = 0;\r\n      // 重置号码数量状态\r\n      this.numberCount = 0;\r\n      this.isNumberCountExceeded = false;\r\n    },\r\n    addBetGroup() {\r\n      const newGroup = {\r\n        methodId: undefined,\r\n        money: '',\r\n        betNumbers: '',\r\n        danshuang: null,\r\n        daxiao: null,\r\n        dingwei: null,\r\n        hezhi: null,\r\n        lotteryId: this.futiSwitch === 'tc' ? 2 : 1\r\n      };\r\n\r\n      // 直接操作formData\r\n      if (!this.formData.betGroups) {\r\n        this.formData.betGroups = [];\r\n      }\r\n      this.formData.betGroups.push({ ...newGroup });\r\n\r\n      this.$nextTick(() => {\r\n        this.updateTotals();\r\n      });\r\n    },\r\n    removeBetGroup(idx) {\r\n      if (this.formData.betGroups.length <= 1) {\r\n        // 如果只剩一个投注组，则清空它而不是删除\r\n        this.formData.betGroups = [{\r\n          methodId: undefined,\r\n          money: '',\r\n          betNumbers: '',\r\n          danshuang: null,\r\n          daxiao: null,\r\n          dingwei: null,\r\n          hezhi: null,\r\n          lotteryId: 1\r\n        }];\r\n      } else {\r\n        // 如果有多个投注组，则删除指定的组\r\n        this.formData.betGroups.splice(idx, 1);\r\n      }\r\n\r\n      this.$nextTick(() => {\r\n        this.updateTotals();\r\n        this.$forceUpdate(); // 强制更新视图\r\n      });\r\n    },\r\n    updateTotals() {\r\n      this.totalAmount = this.formData.betGroups.reduce((sum, g) => sum + Number(this.getGroupAmount(g)), 0);\r\n      this.totalBets = this.formData.betGroups.reduce((sum, g) => sum + Number(this.getGroupBets(g)), 0);\r\n    },\r\n    getHezhiOptions(methodId) {\r\n      const map = {\r\n        21: [7, 20],\r\n        22: [8, 19],\r\n        23: [9, 18],\r\n        24: [10, 17],\r\n        25: [11, 16],\r\n        26: [12, 15],\r\n        27: [13, 14],\r\n        37: [0, 27],\r\n        38: [1, 26],\r\n        39: [2, 25],\r\n        40: [3, 24],\r\n        41: [4, 23],\r\n        42: [5, 22],\r\n        43: [6, 21]\r\n      };\r\n      return map[methodId] || [];\r\n    },\r\n    onMethodChange(group) {\r\n      \r\n      const index = this.formData.betGroups.findIndex(g => g === group);\r\n      if (index > -1) {\r\n        // 创建一个新的组对象，确保包含所有必要的字段\r\n        const updatedGroup = {\r\n          ...group,\r\n          danshuang: null,\r\n          daxiao: null,\r\n          dingwei: null,\r\n          hezhi: null\r\n          // 不重置 betNumbers 和 money\r\n        };\r\n\r\n        // 根据玩法类型设置默认值\r\n        if (updatedGroup.methodId === 29) {\r\n          updatedGroup.danshuang = 200;\r\n        } else if (updatedGroup.methodId === 30) {\r\n          updatedGroup.daxiao = 300;\r\n        } else if (updatedGroup.methodId === 2) {\r\n          updatedGroup.dingwei = 100;\r\n        } else if ([21, 22, 23, 24, 25, 26, 27, 37, 38, 39, 40, 41, 42, 43].includes(updatedGroup.methodId)) {\r\n          const options = this.getHezhiOptions(updatedGroup.methodId);\r\n          if (options && options.length > 0) {\r\n            updatedGroup.hezhi = options[0];\r\n          }\r\n        }\r\n\r\n        // 更新组数据\r\n        this.$set(this.formData.betGroups, index, updatedGroup);\r\n        this.$forceUpdate();\r\n      }\r\n    },\r\n    // 获取当前期号\r\n    getCurrentIssueNumber() {\r\n      const loadingInstance = this.$loading({\r\n        lock: true,\r\n        text: '正在获取期号...',\r\n        spinner: 'el-icon-loading',\r\n        background: 'rgba(0, 0, 0, 0.7)',\r\n        customClass: 'bet-dialog-loading'\r\n      });\r\n\r\n      // 使用Promise.all同时获取两个期号\r\n      Promise.all([\r\n        // 获取福彩3D期号\r\n        getCurrentQihao(1).catch(error => {\r\n          console.error('获取福彩3D期号失败:', error);\r\n          return { code: 500, msg: '获取失败' };\r\n        }),\r\n        // 获取体彩排三期号\r\n        getCurrentQihao(2).catch(error => {\r\n          console.error('获取体彩排三期号失败:', error);\r\n          return { code: 500, msg: '获取失败' };\r\n        })\r\n      ]).then(([fc3dResponse, tcResponse]) => {\r\n        // 处理福彩3D期号\r\n        if (fc3dResponse.code === 200) {\r\n          this.formData.fc3dIssueNumber = fc3dResponse.msg;\r\n          \r\n        } else {\r\n          console.error('获取福彩3D期号失败:', fc3dResponse);\r\n          this.$message.error('获取福彩3D期号失败');\r\n        }\r\n\r\n        // 处理体彩排三期号\r\n        if (tcResponse.code === 200) {\r\n          this.formData.tcIssueNumber = tcResponse.msg;\r\n         \r\n        } else {\r\n          console.error('获取体彩排三期号失败:', tcResponse);\r\n          this.$message.error('获取体彩排三期号失败');\r\n        }\r\n      }).finally(() => {\r\n        loadingInstance.close();\r\n      });\r\n    },\r\n    // 新增：动态计算号码数量的方法\r\n    calculateNumberCount(input) {\r\n      if (!input || !input.trim()) return 0;\r\n\r\n      // 清理输入，去除首尾空白\r\n      const cleanInput = input.trim();\r\n\r\n      // 使用更精确的正则匹配号码组\r\n      // 匹配2-9位连续数字，考虑各种分隔符\r\n      const numberPattern = /\\d{2,9}/g;\r\n      const allMatches = cleanInput.match(numberPattern);\r\n\r\n      if (!allMatches) return 0;\r\n\r\n      // 按行分析，更准确地识别号码和金额\r\n      const lines = cleanInput.split(/[\\r\\n]+/).filter(line => line.trim());\r\n      let totalCount = 0;\r\n\r\n      for (const line of lines) {\r\n        const trimmedLine = line.trim();\r\n        if (!trimmedLine) continue;\r\n\r\n        const lineNumbers = trimmedLine.match(numberPattern);\r\n        if (!lineNumbers) continue;\r\n\r\n        // 检查金额相关关键字\r\n        const moneyKeywords = /[元块米钱赶组直吊放防各]/;\r\n        const hasMoneyKeyword = moneyKeywords.test(trimmedLine);\r\n\r\n        // 检查是否有明显的金额分隔符（如+、/等）\r\n        const hasMoneyDelimiter = /[+＋\\/]/.test(trimmedLine);\r\n\r\n        if (hasMoneyKeyword || hasMoneyDelimiter) {\r\n          // 如果包含金额关键字或分隔符，最后1-2个数字可能是金额\r\n          // 更保守的估计：减去1个可能的金额\r\n          totalCount += Math.max(0, lineNumbers.length - 1);\r\n        } else {\r\n          // 纯号码行，所有数字都算作号码\r\n          totalCount += lineNumbers.length;\r\n        }\r\n      }\r\n\r\n      return totalCount;\r\n    },\r\n    // 智能预处理批量号码+金额输入，避免金额被误识别为号码\r\n    smartPreprocessInput(input) {\r\n      if (!input) return input;\r\n      // 支持多行或\"—\"等超级分隔符\r\n      let parts = input.split(/[—\\-\\n\\r]+/).map(s => s.trim()).filter(Boolean);\r\n      if (parts.length > 1) {\r\n        // 金额表达关键字更宽松，兼容赶/组/直/吊/放/防/元/各等\r\n        const moneyExpr = /(赶|组|直|吊|放|防|元|各)[^\\d]*\\d+.*$/;\r\n        const last = parts[parts.length - 1];\r\n        if (moneyExpr.test(last)) {\r\n          // 合并为\"号码—号码—...—金额表达\"\r\n          return parts.slice(0, -1).join('—') + '—' + last;\r\n        }\r\n      }\r\n      return input;\r\n    },\r\n    // 新增：检查首组号码是否为三位数的方法\r\n    isFirstNumberThreeDigits(input) {\r\n      if (!input || !input.trim()) return false;\r\n\r\n      // 清理输入，去除首尾空白\r\n      const cleanInput = input.trim();\r\n\r\n      // 匹配第一个2-9位连续数字\r\n      const firstNumberMatch = cleanInput.match(/\\d{2,9}/);\r\n\r\n      if (!firstNumberMatch) return false;\r\n\r\n      // 检查第一个号码是否为三位数\r\n      return firstNumberMatch[0].length === 3;\r\n    },\r\n    handleNumberRecognition(value) {\r\n      // 实时计算并更新号码数量\r\n      this.numberCount = this.calculateNumberCount(value);\r\n\r\n      // 判断如果首组号码为三位数则不限制号码组数为30\r\n      const isFirstThreeDigits = this.isFirstNumberThreeDigits(value);\r\n      this.isNumberCountExceeded = isFirstThreeDigits ? false : this.numberCount > 30;\r\n\r\n      if (!value) {\r\n        this.formData.betGroups = [{\r\n          methodId: undefined,\r\n          money: '',\r\n          betNumbers: '',\r\n          danshuang: null,\r\n          daxiao: null,\r\n          dingwei: null,\r\n          hezhi: null,\r\n          lotteryId: this.futiSwitch === 'tc' ? 2 : 1\r\n        }];\r\n        return;\r\n      }\r\n\r\n      // 如果超过30个号码，不进行识别处理，但仍然显示计数\r\n      if (this.isNumberCountExceeded) {\r\n        \r\n        return;\r\n      }\r\n      // 1. 先整体识别（必须用原始 value，不能预处理！）\r\n      let result = handleNumberRecognition(value);\r\n      // 2. 如果整体没命中，再按行分割，每行单独识别，不做任何合并\r\n      if (!result || !result.groups || !result.groups.some(g => g.methodId)) {\r\n        const lines = value.split(/[\\r\\n]+/).map(line => line.trim()).filter(Boolean);\r\n        let allGroups = [];\r\n        for (const line of lines) {\r\n          const lineResult = handleNumberRecognition(line);\r\n          if (lineResult && lineResult.groups && lineResult.groups.length > 0) {\r\n            allGroups = allGroups.concat(lineResult.groups);\r\n          }\r\n        }\r\n        result = { groups: allGroups };\r\n      }\r\n      \r\n      \r\n\r\n      if (result) {\r\n        // 如果存在groups，说明有多个和值组\r\n        if (result.groups && result.groups.length > 0) {\r\n     \r\n          // 清空现有的投注组\r\n          this.$set(this.formData, 'betGroups', []);\r\n          // 添加所有和值组\r\n          result.groups.forEach(group => {\r\n            const newGroup = {\r\n              methodId: group.methodId,\r\n              danshuang: group.danshuang,\r\n              daxiao: group.daxiao,\r\n              dingwei: group.dingwei,\r\n              hezhi: group.hezhi,\r\n              betNumbers: group.betNumbers,\r\n              money: group.money != null ? String(group.money) : '', // 确保money为字符串类型\r\n              lotteryId: this.futiSwitch === 'tc' ? 2 : (group.lotteryId || 1)\r\n            };\r\n\r\n            this.formData.betGroups.push(newGroup);\r\n          });\r\n        } else {\r\n\r\n          // 单个投注组的情况\r\n          const newGroup = {\r\n            methodId: result.methodId,\r\n            danshuang: result.danshuang,\r\n            daxiao: result.daxiao,\r\n            dingwei: result.dingwei,\r\n            hezhi: result.hezhi,\r\n            betNumbers: result.betNumbers,\r\n            money: result.money != null ? String(result.money) : '', // 确保money为字符串类型\r\n            lotteryId: this.futiSwitch === 'tc' ? 2 : (result.lotteryId || 1)\r\n          };\r\n\r\n          this.$set(this.formData, 'betGroups', [newGroup]);\r\n        }\r\n\r\n        // 同步到localForm\r\n        const localGroups = this.formData.betGroups.map(group => ({\r\n          ...group,\r\n          danshuang: group.danshuang || null,\r\n          daxiao: group.daxiao || null,\r\n          dingwei: group.dingwei || null,\r\n          hezhi: group.hezhi || null,\r\n          betNumbers: group.betNumbers || '',\r\n          money: group.money || null,\r\n          lotteryId: group.lotteryId || 1\r\n        }));\r\n   \r\n        this.$set(this.localForm, 'betGroups', localGroups);\r\n\r\n        // 强制更新视图\r\n        this.$nextTick(() => {\r\n          \r\n          this.updateTotals();\r\n          // 确保每个投注组都正确更新\r\n          this.formData.betGroups.forEach((group, index) => {\r\n            const updatedGroup = { ...group };\r\n           \r\n            this.$set(this.formData.betGroups, index, updatedGroup);\r\n          });\r\n    \r\n          this.$forceUpdate();\r\n        });\r\n      }\r\n    },\r\n    formatBetNumbersToDisplay(jsonStr) {\r\n      try {\r\n        if (!jsonStr) return '';\r\n\r\n        // 如果不是JSON格式，直接返回\r\n        if (!jsonStr.startsWith('{')) {\r\n          return jsonStr;\r\n        }\r\n\r\n        const data = typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr;\r\n        if (!data.numbers || !Array.isArray(data.numbers)) return jsonStr;\r\n\r\n        return data.numbers.map(num => {\r\n          // 跨度玩法特殊处理\r\n          if (num.kuadu !== undefined) {\r\n            return `跨度${num.kuadu}`;\r\n          }\r\n          // 胆拖玩法特殊处理\r\n          if (num.danma !== undefined && num.tuoma !== undefined) {\r\n            return `胆${num.danma}拖${num.tuoma}`;\r\n          }\r\n          // 其他玩法\r\n          const values = Object.values(num);\r\n          return values.join('');\r\n        }).join(',');\r\n      } catch (error) {\r\n        console.error('格式化下注号码失败:', error, 'jsonStr:', jsonStr);\r\n        return jsonStr;\r\n      }\r\n    },\r\n    // 获取用于显示的下注号码\r\n    getDisplayBetNumbers(group) {\r\n      if (!group.betNumbers) return '';\r\n\r\n      // 跨度玩法 (60-69) 和胆拖玩法 (44-59) 需要特殊格式化\r\n      if ((group.methodId >= 44 && group.methodId <= 59) || (group.methodId >= 60 && group.methodId <= 69)) {\r\n        try {\r\n          // 检查是否是JSON格式\r\n          if (group.betNumbers.startsWith('{') && group.betNumbers.includes('numbers')) {\r\n            return this.formatBetNumbersToDisplay(group.betNumbers);\r\n          }\r\n          // 如果不是JSON格式，直接返回（用户正在编辑）\r\n          return group.betNumbers;\r\n        } catch (error) {\r\n          console.error('格式化显示号码失败:', error);\r\n          return group.betNumbers;\r\n        }\r\n      }\r\n\r\n      // 其他玩法直接显示\r\n      return group.betNumbers;\r\n    },\r\n    // 处理下注号码输入\r\n    onBetNumbersInput(value, index) {\r\n      const group = this.formData.betGroups[index];\r\n\r\n      // 跨度玩法和胆拖玩法需要保持JSON格式存储，但显示为用户友好格式\r\n      if ((group.methodId >= 44 && group.methodId <= 59) || (group.methodId >= 60 && group.methodId <= 69)) {\r\n        // 对于这些特殊玩法，用户输入的友好格式需要转换为JSON格式存储\r\n        // 但这里我们暂时直接存储用户输入，让识别逻辑处理\r\n        this.$set(this.formData.betGroups, index, { ...group, betNumbers: value });\r\n      } else {\r\n        // 其他玩法直接存储\r\n        this.$set(this.formData.betGroups, index, { ...group, betNumbers: value });\r\n      }\r\n\r\n      this.updateTotals();\r\n    },\r\n    initFormData() {\r\n      try {\r\n        // 保存原始数据\r\n        this.originalUserId = this.row.userId;\r\n        this.originalSerialNumber = this.row.serialNumber;\r\n\r\n        // 设置期号\r\n        this.formData.fc3dIssueNumber = this.row.issueNumber;\r\n        this.formData.tcIssueNumber = this.row.issueNumber;\r\n\r\n        // 格式化下注号码\r\n        const formattedBetNumbers = this.formatBetNumbersToDisplay(this.row.betNumbers);\r\n\r\n        // 创建投注组\r\n        const group = {\r\n          methodId: this.row.methodId,\r\n          money: this.row.money != null ? String(this.row.money) : '', // 确保money为字符串类型\r\n          betNumbers: formattedBetNumbers, // 使用格式化后的号码\r\n          danshuang: this.row.danshuang,\r\n          daxiao: this.row.daxiao,\r\n          dingwei: this.row.dingwei,\r\n          hezhi: this.row.hezhi,\r\n          lotteryId: this.row.lotteryId\r\n        };\r\n\r\n        // 设置投注组\r\n        this.formData.betGroups = [group];\r\n\r\n        // 如果有识别文本，也设置\r\n        if (this.row.shibie) {\r\n          this.formData.shibie = this.row.shibie;\r\n        }\r\n\r\n        // 如果有流水号，也设置\r\n        if (this.row.serialNumber) {\r\n       \r\n          this.serialNumber = this.row.serialNumber;\r\n        }\r\n\r\n        // 更新总计\r\n        this.$nextTick(() => {\r\n          this.updateTotals();\r\n          // 强制更新视图以确保特殊玩法正确显示\r\n          this.$forceUpdate();\r\n        });\r\n      } catch (error) {\r\n        console.error('初始化表单数据失败:', error);\r\n      }\r\n    },\r\n    formatNumbersForRequest(betNumbers, methodId) {\r\n      if (!betNumbers) return { numbers: [] };\r\n\r\n      // 胆拖玩法 (44-59) - 只存储danma和tuoma\r\n      if (methodId >= 44 && methodId <= 59) {\r\n        return this.formatDantuoNumbers(betNumbers, methodId);\r\n      }\r\n\r\n      // 跨度玩法 (60-69) - 只存储kuadu\r\n      if (methodId >= 60 && methodId <= 69) {\r\n        return this.formatKuaduNumbers(betNumbers, methodId);\r\n      }\r\n\r\n      // 分割号码组\r\n      const numberGroups = betNumbers.split(',').filter(n => n.trim());\r\n\r\n      // 根据玩法格式化\r\n      const formatMap = {\r\n        // 一位数玩法（独胆、一码定位、杀码等）\r\n        1: (num) => ({ a: parseInt(num) }),\r\n        2: (num) => ({ a: parseInt(num) }),\r\n        33: (num) => ({ a: parseInt(num) }),\r\n        34: (num) => ({ a: num }), // 两码定位，保持原始模式如\"9*6\"\r\n        35: (num) => ({ a: num }), // 一码单双\r\n        36: (num) => ({ a: num }), // 一码大小\r\n        // 两位数玩法（两码组合、对子等）\r\n        3: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1] };\r\n        },\r\n        4: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1] };\r\n        },\r\n        // 两码组三\r\n        70: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1] };\r\n        },\r\n\r\n        // 三位数玩法（三码直选、组选、防对等）\r\n        5: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2] };\r\n        },\r\n        6: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2] };\r\n        },\r\n        7: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2] };\r\n        },\r\n        100: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2] };\r\n        },\r\n\r\n        // 四码\r\n        8: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3] };\r\n        },\r\n        9: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3] };\r\n        },\r\n\r\n        // 五码\r\n        10: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4] };\r\n        },\r\n        11: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4] };\r\n        },\r\n\r\n        // 六码\r\n        12: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5] };\r\n        },\r\n        13: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5] };\r\n        },\r\n\r\n        // 七码\r\n        14: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6] };\r\n        },\r\n        15: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6] };\r\n        },\r\n\r\n        // 八码\r\n        16: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6], h: digits[7] };\r\n        },\r\n        17: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6], h: digits[7] };\r\n        },\r\n\r\n        // 九码\r\n        18: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6], h: digits[7], i: digits[8] };\r\n        },\r\n        19: (num) => {\r\n          const digits = num.split('').map(n => parseInt(n));\r\n          return { a: digits[0], b: digits[1], c: digits[2], d: digits[3], e: digits[4], f: digits[5], g: digits[6], h: digits[7], i: digits[8] };\r\n        },\r\n\r\n        // 包打组六、组三（不需要号码，返回空对象）\r\n        20: () => ({}),\r\n        31: () => ({}),\r\n\r\n        // 和值玩法（和值直接传递，不需要号码）\r\n        21: () => ({}),\r\n        22: () => ({}),\r\n        23: () => ({}),\r\n        24: () => ({}),\r\n        25: () => ({}),\r\n        26: () => ({}),\r\n        27: () => ({}),\r\n\r\n        // 新增和值玩法（和值直接传递，不需要号码）\r\n        37: () => ({}),\r\n        38: () => ({}),\r\n        39: () => ({}),\r\n        40: () => ({}),\r\n        41: () => ({}),\r\n        42: () => ({}),\r\n        43: () => ({}),\r\n\r\n        // 直选复式（暂时按空对象处理）\r\n        28: () => ({}),\r\n\r\n        // 和值单双\r\n        29: () => ({}),\r\n        // 和值大小\r\n        30: () => ({}),\r\n\r\n      };\r\n\r\n      const formatter = formatMap[methodId];\r\n      if (!formatter) return { numbers: [] };\r\n\r\n      return {\r\n        numbers: numberGroups.map(num => formatter(num.trim()))\r\n      };\r\n    },\r\n    submitForm() {\r\n      // 先验证金额\r\n      const moneyValidation = this.validateMoney();\r\n      if (!moneyValidation.valid) {\r\n        this.$alert(moneyValidation.message, '错误', {\r\n          confirmButtonText: '确定',\r\n          type: 'error',\r\n          dangerouslyUseHTMLString: true\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          const loadingInstance = this.$loading({\r\n            lock: true,\r\n            text: '正在提交下注...',\r\n            spinner: 'el-icon-loading',\r\n            background: 'rgba(0, 0, 0, 0.7)'\r\n          });\r\n\r\n          // 修正 userId 赋值逻辑\r\n          const betRecords = this.formData.betGroups.map((group, index) => {\r\n            // 格式化下注号码为JSON格式\r\n            const numbers = this.formatNumbersForRequest(group.betNumbers, group.methodId);\r\n            // 从 sessionStorage 获取 sysUserId\r\n            const sysUserId = sessionStorage.getItem('sysUserId');\r\n\r\n            const record = {\r\n              lotteryId: group.lotteryId,\r\n              issueNumber: group.lotteryId === 1 ? this.formData.fc3dIssueNumber : this.formData.tcIssueNumber,\r\n              methodId: group.methodId,\r\n              // 修改：userId 始终用当前选择的 selectedUserId\r\n              userId: Number(this.selectedUserId),\r\n              // 新增：从 sessionStorage 获取并传递 sysUserId\r\n              sysUserId: sysUserId ? Number(sysUserId) : null,\r\n              betNumbers: JSON.stringify(numbers),\r\n              money: group.money,\r\n              danshuang: group.danshuang,\r\n              daxiao: group.daxiao,\r\n              dingwei: group.dingwei,\r\n              hezhi: group.hezhi,\r\n              totalAmount: this.getGroupAmount(group),\r\n              totalBets: this.getGroupBets(group),\r\n              shibie: this.formData.shibie,\r\n              betTime: this.formatDate(new Date()),\r\n              serialNumber: this.serialNumber // 新增：传递流水号\r\n            };\r\n            // 如果是修改操作，添加betId\r\n            if (this.row && index === 0) {\r\n              record.betId = this.row.betId;\r\n            }\r\n            // 逐行打印每个 record\r\n          \r\n            return record;\r\n          });\r\n\r\n          // 提交前打印所有 betRecords\r\n       \r\n\r\n          // 根据是否是修改操作选择不同的提交方式\r\n          const url = this.row ? '/game/record' : '/game/record/batch';\r\n          const method = this.row ? 'put' : 'post';\r\n          const data = this.row ? betRecords[0] : betRecords;\r\n\r\n          request({\r\n            url,\r\n            method,\r\n            data\r\n          }).then(response => {\r\n            if (response.code === 200) {\r\n              this.$modal.msgSuccess('操作成功');\r\n              this.$emit('success');\r\n              // 清空号码识别框和投注组\r\n              this.formData.shibie = '';\r\n              this.formData.betGroups = [{\r\n                methodId: undefined,\r\n                money: '',\r\n                betNumbers: '',\r\n                danshuang: null,\r\n                daxiao: null,\r\n                dingwei: null,\r\n                hezhi: null,\r\n                lotteryId: 1\r\n              }];\r\n              this.updateTotals();\r\n              // 新增：仅在修改时关闭弹窗，新增时不关闭\r\n              if (this.row) {\r\n                this.close();\r\n              }\r\n            } else {\r\n              // 如果后端返回了具体哪些投注失败\r\n              if (response.data && Array.isArray(response.data.failedIndices)) {\r\n                // 保留失败的投注组\r\n                const failedGroups = this.formData.betGroups.filter((_, index) =>\r\n                  response.data.failedIndices.includes(index)\r\n                );\r\n                this.$set(this.formData, 'betGroups', failedGroups);\r\n                this.$modal.msgError(`${response.data.failedIndices.length}个下注失败，请重试`);\r\n              } else {\r\n                // 检查错误是否已经被响应拦截器处理\r\n                if (response._errorHandled) {\r\n                  // 错误已经被响应拦截器处理并显示，这里不再重复处理\r\n          \r\n                } else {\r\n                  // 未被处理的错误，显示通用错误信息\r\n                  this.$modal.msgError(response.msg || '操作失败，请重试');\r\n                }\r\n              }\r\n            }\r\n          }).catch(error => {\r\n            console.error('网络请求失败:', error);\r\n\r\n            // 现在只有真正的网络错误才会进入 catch 块\r\n            // 业务错误已经在响应拦截器中处理并返回到 then 块\r\n            this.$modal.msgError('网络连接失败，请检查网络后重试');\r\n          }).finally(() => {\r\n            loadingInstance.close();\r\n            this.updateTotals();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    getGroupBets(group) {\r\n      // 胆拖玩法 (44-59) - 始终显示为1注\r\n      if (group.methodId >= 44 && group.methodId <= 59) {\r\n        return 1;\r\n      }\r\n\r\n      // 跨度玩法 (60-69) - 始终显示为1注\r\n      if (group.methodId >= 60 && group.methodId <= 69) {\r\n        return 1;\r\n      }\r\n\r\n      if (this.isSpecialMethod(group.methodId)) {\r\n        if ([21,22,23,24,25,26,27,37,38,39,40,41,42,43].includes(group.methodId)) {\r\n          return group.hezhi ? 1 : 0;\r\n        }\r\n        if ([29,30].includes(group.methodId)) {\r\n          if (group.danshuang || group.daxiao) {\r\n            return 1;\r\n          }\r\n          return 0;\r\n        }\r\n        if ([20,31].includes(group.methodId)) {  // 包打组六和包打组三\r\n          return 1;\r\n        }\r\n        return 0;\r\n      }\r\n      if (group.betNumbers) {\r\n        return group.betNumbers.split(',').filter(s => s.trim()).length;\r\n      }\r\n      return 0;\r\n    },\r\n    getGroupAmount(group) {\r\n      const bets = this.getGroupBets(group);\r\n      const money = Number(group.money) || 0;\r\n      return bets * money;\r\n    },\r\n    isSpecialMethod(methodId) {\r\n      return [20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 37, 38, 39, 40, 41, 42, 43].includes(methodId);\r\n    },\r\n    // 胆拖格式化方法\r\n    formatDantuoNumbers(betNumbers, methodId) {\r\n      // 解析胆码和拖码\r\n      const parts = betNumbers.split('拖');\r\n      if (parts.length !== 2) return { numbers: [] };\r\n\r\n      const danma = parts[0].replace(/[^0-9]/g, '');\r\n      const tuoma = parts[1].replace(/[^0-9,，\\s]/g, '').split(/[,，\\s]+/).filter(n => n && n !== danma);\r\n\r\n      return {\r\n        numbers: [{\r\n          danma: danma,\r\n          tuoma: tuoma.join(',')\r\n        }]\r\n      };\r\n    },\r\n    // 跨度格式化方法\r\n    formatKuaduNumbers(betNumbers, methodId) {\r\n      console.log('formatKuaduNumbers 输入:', betNumbers, 'methodId:', methodId);\r\n\r\n      // 如果已经是JSON格式，直接返回\r\n      if (betNumbers.startsWith('{')) {\r\n        try {\r\n          const parsed = JSON.parse(betNumbers);\r\n          console.log('已是JSON格式:', parsed);\r\n          return parsed;\r\n        } catch (error) {\r\n          console.error('JSON解析失败:', error);\r\n        }\r\n      }\r\n\r\n      // 解析跨度值 - 支持多种格式\r\n      let kuaduValue = null;\r\n\r\n      // 格式1: \"跨度5\"\r\n      let match = betNumbers.match(/跨度(\\d)/);\r\n      if (match) {\r\n        kuaduValue = match[1];\r\n      } else {\r\n        // 格式2: \"跨5-50\" 或 \"跨5 50\"\r\n        match = betNumbers.match(/跨(\\d)[\\s\\-]*\\d+/);\r\n        if (match) {\r\n          kuaduValue = match[1];\r\n        } else {\r\n          // 格式3: 从methodId推导跨度值\r\n          if (methodId >= 60 && methodId <= 69) {\r\n            kuaduValue = (methodId - 60).toString();\r\n          }\r\n        }\r\n      }\r\n\r\n      console.log('解析出的跨度值:', kuaduValue);\r\n\r\n      if (kuaduValue === null) {\r\n        console.warn('无法解析跨度值，返回空数组');\r\n        return { numbers: [] };\r\n      }\r\n\r\n      const result = {\r\n        numbers: [{\r\n          kuadu: kuaduValue\r\n        }]\r\n      };\r\n\r\n      console.log('formatKuaduNumbers 输出:', result);\r\n      return result;\r\n    },\r\n    updateGroupValue(group, field, value) {\r\n\r\n      const index = this.formData.betGroups.findIndex(g => g === group);\r\n      if (index > -1) {\r\n        const updatedGroup = {\r\n          ...this.formData.betGroups[index],\r\n          [field]: value\r\n        };\r\n        this.$set(this.formData.betGroups, index, updatedGroup);\r\n        this.$forceUpdate();\r\n      }\r\n    },\r\n\r\n\r\n    // 格式化日期为 yyyy-MM-dd HH:mm:ss\r\n    formatDate(date) {\r\n      const pad = (num) => (num < 10 ? `0${num}` : num);\r\n      const year = date.getFullYear();\r\n      const month = pad(date.getMonth() + 1);\r\n      const day = pad(date.getDate());\r\n      const hours = pad(date.getHours());\r\n      const minutes = pad(date.getMinutes());\r\n      const seconds = pad(date.getSeconds());\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n    getCurrentUser() {\r\n      let userId = sessionStorage.getItem('User_Id');\r\n\r\n      if (userId) {\r\n        // 如果session中有User_Id，直接使用\r\n        this.selectedUserId = Number(userId);\r\n        this.loadUserById(userId);\r\n      } else {\r\n        // 如果session中没有User_Id，先获取用户列表，然后使用第一个用户\r\n        this.initializeFirstUser();\r\n      }\r\n    },\r\n\r\n    loadUserById(userId) {\r\n      request({\r\n        url: `/game/customer/${userId}`,\r\n        method: 'get'\r\n      }).then(res => {\r\n        if (res.code === 200 && res.data) {\r\n          this.currentUser = res.data;\r\n        } else {\r\n          this.currentUser = { name: '未知用户' };\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取用户信息失败:', error);\r\n        this.currentUser = { name: '未知用户' };\r\n      });\r\n    },\r\n\r\n    initializeFirstUser() {\r\n      // 获取当前用户的玩家列表\r\n      request({\r\n        url: '/game/customer/list',\r\n        method: 'get',\r\n        params: { pageNum: 1, pageSize: 1000 }\r\n      }).then(res => {\r\n        if (res.code === 200 && res.rows && res.rows.length > 0) {\r\n          // 使用第一个玩家作为默认用户\r\n          const firstUser = res.rows[0];\r\n          this.selectedUserId = firstUser.userId;\r\n          this.currentUser = firstUser;\r\n          // 保存到session中\r\n          sessionStorage.setItem('User_Id', firstUser.userId);\r\n        \r\n        } else {\r\n          console.warn('当前用户没有玩家数据');\r\n          this.currentUser = { name: '无玩家数据' };\r\n          this.selectedUserId = null;\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取玩家列表失败:', error);\r\n        this.currentUser = { name: '获取失败' };\r\n        this.selectedUserId = null;\r\n      });\r\n    },\r\n    getAllUsers() {\r\n      request({\r\n        url: '/game/customer/list',\r\n        method: 'get',\r\n        params: { pageNum: 1, pageSize: 1000 }\r\n      }).then(res => {\r\n        if (res.code === 200 && res.rows) {\r\n          this.userList = res.rows;\r\n        } else {\r\n          this.userList = [];\r\n        }\r\n      });\r\n    },\r\n    switchUser() {\r\n      if (!this.selectedUserId) return;\r\n      sessionStorage.setItem('User_Id', this.selectedUserId);\r\n      this.getCurrentUser();\r\n      this.$message.success('切换用户成功');\r\n    },\r\n    onFutiSwitchChange(val) {\r\n      sessionStorage.setItem('futiSwitch', val);\r\n      if (val === 'tc') {\r\n        // 仅体彩，所有下注单都设为2\r\n        if (this.formData && this.formData.betGroups) {\r\n          this.formData.betGroups.forEach(group => {\r\n            group.lotteryId = 2;\r\n            // 确保money字段始终为字符串类型\r\n            if (group.money != null && typeof group.money !== 'string') {\r\n              group.money = String(group.money);\r\n            }\r\n          });\r\n        }\r\n      } else {\r\n        // 切换为全部彩种时，确保数据类型正确\r\n        if (this.formData && this.formData.betGroups) {\r\n          this.formData.betGroups.forEach(group => {\r\n            // 确保money字段始终为字符串类型\r\n            if (group.money != null && typeof group.money !== 'string') {\r\n              group.money = String(group.money);\r\n            }\r\n          });\r\n        }\r\n      }\r\n      // 切换为全部彩种时不做强制限制\r\n      this.$forceUpdate();\r\n    },\r\n    // remote 搜索用的 filterMethod\r\n    filterMethod(query) {\r\n      if (!query) {\r\n        this.filteredGameMethods = this.gameMethodsData;\r\n      } else {\r\n        this.filteredGameMethods = this.gameMethodsData.filter(item => {\r\n          return (item.methodName && item.methodName.indexOf(query) !== -1) ||\r\n                 (query === '3' && item.methodName && item.methodName.indexOf('三') !== -1);\r\n        });\r\n      }\r\n    },\r\n    showFormatDialog() {\r\n      this.formatDialogVisible = true;\r\n      this.formatInput = '';\r\n      this.formatOutput = '';\r\n      this.formatTab = 'unified'; // 每次打开都默认显示字符转换\r\n    },\r\n    /** 显示统计对话框 */\r\n    showStatistics() {\r\n      this.statisticsVisible = true;\r\n    },\r\n    /** 验证金额 */\r\n    validateMoney() {\r\n      for (let i = 0; i < this.formData.betGroups.length; i++) {\r\n        const group = this.formData.betGroups[i];\r\n        const money = group.money;\r\n        const groupNumber = i + 1; // 下注单号从1开始\r\n\r\n        // 确保money是字符串类型，防止trim()方法报错\r\n        const moneyStr = money != null ? String(money) : '';\r\n\r\n        // 检查是否为空\r\n        if (!moneyStr || moneyStr.trim() === '') {\r\n          return {\r\n            valid: false,\r\n            message: `第<span style=\"color: #ff4757; font-weight: bold; font-size: 16px;\">${groupNumber}</span>组下注金额不能为<span style=\"color: #ff4757; font-weight: bold; font-size: 16px;\">空</span>`\r\n          };\r\n        }\r\n\r\n        // 检查是否为数字\r\n        const numMoney = Number(moneyStr.trim());\r\n        if (isNaN(numMoney) || numMoney <= 0) {\r\n          return {\r\n            valid: false,\r\n            message: `第<span style=\"color: #ff4757; font-weight: bold; font-size: 16px;\">${groupNumber}</span>组下注金额不能为<span style=\"color: #ff4757; font-weight: bold; font-size: 16px;\">\"${moneyStr}\"</span>，请修改`\r\n          };\r\n        }\r\n      }\r\n\r\n      return {\r\n        valid: true,\r\n        message: ''\r\n      };\r\n    },\r\n    /** 检查统计权限 */\r\n    async checkStatisticsPermission() {\r\n      try {\r\n        // 调用专门的统计权限API\r\n        const response = await getStatisticsPermission();\r\n\r\n\r\n        if (response && response.code === 200 && response.data !== undefined) {\r\n\r\n          this.hasStatisticsPermission = response.data === '1';\r\n        } else {\r\n \r\n          this.hasStatisticsPermission = false;\r\n        }\r\n\r\n  \r\n      } catch (error) {\r\n        console.error('获取用户权限失败:', error);\r\n        this.hasStatisticsPermission = false;\r\n      }\r\n    },\r\n    handleFormatInput() {\r\n      // 句号tab：所有句号类字符转为-\r\n      let out = (this.formatInput || '').replace(/[。．.]/g, '-');\r\n      // 连续多个短横合并为一个\r\n      out = out.replace(/-+/g, '-');\r\n      // 先替换所有汉字数字为阿拉伯数字\r\n      let lines = out.split('\\n').map(line => this.replaceChineseNumber(line));\r\n      lines = this.autoConvertChineseMoney(lines);\r\n      this.formatOutput = lines.filter(line => line).join('\\n');\r\n    },\r\n    insertFormatResult() {\r\n      this.formData.shibie = this.formatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.formatOutput);\r\n    },\r\n    handleColonFormatInput() {\r\n      // 冒号tab：所有冒号类字符转为-\r\n      let out = (this.colonFormatInput || '').replace(/[：:]/g, '-');\r\n      // 连续多个短横合并为一个\r\n      out = out.replace(/-+/g, '-');\r\n      // 先替换所有汉字数字为阿拉伯数字\r\n      let lines = out.split('\\n').map(line => this.replaceChineseNumber(line));\r\n      lines = this.autoConvertChineseMoney(lines);\r\n      this.colonFormatOutput = lines.filter(line => line).join('\\n');\r\n    },\r\n    insertColonFormatResult() {\r\n      this.formData.shibie = this.colonFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.colonFormatOutput);\r\n    },\r\n\r\n    handleMoneyFormatInput() {\r\n      // 只做汉字金额转数字，且只替换最后一个汉字金额为/数字\r\n      let lines = (this.moneyFormatInput || '').split('\\n').map(line => this.replaceChineseNumber(line));\r\n      const chineseMoneyReg = /([一二三四五六七八九十百千万两零]+)(元|块|米)?/g;\r\n      lines = lines.map(line => {\r\n        let lastMatch;\r\n        let match;\r\n        while ((match = chineseMoneyReg.exec(line)) !== null) {\r\n          lastMatch = match;\r\n        }\r\n        if (lastMatch) {\r\n          const num = this.chineseToNumber(lastMatch[1]);\r\n          const unit = lastMatch[2] || '';\r\n          const start = lastMatch.index;\r\n          const end = start + lastMatch[0].length;\r\n          let prefix = line.slice(0, start);\r\n          if (!/[\\/\\s,，.。-]+$/.test(prefix)) {\r\n            prefix += '/';\r\n          }\r\n          return prefix + num + unit + line.slice(end);\r\n        }\r\n        return line;\r\n      });\r\n      this.moneyFormatOutput = lines.filter(line => line).join('\\n');\r\n    },\r\n    insertMoneyFormatResult() {\r\n      this.formData.shibie = this.moneyFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.moneyFormatOutput);\r\n    },\r\n    handleUnifiedFormatInput() {\r\n      // 统一字符转换：智能识别并转换各种分隔符和汉字金额\r\n      let out = this.unifiedFormatInput || '';\r\n\r\n      // 1. 先处理汉字金额转换（在其他转换之前）\r\n      let lines = out.split('\\n').map(line => line.trim()).filter(Boolean);\r\n      lines = this.autoConvertChineseMoney(lines);\r\n      out = lines.join('\\n');\r\n\r\n      // 2. 替换剩余的汉字数字为阿拉伯数字（不包括已转换的金额）\r\n      out = this.replaceChineseNumber(out);\r\n\r\n      // 3. 转换各种分隔符为空格（支持一个或多个连续字符）\r\n      // 逗号、句号、冒号、下划线都转为空格\r\n      out = out.replace(/[,，]+/g, ' ');        // 逗号（中英文）\r\n      out = out.replace(/[。．.]+/g, ' ');       // 句号（中英文）\r\n      out = out.replace(/[：:]+/g, ' ');        // 冒号（中英文）\r\n      out = out.replace(/_+/g, ' ');            // 下划线\r\n\r\n      // 4. 处理加号类字符（转换为空格）\r\n      out = out.replace(/[➕＋﹢✚✛✜✙❇️❌❎]+/g, ' ');\r\n\r\n      // 5. 合并多个连续空格为一个\r\n      out = out.replace(/\\s+/g, ' ');\r\n\r\n      this.unifiedFormatOutput = out.split('\\n').map(line => line.trim()).filter(Boolean).join('\\n');\r\n    },\r\n    insertUnifiedFormatResult() {\r\n      this.formData.shibie = this.unifiedFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.unifiedFormatOutput);\r\n    },\r\n    handleChainFormatInput() {\r\n      // 链子分类：按号码长度分组到不同行\r\n      let input = this.chainFormatInput || '';\r\n\r\n      // 1. 先进行基础的字符转换\r\n      input = this.replaceChineseNumber(input);\r\n\r\n      // 2. 提取所有数字（支持各种分隔符）\r\n      // 使用正则匹配所有连续的数字\r\n      const numberPattern = /\\d+/g;\r\n      const numbers = input.match(numberPattern) || [];\r\n\r\n      // 3. 按长度分组，同时记录每个长度首次出现的位置\r\n      const lengthGroups = {};\r\n      const lengthFirstIndex = {};\r\n\r\n      numbers.forEach((num, index) => {\r\n        const length = num.length;\r\n        if (!lengthGroups[length]) {\r\n          lengthGroups[length] = [];\r\n          lengthFirstIndex[length] = index; // 记录该长度首次出现的位置\r\n        }\r\n        lengthGroups[length].push(num);\r\n      });\r\n\r\n      // 4. 按首次出现的顺序排序并生成输出\r\n      const sortedLengths = Object.keys(lengthGroups).sort((a, b) => lengthFirstIndex[a] - lengthFirstIndex[b]);\r\n      const resultLines = [];\r\n\r\n      sortedLengths.forEach(length => {\r\n        const nums = lengthGroups[length];\r\n        if (nums.length > 0) {\r\n          // 每行格式：直接显示号码列表，不加长度标识\r\n          const line = nums.join(' ');\r\n          resultLines.push(line);\r\n        }\r\n      });\r\n\r\n      this.chainFormatOutput = resultLines.join('\\n');\r\n    },\r\n    insertChainFormatResult() {\r\n      this.formData.shibie = this.chainFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.chainFormatOutput);\r\n    },\r\n    handleAmountFormatInput() {\r\n      // 金额整合：将相同金额且相同位数的号码合并到一行\r\n      // 支持每行一个号码，也支持一行内用空格分隔的多个号码\r\n      const input = this.amountFormatInput || '';\r\n      const lines = input.split('\\n').filter(line => line.trim());\r\n\r\n      // 解析每行的号码和金额\r\n      const groups = {};\r\n      const groupOrder = []; // 记录分组首次出现的顺序\r\n\r\n      lines.forEach(line => {\r\n        const trimmedLine = line.trim();\r\n        if (!trimmedLine) return;\r\n\r\n        // 先尝试按空格分割，看是否有多个号码-金额对\r\n        const parts = trimmedLine.split(/\\s+/).filter(part => part.trim());\r\n\r\n        // 如果只有一个部分，按原来的逻辑处理\r\n        if (parts.length === 1) {\r\n          this.processAmountFormatItem(parts[0], groups, groupOrder);\r\n        } else {\r\n          // 如果有多个部分，逐个处理每个号码-金额对\r\n          parts.forEach(part => {\r\n            this.processAmountFormatItem(part, groups, groupOrder);\r\n          });\r\n        }\r\n      });\r\n\r\n      // 按首次出现的顺序生成输出\r\n      const resultLines = [];\r\n      groupOrder.forEach(groupKey => {\r\n        const group = groups[groupKey];\r\n        if (group && group.numbers.length > 0) {\r\n          // 合并相同金额和位数的号码到一行\r\n          const line = group.numbers.join('-') + '-' + group.amount;\r\n          resultLines.push(line);\r\n        }\r\n      });\r\n\r\n      this.amountFormatOutput = resultLines.join('\\n');\r\n    },\r\n\r\n    // 新增辅助函数：处理单个号码-金额项\r\n    processAmountFormatItem(item, groups, groupOrder) {\r\n      const trimmedItem = item.trim();\r\n      if (!trimmedItem) return;\r\n\r\n      // 查找最后一个短横线，分离号码和金额\r\n      const lastDashIndex = trimmedItem.lastIndexOf('-');\r\n      if (lastDashIndex === -1) return; // 没有找到分隔符，跳过\r\n\r\n      const number = trimmedItem.substring(0, lastDashIndex).trim();\r\n      const amount = trimmedItem.substring(lastDashIndex + 1).trim();\r\n\r\n      if (!number || !amount) return; // 号码或金额为空，跳过\r\n\r\n      // 按金额和号码位数分组\r\n      const numberLength = number.length;\r\n      const groupKey = `${amount}_${numberLength}`; // 金额_位数作为分组键\r\n\r\n      if (!groups[groupKey]) {\r\n        groups[groupKey] = {\r\n          amount: amount,\r\n          length: numberLength,\r\n          numbers: []\r\n        };\r\n        groupOrder.push(groupKey); // 记录分组首次出现的顺序\r\n      }\r\n      groups[groupKey].numbers.push(number);\r\n    },\r\n    insertAmountFormatResult() {\r\n      this.formData.shibie = this.amountFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.amountFormatOutput);\r\n    },\r\n    handleRemoveFormatInput() {\r\n      // 去除分隔符和英文字母：只保留汉字、数字和换行\r\n      const input = this.removeFormatInput || '';\r\n\r\n      // 使用正则表达式只保留汉字、数字和换行符\r\n      // \\u4e00-\\u9fff 匹配汉字\r\n      // \\d 匹配数字\r\n      // \\n\\r 匹配换行符\r\n      // 去除所有英文字母（大小写）和其他字符\r\n      const output = input.replace(/[^\\u4e00-\\u9fff\\d\\n\\r]/g, '');\r\n\r\n      // 清理多余的空行，但保留必要的换行\r\n      this.removeFormatOutput = output.split('\\n')\r\n        .map(line => line.trim())\r\n        .filter((line, index, arr) => {\r\n          // 保留非空行，以及前一行非空的空行（作为分隔）\r\n          return line || (index > 0 && arr[index - 1]);\r\n        })\r\n        .join('\\n')\r\n        .replace(/\\n{3,}/g, '\\n\\n'); // 最多保留两个连续换行\r\n    },\r\n    insertRemoveFormatResult() {\r\n      this.formData.shibie = this.removeFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.removeFormatOutput);\r\n    },\r\n    handleUnderscoreFormatInput() {\r\n      // 下划线tab：将一个或多个连续的下划线转换为空格\r\n      let out = (this.underscoreFormatInput || '').replace(/_+/g, ' ');\r\n      // 先替换所有汉字数字为阿拉伯数字\r\n      let lines = out.split('\\n').map(line => this.replaceChineseNumber(line));\r\n      lines = this.autoConvertChineseMoney(lines);\r\n      this.underscoreFormatOutput = lines.filter(line => line).join('\\n');\r\n    },\r\n    insertUnderscoreFormatResult() {\r\n      this.formData.shibie = this.underscoreFormatOutput;\r\n      this.formatDialogVisible = false;\r\n      this.handleNumberRecognition(this.underscoreFormatOutput);\r\n    },\r\n    // 工具函数：替换所有汉字数字为阿拉伯数字\r\n    replaceChineseNumber(str) {\r\n      // 先处理完整的中文数字（包括单位），然后再处理单个汉字数字\r\n      // 匹配完整的中文数字表达式，如\"二十五\"、\"一百二十三\"等\r\n      const chineseNumberPattern = /[零一二两三四五六七八九十百千万]+/g;\r\n\r\n      return str.replace(chineseNumberPattern, (match) => {\r\n        // 如果匹配的是单个字符且不包含单位，使用简单映射\r\n        if (match.length === 1 && !/[十百千万]/.test(match)) {\r\n          const map = { '零':0,'一':1,'二':2,'两':2,'三':3,'四':4,'五':5,'六':6,'七':7,'八':8,'九':9 };\r\n          return map[match] || match;\r\n        }\r\n        // 否则使用完整的中文数字转换\r\n        return this.chineseToNumber(match);\r\n      });\r\n    },\r\n    chineseToNumber(chinese) {\r\n      if (!chinese) return 0;\r\n\r\n      // 先判断是否为纯数字\r\n      if (/^\\d+$/.test(chinese)) {\r\n        return parseInt(chinese, 10);\r\n      }\r\n\r\n      // 移除单位\r\n      const str = chinese.replace(/元|块|米/g, '');\r\n\r\n      // 数字映射\r\n      const numMap = {\r\n        '零': 0, '一': 1, '二': 2, '两': 2, '三': 3, '四': 4,\r\n        '五': 5, '六': 6, '七': 7, '八': 8, '九': 9\r\n      };\r\n\r\n      // 单位映射\r\n      const unitMap = { '十': 10, '百': 100, '千': 1000, '万': 10000 };\r\n\r\n      let result = 0;\r\n      let temp = 0;\r\n      let hasNum = false;\r\n\r\n      for (let i = 0; i < str.length; i++) {\r\n        const char = str[i];\r\n\r\n        if (numMap.hasOwnProperty(char)) {\r\n          // 是数字\r\n          temp = numMap[char];\r\n          hasNum = true;\r\n        } else if (unitMap.hasOwnProperty(char)) {\r\n          // 是单位\r\n          const unit = unitMap[char];\r\n\r\n          if (char === '十' && !hasNum) {\r\n            // \"十\"前面没有数字，如\"十五\"，十前面默认为1\r\n            temp = 1;\r\n          }\r\n\r\n          if (unit === 10000) {\r\n            // 万\r\n            result = (result + temp) * unit;\r\n            temp = 0;\r\n          } else if (unit >= 100) {\r\n            // 百、千\r\n            result += temp * unit;\r\n            temp = 0;\r\n          } else {\r\n            // 十\r\n            result += temp * unit;\r\n            temp = 0;\r\n          }\r\n          hasNum = false;\r\n        }\r\n      }\r\n\r\n      // 处理最后剩余的数字\r\n      result += temp;\r\n\r\n      return result;\r\n    },\r\n    autoConvertChineseMoney(lines) {\r\n      if (typeof lines === 'string') lines = lines.split(/\\n/);\r\n      // 更精确的中文金额正则，支持复杂的中文数字\r\n      const chineseMoneyReg = /([零一二三四五六七八九十百千万两]+)(元|块|米)?/g;\r\n      return lines.map(line => {\r\n        // 查找所有汉字金额\r\n        let lastMatch;\r\n        let match;\r\n        // 重置正则的lastIndex\r\n        chineseMoneyReg.lastIndex = 0;\r\n        while ((match = chineseMoneyReg.exec(line)) !== null) {\r\n          // 验证匹配的是否真的是数字（包含数字字符或单位字符）\r\n          if (/[一二三四五六七八九十百千万两零]/.test(match[1])) {\r\n            lastMatch = match;\r\n          }\r\n        }\r\n        if (lastMatch) {\r\n          const num = this.chineseToNumber(lastMatch[1]);\r\n          const unit = lastMatch[2] || '';\r\n          // 替换最后一个汉字金额为数字\r\n          const start = lastMatch.index;\r\n          const end = start + lastMatch[0].length;\r\n          let prefix = line.slice(0, start);\r\n\r\n          // 确保汉字金额前面有空格，避免和号码混淆\r\n          if (prefix && !/[\\s\\/,，.。-]$/.test(prefix)) {\r\n            prefix += ' ';\r\n          }\r\n\r\n          return prefix + num + unit + line.slice(end);\r\n        }\r\n        return line;\r\n      });\r\n    },\r\n    // 初始化本组序号\r\n    initSerialNumber() {\r\n      // 如果是修改模式（有row数据）且已有流水号，不要重新初始化\r\n      if (this.row && this.row.serialNumber) {\r\n\r\n        this.serialNumber = this.row.serialNumber;\r\n        this.originalSerialNumber = this.row.serialNumber;\r\n        // 仍然获取最大流水号用于生成新流水号时参考\r\n        getMaxSerialNumber().then(max => {\r\n          this.maxSerialNumber = max ? Number(max) : 1;\r\n        }).catch(() => {\r\n          this.maxSerialNumber = 1;\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 新增模式或没有流水号时，获取最大流水号\r\n      \r\n      getMaxSerialNumber().then(max => {\r\n        if (max == null) {\r\n          this.maxSerialNumber = 1;\r\n          this.serialNumber = 1;\r\n        } else {\r\n          this.maxSerialNumber = Number(max);\r\n          this.serialNumber = Number(max);\r\n        }\r\n        \r\n      }).catch(() => {\r\n        this.maxSerialNumber = 1;\r\n        this.serialNumber = 1;\r\n      });\r\n    },\r\n    // 增加本组序号，只能加到最大+1\r\n    increaseSerialNumber() {\r\n      const next = Number(this.maxSerialNumber) + 1;\r\n      if (this.serialNumber < next) {\r\n        this.serialNumber = next;\r\n      }\r\n      // 如果已经是最大+1，则不再增加\r\n    },\r\n    // 生成新的流水号\r\n    generateNewSerialNumber() {\r\n      this.generateLoading = true;\r\n      generateSerialNumber().then(response => {\r\n        if (response.code === 200) {\r\n          this.serialNumber = response.serialNumber;\r\n          this.maxSerialNumber = response.serialNumber;\r\n          this.$message.success(`生成新流水号: ${response.serialNumber}`);\r\n        } else {\r\n          this.$message.error('生成流水号失败: ' + response.msg);\r\n        }\r\n      }).catch(error => {\r\n        console.error('生成流水号失败:', error);\r\n        this.$message.error('生成流水号失败');\r\n      }).finally(() => {\r\n        this.generateLoading = false;\r\n      });\r\n    },\r\n    // 处理用户ID变化\r\n    handleUserIdChange(newUserId, oldUserId) {\r\n    \r\n      if (newUserId === this.originalUserId) {\r\n        // 改回原用户，恢复原流水号\r\n        this.serialNumber = this.originalSerialNumber;\r\n        this.$message.info(`已恢复原流水号: ${this.originalSerialNumber}`);\r\n      } else {\r\n        // 改为其他用户，自动生成新流水号\r\n        this.autoGenerateSerialNumber();\r\n      }\r\n    },\r\n    // 自动生成流水号（不显示成功消息）\r\n    autoGenerateSerialNumber() {\r\n      generateSerialNumber().then(response => {\r\n        if (response.code === 200) {\r\n          this.serialNumber = response.serialNumber;\r\n          this.maxSerialNumber = response.serialNumber;\r\n          this.$message.info(`用户变更，自动生成新流水号: ${response.serialNumber}`);\r\n        } else {\r\n          this.$message.error('自动生成流水号失败: ' + response.msg);\r\n        }\r\n      }).catch(error => {\r\n        console.error('自动生成流水号失败:', error);\r\n        this.$message.error('自动生成流水号失败');\r\n      });\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 使用全局样式 */\r\n.bet-dialog-loading {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n.bet-dialog-loading .el-loading-mask {\r\n  z-index: 9999 !important;\r\n}\r\n</style>\r\n\r\n<style scoped>\r\n/* 现代化弹窗样式 */\r\n.modern-bet-dialog {\r\n  .el-dialog {\r\n    border-radius: 12px;\r\n    overflow: hidden;\r\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);\r\n  }\r\n\r\n  .el-dialog__header {\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    padding: 20px 24px;\r\n  }\r\n\r\n  .el-dialog__title {\r\n    color: white;\r\n    font-weight: 600;\r\n    font-size: 18px;\r\n  }\r\n\r\n  .el-dialog__headerbtn .el-dialog__close {\r\n    color: white;\r\n    font-size: 20px;\r\n\r\n    &:hover {\r\n      color: rgba(255, 255, 255, 0.8);\r\n    }\r\n  }\r\n}\r\n\r\n/* 福体开关样式 */\r\n.lottery-switch {\r\n  font-weight: 500;\r\n  margin-left: 8px;\r\n}\r\n\r\n/* 表单样式 */\r\n.modern-bet-form {\r\n  .el-form-item__label {\r\n    font-weight: 500;\r\n    color: #495057;\r\n  }\r\n}\r\n\r\n/* 合并的信息卡片样式 */\r\n.info-card {\r\n  background: white;\r\n  padding: 8px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e9ecef;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.info-row {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-bottom: 6px;\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.info-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.info-label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  font-weight: 500;\r\n  color: #495057;\r\n  font-size: 13px;\r\n  min-width: 60px;\r\n\r\n  i {\r\n    color: #667eea;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n.compact-input {\r\n  .el-input__inner {\r\n    height: 28px;\r\n    font-size: 12px;\r\n    border-radius: 4px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:focus {\r\n      border-color: #667eea;\r\n      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);\r\n    }\r\n  }\r\n}\r\n\r\n.compact-select {\r\n  .el-input__inner {\r\n    height: 28px;\r\n    font-size: 12px;\r\n    border-radius: 4px;\r\n  }\r\n}\r\n\r\n.compact-btn {\r\n  height: 28px;\r\n  padding: 0 8px;\r\n  font-size: 12px;\r\n  border-radius: 4px;\r\n\r\n  i {\r\n    margin-right: 2px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n/* 识别区域样式 */\r\n.recognition-section {\r\n  background: white;\r\n  padding: 6px;\r\n  border-radius: 6px;\r\n  border: 1px solid #e9ecef;\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.recognition-wrapper {\r\n  .el-form-item__content {\r\n    position: relative;\r\n  }\r\n}\r\n\r\n.recognition-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  margin-bottom: 6px;\r\n  font-size: 12px;\r\n  color: #495057;\r\n\r\n  i {\r\n    color: #667eea;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .el-tag {\r\n    margin-left: auto;\r\n  }\r\n\r\n  .el-button {\r\n    margin-left: auto;\r\n  }\r\n}\r\n\r\n.recognition-textarea {\r\n  .el-textarea__inner {\r\n    border-radius: 6px;\r\n    border-color: #e9ecef;\r\n    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\r\n    font-size: 12px;\r\n    transition: all 0.3s ease;\r\n\r\n    &:focus {\r\n      border-color: #667eea;\r\n      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);\r\n    }\r\n  }\r\n}\r\n\r\n.option-group {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  padding: 6px 10px;\r\n  background: rgba(248, 249, 250, 0.8);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(233, 236, 239, 0.8);\r\n  backdrop-filter: blur(5px);\r\n  transition: all 0.3s ease;\r\n  min-height: 36px;\r\n\r\n  &:hover {\r\n    background: rgba(248, 249, 250, 1);\r\n    border-color: rgba(102, 126, 234, 0.3);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n  }\r\n}\r\n\r\n.option-label {\r\n  min-width: 55px;\r\n  margin-right: 8px;\r\n  font-weight: 600;\r\n  color: #495057;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  flex-shrink: 0;\r\n\r\n  &::before {\r\n    content: '●';\r\n    color: #667eea;\r\n    font-size: 10px;\r\n    animation: pulse 2s infinite;\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    opacity: 0.6;\r\n  }\r\n}\r\n\r\n/* 投注组总额和投注数美化 */\r\n.group-summary {\r\n  display: flex;\r\n  gap: 16px;\r\n  margin-left: auto;\r\n}\r\n\r\n.summary-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  padding: 4px 8px;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n\r\n  &.amount {\r\n    background: rgba(102, 126, 234, 0.1);\r\n    border: 1px solid rgba(102, 126, 234, 0.2);\r\n\r\n    .summary-icon {\r\n      color: #667eea;\r\n    }\r\n\r\n    .summary-value {\r\n      color: #667eea;\r\n      font-size: 15px;\r\n    }\r\n  }\r\n\r\n  &.bets {\r\n    background: rgba(255, 107, 53, 0.1);\r\n    border: 1px solid rgba(255, 107, 53, 0.2);\r\n\r\n    .summary-icon {\r\n      color: #ff6b35;\r\n    }\r\n\r\n    .summary-value {\r\n      color: #ff6b35;\r\n    }\r\n  }\r\n\r\n  &:hover {\r\n    transform: translateY(-1px);\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  }\r\n}\r\n\r\n.summary-icon {\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.summary-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n}\r\n\r\n.summary-label {\r\n  font-size: 10px;\r\n  color: #666;\r\n  line-height: 1;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.summary-value {\r\n  font-size: 12px;\r\n  font-weight: 700;\r\n  line-height: 1;\r\n}\r\n\r\n.el-radio-group {\r\n  display: inline-flex;\r\n  gap: 6px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.el-radio-button {\r\n  margin-right: 0;\r\n\r\n  .el-radio-button__inner {\r\n    border-radius: 6px;\r\n    font-weight: 600;\r\n    transition: all 0.3s ease;\r\n    border-color: #e9ecef;\r\n    padding: 6px 8px;\r\n    font-size: 11px;\r\n    min-width: 65px;\r\n    text-align: center;\r\n\r\n    &:hover {\r\n      border-color: #667eea;\r\n      color: #667eea;\r\n    }\r\n  }\r\n\r\n  /* 福彩3D按钮样式 */\r\n  &[data-lottery=\"fc3d\"] {\r\n    .el-radio-button__inner {\r\n      border-color: #ff6b35;\r\n      color: #ff6b35;\r\n\r\n      &:hover {\r\n        border-color: #ff6b35;\r\n        background: rgba(255, 107, 53, 0.1);\r\n      }\r\n    }\r\n\r\n    &.is-active {\r\n      .el-radio-button__inner {\r\n        background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);\r\n        border-color: #ff6b35;\r\n        color: white;\r\n        box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);\r\n      }\r\n    }\r\n  }\r\n\r\n  /* 体彩排三按钮样式 */\r\n  &[data-lottery=\"tc\"] {\r\n    .el-radio-button__inner {\r\n      border-color: #409eff;\r\n      color: #409eff;\r\n      min-width: 70px;\r\n\r\n      &:hover {\r\n        border-color: #409eff;\r\n        background: rgba(64, 158, 255, 0.1);\r\n      }\r\n    }\r\n\r\n    &.is-active {\r\n      .el-radio-button__inner {\r\n        background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);\r\n        border-color: #409eff;\r\n        color: white;\r\n        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.bet-groups-list-wrapper {\r\n  position: relative;\r\n  margin-bottom: 8px;\r\n  margin-top: 0;\r\n}\r\n\r\n.bet-groups-list {\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  padding: 0 4px;\r\n}\r\n\r\n.bet-group-card {\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  box-shadow: 0 3px 12px 0 rgba(0, 0, 0, 0.08);\r\n  margin-bottom: 8px;\r\n  padding: 10px 14px;\r\n  border: 2px solid #e9ecef;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 6px;\r\n    height: 100%;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  }\r\n\r\n  &::after {\r\n   \r\n    position: absolute;\r\n    top: 8px;\r\n    right: 12px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    padding: 4px 10px;\r\n    border-radius: 15px;\r\n    font-size: 11px;\r\n    font-weight: 700;\r\n    z-index: 10;\r\n    box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);\r\n  }\r\n}\r\n\r\n.bet-group-card:hover {\r\n  box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.15);\r\n  transform: translateY(-2px);\r\n  border-color: #667eea;\r\n}\r\n\r\n.bet-group-even {\r\n  background: linear-gradient(135deg, #fff8f5 0%, #fef5f0 100%) !important;\r\n  border-color: #ffb8a1 !important;\r\n\r\n  &::before {\r\n    background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);\r\n  }\r\n\r\n  &::after {\r\n    \r\n    background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);\r\n    box-shadow: 0 2px 6px rgba(255, 107, 53, 0.3);\r\n  }\r\n\r\n  &:hover {\r\n    box-shadow: 0 6px 20px 0 rgba(255, 107, 53, 0.2);\r\n    border-color: #ff6b35;\r\n  }\r\n}\r\n\r\n.bet-group {\r\n  background: #f5f7fa;\r\n  border-radius: 4px;\r\n  padding: 15px;\r\n  margin-bottom: 15px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.bet-group:hover {\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);\r\n}\r\n\r\n.bet-group-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #dcdfe6;\r\n}\r\n\r\n.bet-group-content {\r\n  margin-left: 10px;\r\n}\r\n\r\n.bet-group-footer {\r\n  margin-top: 10px;\r\n  padding-top: 10px;\r\n  border-top: 1px solid #dcdfe6;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.el-form-item--small.el-form-item {\r\n  margin-bottom: 3px;\r\n}\r\n\r\n.modern-bet-form .el-form-item {\r\n  margin-bottom: 6px;\r\n}\r\n\r\n.el-textarea__inner {\r\n  font-family: monospace;\r\n}\r\n\r\n.el-input-number--small {\r\n  width: 130px;\r\n}\r\n\r\n.total-info {\r\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);\r\n  padding: 6px 12px;\r\n  border-radius: 6px;\r\n  box-shadow: 0 1px 6px 0 rgba(102, 126, 234, 0.1);\r\n  margin-top: 4px;\r\n  border: 1px solid rgba(102, 126, 234, 0.2);\r\n  position: relative;\r\n  font-size: 13px;\r\n\r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    height: 2px;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    border-radius: 6px 6px 0 0;\r\n  }\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center;\r\n  \r\n  /* background: #f8f9fa; */\r\n  border-radius: 0 0 6px 6px;\r\n  margin: 4px -6px -6px -6px;\r\n}\r\n\r\n.dialog-footer .el-button {\r\n  min-width: 80px;\r\n  margin: 0 6px;\r\n  border-radius: 6px;\r\n  font-weight: 500;\r\n  font-size: 13px;\r\n  height: 32px;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-1px);\r\n  }\r\n\r\n  &.el-button--primary {\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    border: none;\r\n\r\n    &:hover {\r\n      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\r\n    }\r\n  }\r\n}\r\n\r\n:deep(.bet-dialog-loading) {\r\n  z-index: 3000 !important;\r\n}\r\n\r\n.bet-group-add-btn {\r\n  position: absolute;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  bottom: -12px;\r\n  width: 28px;\r\n  height: 28px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: white;\r\n  border: 2px solid white;\r\n  box-shadow: 0 2px 8px 0 rgba(102, 126, 234, 0.3);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  z-index: 20;\r\n  transition: all 0.3s ease;\r\n\r\n  &::before {\r\n    content: '+';\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.bet-group-add-btn:hover {\r\n  background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);\r\n  transform: translateX(-50%) translateY(-1px) scale(1.05);\r\n  box-shadow: 0 4px 12px 0 rgba(102, 126, 234, 0.4);\r\n}\r\n.el-dialog__body{\r\n  margin-top: -30px;\r\n}\r\n\r\n/* 简洁格式转换弹窗样式 */\r\n.simple-format-dialog {\r\n  .el-textarea__inner {\r\n    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\r\n    font-size: 13px;\r\n  }\r\n\r\n  .el-form-item {\r\n    margin-bottom: 16px;\r\n  }\r\n\r\n  .el-button {\r\n    margin-right: 8px;\r\n  }\r\n}\r\n\r\n/* 号码数量超限样式 */\r\n.number-count-exceeded .el-textarea__inner {\r\n  border-color: #f56c6c !important;\r\n  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;\r\n}\r\n\r\n.number-count-exceeded .el-textarea__inner:focus {\r\n  border-color: #f56c6c !important;\r\n  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.3) !important;\r\n}\r\n\r\n/* 格式转换弹窗美化样式 */\r\n.format-dialog {\r\n  .el-dialog__header {\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: white;\r\n    padding: 20px 24px;\r\n    border-radius: 8px 8px 0 0;\r\n\r\n    .el-dialog__title {\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      color: white;\r\n    }\r\n\r\n    .el-dialog__close {\r\n      color: white;\r\n      font-size: 20px;\r\n\r\n      &:hover {\r\n        color: #f0f0f0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    background: #f8f9fa;\r\n  }\r\n}\r\n\r\n.format-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.format-tabs {\r\n  .el-tabs__header {\r\n    margin: 0 0 20px 0;\r\n    background: white;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    padding: 12px;\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n\r\n  .el-tabs__nav {\r\n    border: none;\r\n    display: flex;\r\n    justify-content: center;\r\n    width: 100%;\r\n  }\r\n\r\n  .el-tabs__item {\r\n    border: none;\r\n    border-radius: 6px;\r\n    margin-right: 8px;\r\n    padding: 12px 24px;\r\n    font-weight: 500;\r\n    transition: all 0.3s ease;\r\n    min-width: 120px;\r\n    text-align: center;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 44px;\r\n\r\n    &:hover {\r\n      background: #e3f2fd;\r\n      color: #1976d2;\r\n    }\r\n\r\n    &.is-active {\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      color: white;\r\n      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n    }\r\n\r\n    i {\r\n      margin-right: 6px;\r\n      font-size: 16px;\r\n      display: inline-block;\r\n      vertical-align: middle;\r\n    }\r\n\r\n    span {\r\n      display: inline-block;\r\n      vertical-align: middle;\r\n    }\r\n  }\r\n\r\n  .el-tabs__content {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.tab-content {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  border: 1px solid #e8eaed;\r\n}\r\n\r\n.input-section, .output-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-weight: 600;\r\n  color: #333;\r\n  font-size: 14px;\r\n\r\n  i {\r\n    margin-right: 8px;\r\n    font-size: 16px;\r\n    color: #667eea;\r\n  }\r\n}\r\n\r\n.arrow-section {\r\n  text-align: center;\r\n  margin: 16px 0;\r\n\r\n  i {\r\n    font-size: 24px;\r\n    color: #667eea;\r\n    animation: bounce 2s infinite;\r\n  }\r\n}\r\n\r\n@keyframes bounce {\r\n  0%, 20%, 50%, 80%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  40% {\r\n    transform: translateY(-8px);\r\n  }\r\n  60% {\r\n    transform: translateY(-4px);\r\n  }\r\n}\r\n\r\n.format-input, .format-output {\r\n  .el-textarea__inner {\r\n    border-radius: 8px;\r\n    border: 2px solid #e8eaed;\r\n    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\r\n    font-size: 13px;\r\n    line-height: 1.6;\r\n    transition: all 0.3s ease;\r\n\r\n    &:focus {\r\n      border-color: #667eea;\r\n      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\r\n    }\r\n\r\n    &::placeholder {\r\n      color: #999;\r\n      font-style: italic;\r\n    }\r\n  }\r\n}\r\n\r\n.format-output {\r\n  .el-textarea__inner {\r\n    background: #f8f9fa;\r\n    color: #2d3748;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.button-section {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 12px;\r\n  margin-top: 24px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #e8eaed;\r\n}\r\n\r\n.action-button {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 8px;\r\n  padding: 12px 24px;\r\n  font-weight: 600;\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);\r\n  }\r\n\r\n  &:disabled {\r\n    background: #d1d5db;\r\n    box-shadow: none;\r\n    transform: none;\r\n  }\r\n}\r\n\r\n.cancel-button {\r\n  border: 2px solid #e8eaed;\r\n  border-radius: 8px;\r\n  padding: 12px 24px;\r\n  font-weight: 500;\r\n  background: white;\r\n  color: #6b7280;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    border-color: #667eea;\r\n    color: #667eea;\r\n    background: #f8f9ff;\r\n  }\r\n}\r\n</style>"]}]}