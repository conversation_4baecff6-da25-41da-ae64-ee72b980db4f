{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetDialog.vue?vue&type=template&id=3e7a394a&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\src\\views\\game\\record\\components\\BetDialog.vue", "mtime": 1756432531741}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750942930085}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750942926786}, {"path": "C:\\Users\\<USER>\\Desktop\\duo3d\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750942928768}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}